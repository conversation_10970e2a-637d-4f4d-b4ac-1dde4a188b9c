<?php return array(
    'root' => array(
        'pretty_version' => '8.3.0',
        'version' => '8.3.0.0',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => 'e128371a338723dabd853ee6f44cedee0e6538c1',
        'name' => 'vtiger/vtigercrm',
        'dev' => true,
    ),
    'versions' => array(
        'dg/rss-php' => array(
            'pretty_version' => 'v1.5',
            'version' => '1.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dg/rss-php',
            'aliases' => array(),
            'reference' => '18f00ab1828948a8cfe107729ca1f11c20129b47',
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.17.0',
            'version' => '4.17.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'reference' => 'bbc513d79acf6691fa9cf10f192c90dd2957f18c',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.2',
            'version' => '7.9.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'reference' => 'd281ed313b989f213357e3be1a179f02196ac99b',
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'reference' => '6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8',
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'reference' => 'a70f5c95fb43bc83f07c9c948baa0dc1829bf201',
            'dev_requirement' => false,
        ),
        'league/oauth2-client' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/oauth2-client',
            'aliases' => array(),
            'reference' => '160d6274b03562ebeb55ed18399281d8118b76c8',
            'dev_requirement' => false,
        ),
        'league/oauth2-google' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/oauth2-google',
            'aliases' => array(),
            'reference' => '1b01ba18ba31b29e88771e3e0979e5c91d4afe76',
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.7.0',
            'version' => '3.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'reference' => 'f4393b648b78a5408747de94fca38beb5f7e9ef8',
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'dev_requirement' => false,
        ),
        'phpmailer/phpmailer' => array(
            'pretty_version' => 'v6.9.1',
            'version' => '6.9.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmailer/phpmailer',
            'aliases' => array(),
            'reference' => '039de174cd9c17a8389754d3b877a2ed22743e18',
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'smarty/smarty' => array(
            'pretty_version' => 'v4.5.4',
            'version' => '4.5.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../smarty/smarty',
            'aliases' => array(),
            'reference' => 'c11676e85aa71bc7c3cd9100f1655a9f4d14616e',
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'reference' => '0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1',
            'dev_requirement' => false,
        ),
        'tecnickcom/tcpdf' => array(
            'pretty_version' => '6.7.5',
            'version' => '6.7.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tecnickcom/tcpdf',
            'aliases' => array(),
            'reference' => '951eabf0338ec2522bd0d5d9c79b08a3a3d36b36',
            'dev_requirement' => false,
        ),
        'vtiger/vtigercrm' => array(
            'pretty_version' => '8.3.0',
            'version' => '8.3.0.0',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => 'e128371a338723dabd853ee6f44cedee0e6538c1',
            'dev_requirement' => false,
        ),
    ),
);
