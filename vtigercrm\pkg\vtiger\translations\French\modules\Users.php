<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
    'LBL_ADD_RECORD'               => 'Ajouter un Utilisateur'                    , 
    'LBL_MY_PREFERENCES'           => 'Mes préférences'           , 
    'LBL_MORE_INFORMATION'         => "Plus d'informations", 
    'LBL_USERLOGIN_ROLE'           => 'Informations'                , 
    'LBL_USER_IMAGE_INFORMATION'   => 'Ma photo'                    , 
    'LBL_CURRENCY_CONFIGURATION'   => 'Configuration des devises et monnaies', 
    'LBL_ADDRESS_INFORMATION'      => 'Information sur l\'adresse'         , 
    'LBL_USER_ADV_OPTIONS'         => 'Utilisateur Options avancées', 
    'Asterisk Configuration'       => 'Asterisk Configuration'      , 
    'LBL_HOME_PAGE_COMPONENTS'     => 'Composition de ma page'      , 
    'LBL_TAG_CLOUD_DISPLAY'        => 'Visibilité des Tag Cloud'           , // TODO: Review
    'Role'                         => 'Rôle'                       , 
    'Admin'                        => 'Admin'                       , 
    'User Name'                    => 'Nom d\'Utilisateur'             , 
    'Default Activity View'        => 'Par défaut, Voir mon Calendrier',
    'Default Calendar View'        => 'Vue de Calendrier par Defaut',
    'Default Lead View'            => 'Vue des Prospect par Defaut'    , 
    'Title'                        => 'Fonction'                    , 
    'Office Phone'                 => 'Téléphone de bureau'        , 
    'Department'                   => 'Département'                , 
    'Reports To'                   => 'Supérieur Hiérachique'   , 
    'Yahoo id'                     => 'Yahoo ID'                    , 
    'Home Phone'                   => 'Téléphone personnel'      , 
    'User Image'                   => 'Image/Photo'                 , 
    'Date Format'                  => 'Format des dates'            , 
    'Tag Cloud'                    => 'Tag Cloud'                  , 
    'Signature'                    => 'Signature'                   , 
    'Street Address'               => 'Adresse'                     , 
    'Password'                     => 'Mot de passe'                , 
    'Confirm Password'             => 'Confirmation'                , 
    'LBL_SHOWN'                    => 'Afficher'                    , 
    'LBL_HIDDEN'                   => 'Masquer'                     , 
    'LBL_SHOW'                     => 'Afficher'                    , 
    'LBL_HIDE'                     => 'Masquer'                     , 
    'LBL_HOME_PAGE_COMPO'          => 'Composition de ma page'      , 
    'LBL_LOGIN_HISTORY'            => 'Historique connexion'        , 
    'LBL_USERDETAIL_INFO'          => "Affichage des détails de l'utilisateur", 
    'LBL_DELETE_GROUP'             => 'Supprimer le groupe'           , 
    'LBL_DELETE_GROUPNAME'         => 'Groupe à supprimer'         , 
    'LBL_TRANSFER_GROUP'           => 'De transférer la propriété a:', 
    'LBL_DELETE_USER'              => 'Utilisateur à supprimer' , 
    'LBL_TRANSFER_USER'            => "De transférer la propriété de l'utilisateur", 
    'LBL_DELETE_PROFILE'           => 'Supprimer profil'            , 
    'LBL_TRANSFER_ROLES_TO_PROFILE' => 'Transférer des rôles au profil'         , 
    'LBL_PROFILE_TO_BE_DELETED'    => 'Le Profil va être supprimé', 
    'INTERNAL_MAIL_COMPOSER'       => 'Composeur interne d\'email'  , 
    'Asterisk Extension'           => 'Extension Asterisk'          , 
    'Receive Incoming Calls'      => 'Recevoir les appels Entrants'      , 
    'Reminder Interval'            => 'Intervalle rappel'            , 
    'Webservice Access Key'        => "Clé d'accès WebService", 
    'Language'                     => 'Langue :'                    , 
    'Theme'                        => 'Thème'            , 
    'Time Zone'                    => 'Fuseau horaire'              , 
    'Decimal Separator'            => 'Séparateur décimal'   , 
    'Digit Grouping Pattern'       => 'Patterne de groupe'          , 
    'Digit Grouping Separator'     => 'Séparateur de Groupement des chiffres'       , 
    'Symbol Placement'             => 'Placement du symbole', 
    'Number Of Currency Decimals'  => 'Nombre de Chiffres après la virgule' , 
    'Truncate Trailing Zeros'      => 'Tronquer les zéros de fin'     , 
    'Default Call Duration'        => 'Default Call Duration (Mins)', // TODO: Review
    'Other Event Duration'         => 'Other Event Duration (Mins)' , // TODO: Review
    'Calendar Hour Format'         => 'Format de l\'heure du Calendrier'        , // TODO: Review
    'Kwajalein'                    => '(UTC-12:00) International Date Line West', 
    'Pacific/Midway'               => '(UTC-11:00) Coordinated Universal Time-11', 
    'Pacific/Samoa'                => '(UTC-11:00) Samoa'           , 
    'Pacific/Honolulu'             => '(UTC-10:00) Hawaii'          , 
    'America/Anchorage'            => '(UTC-09:00) Alaska'          , 
    'America/Los_Angeles'          => '(UTC-08:00) Pacific Time (US &amp; Canada)', 
    'America/Tijuana'              => '(UTC-08:00) Tijuana, Baja California', 
    'America/Denver'               => '(UTC-07:00) Mountain Time (US &amp; Canada)', 
    'America/Chihuahua'            => '(UTC-07:00) Chihuahua, La Paz, Mazatlan', 
    'America/Mazatlan'             => '(UTC-07:00) Mazatlan'        , 
    'America/Phoenix'              => '(UTC-07:00) Arizona'         , 
    'America/Regina'               => '(UTC-06:00) Saskatchewan'    , 
    'America/Tegucigalpa'          => '(UTC-06:00) Central America' , 
    'America/Chicago'              => '(UTC-06:00) Central Time (US &amp; Canada)', 
    'America/Mexico_City'          => '(UTC-06:00) Mexico City'     , 
    'America/Monterrey'            => '(UTC-06:00) Monterrey'       , 
    'America/New_York'             => '(UTC-05:00) Eastern Time (US &amp; Canada)', 
    'America/Bogota'               => '(UTC-05:00) Bogota, Lima, Quito', 
    'America/Lima'                 => '(UTC-05:00) Lima'            , 
    'America/Rio_Branco'           => '(UTC-05:00) Rio Branco'      , 
    'America/Indiana/Indianapolis' => '(UTC-05:00) Indiana (East)'  , 
    'America/Caracas'              => '(UTC-04:30) Caracas'         , 
    'America/Halifax'              => '(UTC-04:00) Atlantic Time (Canada)', 
    'America/Manaus'               => '(UTC-04:00) Manaus'          , 
    'America/Santiago'             => '(UTC-04:00) Santiago'        , 
    'America/La_Paz'               => '(UTC-04:00) La Paz'          , 
    'America/Cuiaba'               => '(UTC-04:00) Cuiaba'          , 
    'America/Asuncion'             => '(UTC-04:00) Asuncion'        , 
    'America/St_Johns'             => '(UTC-03:30) Newfoundland'    , 
    'America/Argentina/Buenos_Aires' => '(UTC-03:00) Buenos Aires'    , 
    'America/Sao_Paulo'            => '(UTC-03:00) Brasilia'        , 
    'America/Godthab'              => '(UTC-03:00) Greenland'       , 
    'America/Montevideo'           => '(UTC-03:00) Montevideo'      , 
    'Atlantic/South_Georgia'       => '(UTC-02:00) Mid-Atlantic'    , 
    'Atlantic/Azores'              => '(UTC-01:00) Azores'          , 
    'Atlantic/Cape_Verde'          => '(UTC-01:00) Cape Verde Is.'  , 
    'Europe/London'                => '(UTC) London, Edinburgh, Dublin, Lisbon', 
    'UTC'                          => '(UTC) Coordinated Universal Time, Greenwich Mean Time', 
    'Africa/Monrovia'              => '(UTC) Monrovia, Reykjavik'   , 
    'Africa/Casablanca'            => '(UTC) Casablanca'            , 
    'Europe/Belgrade'              => '(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague', 
    'Europe/Sarajevo'              => '(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb', 
    'Europe/Brussels'              => '(UTC+01:00) Brussels, Copenhagen, Madrid, Paris', 
    'Africa/Algiers'               => '(UTC+01:00) West Central Africa', 
    'Europe/Amsterdam'             => '(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna', 
    'Europe/Minsk'                 => '(UTC+02:00) Minsk'           , 
    'Africa/Cairo'                 => '(UTC+02:00) Cairo'           , 
    'Europe/Helsinki'              => '(UTC+02:00) Helsinki, Riga, Sofia, Tallinn, Vilnius', 
    'Europe/Athens'                => '(UTC+02:00) Athens, Bucharest', 
    'Europe/Istanbul'              => '(UTC+02:00) Istanbul'        , 
    'Asia/Jerusalem'               => '(UTC+02:00) Jerusalem'       , 
    'Asia/Amman'                   => '(UTC+02:00) Amman'           , 
    'Asia/Beirut'                  => '(UTC+02:00) Beirut'          , 
    'Africa/Windhoek'              => '(UTC+02:00) Windhoek'        , 
    'Africa/Harare'                => '(UTC+02:00) Harare'          , 
    'Asia/Kuwait'                  => '(UTC+03:00) Kuwait, Riyadh'  , 
    'Asia/Baghdad'                 => '(UTC+03:00) Baghdad'         , 
    'Africa/Nairobi'               => '(UTC+03:00) Nairobi'         , 
    'Asia/Tehran'                  => '(UTC+03:30) Tehran'          , 
    'Asia/Tbilisi'                 => '(UTC+04:00) Tbilisi'         , 
    'Europe/Moscow'                => '(UTC+04:00) Moscow, Volgograd', 
    'Asia/Muscat'                  => '(UTC+04:00) Abu Dhabi, Muscat', 
    'Asia/Baku'                    => '(UTC+04:00) Baku'            , 
    'Asia/Yerevan'                 => '(UTC+04:00) Yerevan'         , 
    'Asia/Karachi'                 => '(UTC+05:00) Islamabad, Karachi', 
    'Asia/Tashkent'                => '(UTC+05:00) Tashkent'        , 
    'Asia/Kolkata'                 => '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi', 
    'Asia/Colombo'                 => '(UTC+05:30) Sri Jayawardenepura', 
    'Asia/Katmandu'                => '(UTC+05:45) Kathmandu'       , 
    'Asia/Dhaka'                   => '(UTC+06:00) Dhaka'           , 
    'Asia/Almaty'                  => '(UTC+06:00) Almaty'          , 
    'Asia/Yekaterinburg'           => '(UTC+06:00) Ekaterinburg'    , 
    'Asia/Rangoon'                 => '(UTC+06:30) Yangon (Rangoon)', 
    'Asia/Novosibirsk'             => '(UTC+07:00) Novosibirsk'     , 
    'Asia/Bangkok'                 => '(UTC+07:00) Bangkok, Jakarta', 
    'Asia/Brunei'                  => '(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi', 
    'Asia/Krasnoyarsk'             => '(UTC+08:00) Krasnoyarsk'     , 
    'Asia/Ulaanbaatar'             => '(UTC+08:00) Ulaan Bataar'    , 
    'Asia/Kuala_Lumpur'            => '(UTC+08:00) Kuala Lumpur, Singapore', 
    'Asia/Taipei'                  => '(UTC+08:00) Taipei'          , 
    'Australia/Perth'              => '(UTC+08:00) Perth'           , 
    'Asia/Irkutsk'                 => '(UTC+09:00) Irkutsk'         , 
    'Asia/Seoul'                   => '(UTC+09:00) Seoul'           , 
    'Asia/Tokyo'                   => '(UTC+09:00) Tokyo'           , 
    'Australia/Darwin'             => '(UTC+09:30) Darwin'          , 
    'Australia/Adelaide'           => '(UTC+09:30) Adelaide'        , 
    'Australia/Canberra'           => '(UTC+10:00) Canberra, Melbourne, Sydney', 
    'Australia/Brisbane'           => '(UTC+10:00) Brisbane'        , 
    'Australia/Hobart'             => '(UTC+10:00) Hobart'          , 
    'Asia/Vladivostok'             => '(UTC+10:00) Vladivostok'     , 
    'Pacific/Guam'                 => '(UTC+10:00) Guam, Port Moresby', 
    'Asia/Yakutsk'                 => '(UTC+10:00) Yakutsk'         , 
    'Etc/GMT-11'				   => '(UTC+11:00) Solomon Is., New Caledonia',
    'Pacific/Fiji'                 => '(UTC+12:00) Fiji'            , 
    'Asia/Kamchatka'               => '(UTC+12:00) Kamchatka'       , 
    'Pacific/Auckland'             => '(UTC+12:00) Auckland'        , 
    'Asia/Magadan'                 => '(UTC+12:00) Magadan'         , 
    'Pacific/Tongatapu'            => '(UTC+13:00) Nukualofa'       , 
    'Summary'                      => 'Résumé'                     , // TODO: Review
    'Detail'                       => 'Detail'                      , // TODO: Review
    'LBL_USER_LIST_DETAILS'        => 'Details'                     , // TODO: Review
    'LBL_USER_DELETED_SUCCESSFULLY' => 'Utilisateur supprimé avec succès',
    'LBL_ACTIVE_USERS' => 'Les utilisateurs actifs',
    'LBL_INACTIVE_USERS' => 'Les utilisateurs inactifs',
    'LBL_DELETE_USER_PERMANENTLY' => "Supprimer l'utilisateur de façon permanente",
    'LBL_RESTORE' => 'Restaurer',
    'LBL_USER_RESTORED_SUCCESSFULLY' => 'Utilisateur restauré avec succès',
    'LBL_ALMOST_THERE'	=>	'On y est presque!',
    'LBL_ABOUT_ME'		=>	'À propos de moi',
    'LBL_WE_PROMISE_TO_KEEP_THIS_PRIVATE'	=>	'(Nous promettons de garder ca privé)',
    'LBL_ALL_FIELDS_BELOW_ARE_REQUIRED'		=>	'(Tous les champs ci-dessous sont obligatoires)',
    'LBL_GET_STARTED'	=> 'Démarrer',
    'LBL_YOUR_CONTACT_NUMBER' => 'Votre numéro de contact',
    'LBL_WHERE_ARE_YOU_FROM' =>	'Où êtes-vous?',
    'LBL_SELECT_COUNTRY'	=> 'Sélectionnez un pays',
    'LBL_COMPANY_SIZE'		=> 'Taille de lentreprise',
    'LBL_JOB_TITLE'			=> 'Titre du poste',
    'LBL_DEPARTMENT'		=> 'Département',
    'LBL_BASE_CURRENCY'		=> 'Devise de base',
    'LBL_CHOOSE_BASE_CURRENCY'	=> 'Choisissez Devise de base',
    'LBL_OPERATING_CURRENCY'	=> 'Devise de référence ne peut pas être modifié ultérieurement. Choisissez votre monnaie de fonctionnement',
    'LBL_LANGUAGE' => 'Langue',
    'LBL_CHOOSE_LANGUAGE'	=> 'Choisissez la langue',
    'LBL_CHOOSE_TIMEZONE'	=> 'Choisissez Fuseau horaire',
    'LBL_DATE_FORMAT'		=> 'Format de la date',
    'LBL_CHOOSE_DATE_FORMAT'=> 'Choisissez Format de date',
    'LBL_PHONE'	=> 'Téléphone',
    'Space' => 'Espace',
    //picklist values for Default Calendar View field in MyPreference Page
    'ListView' => 'Afficher la liste',
    'MyCalendar' => 'Mon calendrier',
    'SharedCalendar' => 'Calendrier partagé',

    'LBL_CHANGE_OWNER' => 'Changer de propriétaire',
    'LBL_TRANSFER_OWNERSHIP' => 'Transfert de propriété',
    'LBL_TRANSFER_OWNERSHIP_TO_USER' => 'De transférer la propriété de l\'utilisateur',
    'LBL_OWNERSHIP_TRANSFERRED_SUCCESSFULLY' => 'Propriétaire du CRM changé avec succès',
    'LBL_OWNERSHIP_TRANSFERRED_FAILED' => 'Impossible de changer le propriétaire du CRM',
    'Account Owner' => 'Titulaire du compte',
    'Starting Day of the week' => 'Jour de départ de la semaine',
    'Day starts at' => 'Journée commence à',
    'Default Event Status' => 'Statut par défaut de l\'événement',
    'Default Activity Type' => 'Par défaut le type d\'activité',
    'Default Record View' => 'Par défaut Voir la fiche',
    'Left Panel Hide' => 'Masquer le Panneau de gauche',
    'Row Height' => 'Hauteur de ligne',
    'LBL_RESTORE_USER_FAILED' => 'Impossible de restaurer utilisateur. Il existe déjà un utilisateur CRM avec ce nom d\'utilisateur.',

    'LBL_DUPLICATE_USER_EXISTS' => 'L\'Utilisateur existe déjà',


    'LBL_CHANGE_USERNAME'          => 'Changer Nom d\'utilisateur'   ,
    'LBL_USERNAME_CHANGED'         => 'Nom d\'utilisateur modifié avec succès',
    'ERROR_CHANGE_USERNAME'        => 'Erreur dans le changement nom d\'utilisateur. S\'il vous plaît essayer plus tard',

    'LBL_REMOVE_USER' => 'Supprimer',
    'LBL_MORE_OPTIONS' => 'Plus D\'Options',
    'LBL_RESTORE_USER' => 'Utilisateur De La Restauration',
    ' Receive Incoming Calls' => 'Recevoir Des Appels Entrants',
    'LBL_OLD_PASSWORD' => 'Ancien Mot De Passe',
    'LBL_CHANGE_PASSWORD' => 'Changer De Mot De Passe',
    'LBL_NEW_PASSWORD' => 'Nouveau Mot De Passe',
    'LBL_CONFIRM_PASSWORD' => 'Confirmer Le Mot De Passe',
    'LBL_CHANGE_ACCESS_KEY' => "Changer la clé d'accès",
    'LBL_ACCESS_KEY_UPDATED_SUCCESSFULLY' => "Clé d'accès mis à jour avec succès",
    'LBL_FAILED_TO_UPDATE_ACCESS_KEY' => "Impossible de mettre à jour la clé d'accès",
    'LBL_LOGIN_AS' => 'Connexion en tant que ',
    'LBL_CREATE_USER' => 'Créer Un Utilisateur',
    'LBL_DELETE_USER_PERMANENTLY_INFO' => 'Suppression d\'un utilisateur de façon permanente va transférer tous les documents, y compris les commentaires et histoires pour un nouvel utilisateur.',
    'LBL_TO_CRM' => 'Connexion de Vtiger CRM',
    'LBL_INVALID_USER_OR_PASSWORD' => 'Nom d\'utilisateur non valide ou le mot de passe.',
    'LBL_INVALID_USER_OR_EMAIL' => 'Nom d\'utilisateur non valide ou une adresse Email.',
    'LBL_EMAIL_SEND' => 'Nous vous avons envoyé un email pour réinitialiser votre mot de passe.',
    'ForgotPassword' => 'Mot De Passe Oublié',
    'LBL_CONNECT_WITH_US' => 'Se connecter avec NOUS',
    'LBL_GET_MORE' => 'Obtenez plus de Vtiger',
    'LBL_TRANSFER_RECORDS_TO_USER' => 'Transférer des dossiers à l\'utilisateur',
    'LBL_USER_TO_BE_DELETED' => 'Utilisateur Supprimé',
    'LBL_USERS_SETTINGS' => 'PARAMÈTRES UTILISATEURS',
    'LBL_TEMPLATES' => 'Modèles',

);
$jsLanguageStrings = array(
		
    //Curency seperator validation messages
    'JS_ENTER_OLD_PASSWORD'=>'Veuilliez rentrer votre ancien mot de passe.',
    'JS_ENTER_NEW_PASSWORD'=>'Veuilliez rentrer votre nouveau mot de passe.',
    'JS_ENTER_CONFIRMATION_PASSWORD'=>'Veuilliez rentrer votre mot de passe de confirmation.',
    'JS_REENTER_PASSWORDS'=>'Veuillez ré-entrer votre mot de passe.  Le nouveau mot de passe et sa confirmation ne correspondent pas.',
    'JS_INVALID_PASSWORD'=>'Vous devez spécifier un nom d\'utilisateur et mot de passe valide.',
    'JS_PASSWORD_CHANGE_FAILED_1'=>'Le changement de mot de passe a échoué pour l\'utilisateur ',
    'JS_PASSWORD_CHANGE_FAILED_2'=>' .  Le nouveau mot de passe doit être remplie.',
    'JS_PASSWORD_INCORRECT_OLD'=>'L\'Ancien mot de passe est incorrecte. Veuillez ré-entrer les informations de mot de passe.',
    'JS_ENTERED_CURRENT_USERNAME_MSG' => 'Vous avez entré le nom d\'utilisateur actuel. S\'il vous plaît, entrer un nouveau nom d\'utilisateur.',
    'JS_NEW_ACCESS_KEY_REQUESTED' => 'Une nouvelle clé d\accès a été demandée',
    'JS_CHANGE_ACCESS_KEY_CONFIRMATION' => "Vous avez demandé une nouvelle clé d'accès. &lt;br&gt;&lt;br&gt;Lorsque la nouvelle clé d'accès sera générée, vous devrez remplacer l'ancienne clef par la nouvelle dans toutes les extensions installées. &lt;br&gt;&lt;br&gt;Voulez-vous continuer?",
);
