<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
    'Project'                      => 'Proyectos'                    , 
	'SINGLE_Project'               => 'Proyecto'                    , 
	'LBL_ADD_RECORD'               => 'Agregar Proyecto'            , 
	'LBL_RECORDS_LIST'             => 'Lista de Proyectos'          , 
	'LBL_PROJECT_INFORMATION'      => 'Información del Proyecto'    , 
	'Project Name'                 => 'Nombre del Proyecto'             , 
	'Start Date'                   => 'Fecha de inicio'                , 
	'Target End Date'              => 'Fecha de término prevista'          , 
	'Actual End Date'              => 'Fecha de término real'              , 
	'Project No'                   => 'Nº de Proyecto'                , 
	'Target Budget'                => 'Presupuesto inicial'         , 
	'Project Url'                  => 'URL de Proyecto'                , 
	'Progress'                     => 'Progreso'                    ,
        'Type'                         => 'Tipo'                        , 
    'Related to'                   => 'En relación con'             ,
	'LBL_TASKS_OPEN'               => 'Actividades abiertas'        , 
	'LBL_TASKS_DUE'                => 'Actividades retrasadas'      , 
	'LBL_TASKS_COMPLETED'          => 'Actividades completadas'     , 
	'LBL_PEOPLE'                   => 'Gente'                       , 
	'LBL_CHARTS'                   => 'Gráficos'                   , 
	'LBL_TASKS_LIST'               => 'Lista de tareas'                  , 
	'LBL_MILESTONES'               => 'Metas'                  , 
	'LBL_TASKS'                    => 'Tareas'                       , 
	'LBL_STATUS_IS'                => 'El estado es'                   , 
	'LBL_STATUS'                   => 'Estado'                      , 
	'LBL_TICKET_PRIORITY'          => 'Prioridad'                    , 
	'LBL_DOWNLOAD_FILE'            => 'Descargar archivo'               , 
    'LBL_MILESTONES_LIST'          => 'Lista de metas'              ,
    'LBL_TASKS_HIGH'               => 'Alta prioridad'              ,
    'LBL_TASKS_NORMAL'             => 'Prioridad normal'            ,
    'LBL_TASKS_LOW'                => 'Prioridad baja'              ,
    'LBL_TASKS_OTHER'              => 'Otras prioridades'           ,
    'LBL_SELECT_PROGRESS'			=> 'Progreso- todos',
	'LBL_SELECT_STATUS'				=> 'estado - todo',
	
	//picklist values
	'prospecting' => 'Prospección',
    'in progress' => 'En curso',
    'initiated' => 'Iniciado',
	'waiting for feedback' => 'Esperando retroalimentación',
    'on hold' => 'En espera',
    'archived' => 'Archivado',
    'completed' => 'Terminado',
    'delivered' => 'Liberado',
	'administrative' => 'Administrativo',
    'operative' => 'Operatorio',
    'other' => 'Otro',
    'low' => 'Bajo',
    'normal' => 'Normal',
    'high' => 'Alto',
    
    //Gantt chart 
    'LBL_CHART' => 'Diagrama de Gantt',
    'LBL_PROGRESS_CHART' => 'Diagrama de Gantt',
    'LBL_TASK_NAME' => 'Nombre de la tarea',
    'LBL_START_DATE' => 'Fecha de inicio',
    'LBL_END_DATE' => 'Fecha de término',
    'LBL_DURATION' => 'Duración',
    'LBL_INFO' => 'Info',
    'LBL_GANTT_INFO1' => 'Deslice sobre el lado derecho de la tarea para ampliar la duración',
    'LBL_GANTT_INFO2' => 'Arrastre a la izquierda o en el centro de la tarea para cambiar la fecha de inicio y de término',
    'LBL_EDIT_PROJECT_TASK_STATUS_COLOR' => 'Editar el color del estado  de la tarea de proyecto',
    'LBL_SELECT_PROJECT_TASK_STATUS_COLOR' => 'Seleccionar el color del estado de la tarea de proyecto',
    'LBL_SELECT_STATUS' => 'Seleccionar estado',
    'LBL_EDIT_COLOR' => 'Editar color',
);

$jsLanguageStrings = array(
   'JS_COLOR_SAVED_SUCESSFULLY' => 'Color guardado exitosamente',
);