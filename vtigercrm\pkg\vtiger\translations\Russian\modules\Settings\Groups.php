<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_TRANSFORM_OWNERSHIP'      => 'Передача права собственности'          , 
	'SINGLE_Groups'                => 'Группа'                , // KEY 5.x: LBL_GROUP
	'LBL_TO_OTHER_GROUP'           => 'Для другой группы'             , 
	'LBL_ADD_RECORD'               => 'Добавить Группу', // KEY 5.x: LBL_ADD_GROUP_BUTTON
	'LBL_GROUP_NAME'               => 'Название Группы', 
	'LBL_GROUP_MEMBERS'            => 'Участники группы'               , 
	'LBL_ADD_USERS_ROLES'          => 'Добавить пользователей, ролей ...'         , 
	'LBL_ROLEANDSUBORDINATE'       => 'Роль и Подчиненные'       , 
	'RoleAndSubordinates'          => 'Роль и Подчиненные'       , 

  'LBL_DUPLICATES_EXIST' => 'Группа с таким именем уже существует',

);
$jsLanguageStrings = array(
	'JS_PLEASE_SELECT_ATLEAST_ONE_MEMBER_FOR_A_GROUP' => 'Пожалуйста, выберите по крайней мере одного члена для группы', 
	'JS_RECORD_DELETED_SUCCESSFULLY' => 'Группа успешно удален'  , 
	'JS_COMMA_NOT_ALLOWED_GROUP' => 'Специальные символы такие как ,"<> не допускаются в имени группы.',
);