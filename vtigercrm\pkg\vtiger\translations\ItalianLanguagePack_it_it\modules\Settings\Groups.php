<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_TRANSFORM_OWNERSHIP'      => 'Trasferisci proprietà'          , 
	'SINGLE_Groups'                => 'Gruppo'                       , // TODO: Review
	'LBL_TO_OTHER_GROUP'           => 'ad altro gruppo '             , 
	'LBL_ADD_RECORD'               => 'Aggiungi Gruppo'                   , // TODO: Review
	'LBL_GROUP_NAME'               => 'Nome Gruppo'                  , // TODO: Review
	'LBL_GROUP_MEMBERS'            => 'Membri Gruppo'               , // TODO: Review
	'LBL_ADD_USERS_ROLES'          => 'Aggiungi Utenti, Ruoli...'         , // TODO: Review
	'LBL_ROLEANDSUBORDINATE'       => 'Ruolo e subordinati'       , // TODO: Review
	'RoleAndSubordinates'          => 'Ruolo e subordinati'       , // TODO: Review

  'LBL_DUPLICATES_EXIST' => 'Il Nome del gruppo Esiste già',

);
$jsLanguageStrings = array(
	'JS_PLEASE_SELECT_ATLEAST_ONE_MEMBER_FOR_A_GROUP' => 'Seleziona almeno un membro per un gruppo', // TODO: Review
	'JS_RECORD_DELETED_SUCCESSFULLY' => 'Gruppo cancellato con successoì'  , // TODO: Review
	'JS_COMMA_NOT_ALLOWED_GROUP' => 'Caratteri speciali come ,"&lt;&gt; non sono ammessi nel Nome gruppo.',
);