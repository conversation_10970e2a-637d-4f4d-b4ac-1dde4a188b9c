<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'SINGLE_Settings:Webforms'     => 'Webform'                     , // TODO: Review
	'WebForm Name'                 => 'Webform Name'                , // TODO: Review
	'Public Id'                    => 'Public Id'                   , // TODO: Review
	'Enabled'                      => 'Status'                      , // TODO: Review
	'Module'                       => 'Module'                      , // TODO: Review
	'Return Url'                   => 'Return Url'                  , // TODO: Review
	'Post Url'                     => 'Post Url'                    , // TODO: Review
    'Captcha Enabled'              => 'captcha Activat'             ,
	'SINGLE_Webforms'              => 'Webform'                     , // TODO: Review
	'LBL_SHOW_FORM'                => 'Show Form'                   , // TODO: Review
	'LBL_DUPLICATES_EXIST'         => 'Webform Name already exists' , // TODO: Review
	'LBL_WEBFORM_INFORMATION'      => 'Webform Information'         , // TODO: Review
	'LBL_FIELD_INFORMATION'        => 'Field Information'           , // TODO: Review
	'LBL_FIELD_NAME'               => 'Field Name'                  , // TODO: Review
	'LBL_OVERRIDE_VALUE'           => 'Override Value'              , // TODO: Review
	'LBL_MANDATORY'                => 'Mandatory'                   , // TODO: Review
	'LBL_WEBFORM_REFERENCE_FIELD'  => 'Webforms reference Field'    , // TODO: Review
	'LBL_SELECT_FIELDS_OF_TARGET_MODULE' => 'Select Fields for Target Module...', // TODO: Review
	'LBL_ALLOWS_YOU_TO_MANAGE_WEBFORMS' => 'Allows you to manage webforms', // TODO: Review
	'LBL_ADD_FIELDS'               => 'Add Fields'                  , // TODO: Review
	'LBL_EMBED_THE_FOLLOWING_FORM_IN_YOUR_WEBSITE' => 'Embed the following form in your website', // TODO: Review
	'LBL_SELECT_VALUE'             => 'Select Value'                , // TODO: Review
	'LBL_LABEL'                    => 'label'                       , // TODO: Review
	'LBL_SAVE_FIELDS_ORDER' => 'Salveaza domenii comanda', 
	'LBL_HIDDEN' => 'Ascuns',
	'LBL_ENABLE_TARGET_MODULES_FOR_WEBFORM' => 'Permite module țintă pentru Webform',
	'LBL_ASSIGN_USERS' => 'Cesiunea de utilizator',
    'LBL_ASSIGN_ROUND_ROBIN' => 'Atribui Utilizatorii In Round Robin',
    'LBL_ROUNDROBIN_USERS_LIST' => 'Runda Lista de Utilizatori Robin',

  'LBL_ADD_RECORD' => 'Adăugare Formular Web',

	'LBL_UPLOAD_DOCUMENTS' => 'Încărcați Documente',
	'LBL_ADD_FILE_FIELD' => 'Fișier de încărcare Câmp',
	'LBL_FIELD_LABEL' => 'Titlul documentului',
	'LBL_FILE_FIELD_INFO' => 'Pentru fiecare fișier încărcat de web a forma un nou document este creat cu fișierul atașat. Documentul este, de asemenea, legat de acest nou creat%s.',
	'LBL_NO_FILE_FIELD' => 'Nu există câmpuri de fișiere adăugate.',
	'LBL_COPY_TO_CLIPBOARD' => 'Copiați în clipboard',
);
$jsLanguageStrings = array(
	'JS_WEBFORM_DELETED_SUCCESSFULLY' => 'Webform deleted successfully', // TODO: Review
	'JS_LOADING_TARGET_MODULE_FIELDS' => 'Loadding Target Module Fields', // TODO: Review
	'JS_SELECT_VALUE'              => 'Select Vlaue'                , // TODO: Review
	'JS_MANDATORY_FIELDS_WITHOUT_OVERRIDE_VALUE_CANT_BE_HIDDEN' => 'Câmpurile obligatorii fără valori suprascrie cant fi ascuns',
	'JS_REFERENCE_FIELDS_CANT_BE_MANDATORY_WITHOUT_OVERRIDE_VALUE' => 'Domenii de referință cant fi obligatorie, fără valoare de anulare',
	'JS_TYPE_TO_SEARCH' => 'Tastați pentru a căuta',
	"JS_WEBFORM_WITH_THIS_NAME_ALREADY_EXISTS" => 'Webform cu acest nume există deja',

  'JS_SELECT_AN_OPTION' => 'Selectați o opțiune',
  'JS_LABEL' => 'tag-uri',

	'JS_MAX_FILE_FIELDS_LIMIT' => 'Maxim puteți adăuga%s câmpuri de fișiere.',
	'JS_COPIED_SUCCESSFULLY' => 'Copiată cu succes.',
	'JS_COPY_FAILED' => 'Copiere nu a reușit. Vă rugăm să copiați manual.',
);