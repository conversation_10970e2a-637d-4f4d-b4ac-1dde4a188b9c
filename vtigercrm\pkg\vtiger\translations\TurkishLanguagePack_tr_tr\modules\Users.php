<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD'               => 'Add User'                    , // TODO: Review
	'LBL_MY_PREFERENCES'           => 'Ayarlarim'                  , 
	'LBL_MORE_INFORMATION'         => 'Detay<PERSON> Bilgi'              , 
	'LBL_USERLOGIN_ROLE'           => 'Kullanici Giris & Rol'    , 
	'LBL_USER_IMAGE_INFORMATION'   => 'Kullanici Resmi'           , 
	'LBL_CURRENCY_CONFIGURATION'   => 'Currency and Number Field Configuration', 
	'LBL_ADDRESS_INFORMATION'      => 'Adres Bilgisi'               , 
	'LBL_USER_ADV_OPTIONS'         => 'Kullanıcı Gelişmiş Seçenekler', 
	'Asterisk Configuration'       => 'Asterisk Yapilandirma'     , 
	'LBL_HOME_PAGE_COMPONENTS'     => 'Ana Sayfa Bileşenleri'       , 
	'LBL_TAG_CLOUD_DISPLAY'        => 'Tag Cloud Display'           , // TODO: Review
	'Role'                         => 'Rol'                         , 
	'Admin'                        => 'Yönetim'                   , 
	'User Name'                    => 'Kullanici Ismi'           , 
	'Default Activity View'		   => 'Standart MyCalendar Görüntüle',
	'Default Calendar View'        => 'Standart Takvim Görünümü'            ,
	'Default Lead View'            => 'Standart Kurşun Görüntüle', 
	'Title'                        => 'Baslik'                    , 
	'Office Phone'                 => 'Ofis Telefonu'               , 
	'Department'                   => 'Departman'                   , 
	'Reports To'                   => 'Için Raporlar'                      , 
	'Yahoo id'                     => 'Yahoo id'                    , 
	'Home Phone'                   => 'Ev Telefonu'                 , 
	'User Image'                   => 'Fotoğrafınızı yükleyin'            , 
	'Date Format'                  => 'Tarih Formati'              , 
	'Tag Cloud'                    => 'Tag Bulutu'                  , 
	'Signature'                    => 'Imza'                       , 
	'Street Address'               => 'Sokak'                       , 
	'Password'                     => 'Sifre'                      , 
	'Confirm Password'             => 'Sifre Onayla'               , 
	'LBL_SHOWN'                    => 'Gösterildi'                   , 
	'LBL_HIDDEN'                   => 'Gizlenen'                    , 
	'LBL_SHOW'                     => 'Gösteri'                     , 
	'LBL_HIDE'                     => 'Gizle'                       , 
	'LBL_HOME_PAGE_COMPO'          => 'Ana Sayfa Bileşenleri'       , 
	'LBL_LOGIN_HISTORY'            => 'Geçmiş giriniz'              , 
	'LBL_USERDETAIL_INFO'          => 'Kullanıcı ayrıntılarını görüntüleme', 
	'LBL_DELETE_GROUP'             => 'Grup Sil'                    , 
	'LBL_DELETE_GROUPNAME'         => 'Grup Silindi'                , 
	'LBL_TRANSFER_GROUP'           => 'Aidiyetini Tasi: '         , 
	'LBL_DELETE_USER'              => 'Kullanici Silindi'         , 
	'LBL_TRANSFER_USER'            => 'Kullanicinin Aidiyetini Transfer Et', 
	'LBL_DELETE_PROFILE'           => 'Profil Sil'                  , 
	'LBL_TRANSFER_ROLES_TO_PROFILE' => 'Profile Rolleri Transfer Et' , 
	'LBL_PROFILE_TO_BE_DELETED'    => 'Profil Silindi'              , 
	'INTERNAL_MAIL_COMPOSER'       => 'Dahili Eposta Olusturucusu' , 
	'Asterisk Extension'           => 'Asterisk Uzatma'             , 
	' Receive Incoming Calls'      => 'Receive Incoming Calls'      , // TODO: Review
	'Reminder Interval'            => 'Hatirlatma Araligi'      , 
	'Webservice Access Key'        => 'Erisim Anahtari'           , 
	'Language'                     => 'Dil:'                        , 
	'Theme'                        => 'Theme'                       , 
	'Time Zone'                    => 'Time Zone'                   , 
	'Decimal Separator'            => 'Decimal Separator'           , 
	'Digit Grouping Pattern'       => 'Digit Grouping Pattern'      , 
	'Digit Grouping Separator'     => 'Digit Grouping Separator'    , 
	'Symbol Placement'             => 'Symbol Placement'            , 
	'Number Of Currency Decimals'  => 'Number Of Currency Decimals' , 
	'Truncate Trailing Zeros'      => 'Truncate Trailing Zeros'     , 
	'Default Call Duration'        => 'Default Call Duration (Mins)', // TODO: Review
	'Other Event Duration'         => 'Other Event Duration (Mins)' , // TODO: Review
	'Calendar Hour Format'         => 'Calendar Hour Format'        , // TODO: Review
	'Kwajalein'                    => '(UTC-12:00) International Date Line West', 
	'Pacific/Midway'               => '(UTC-11:00) Coordinated Universal Time-11', 
	'Pacific/Samoa'                => '(UTC-11:00) Samoa'           , 
	'Pacific/Honolulu'             => '(UTC-10:00) Hawaii'          , 
	'America/Anchorage'            => '(UTC-09:00) Alaska'          , 
	'America/Los_Angeles'          => '(UTC-08:00) Pacific Time (US &amp; Canada)', 
	'America/Tijuana'              => '(UTC-08:00) Tijuana, Baja California', 
	'America/Denver'               => '(UTC-07:00) Mountain Time (US &amp; Canada)', 
	'America/Chihuahua'            => '(UTC-07:00) Chihuahua, La Paz, Mazatlan', 
	'America/Mazatlan'             => '(UTC-07:00) Mazatlan'        , 
	'America/Phoenix'              => '(UTC-07:00) Arizona'         , 
	'America/Regina'               => '(UTC-06:00) Saskatchewan'    , 
	'America/Tegucigalpa'          => '(UTC-06:00) Central America' , 
	'America/Chicago'              => '(UTC-06:00) Central Time (US &amp; Canada)', 
	'America/Mexico_City'          => '(UTC-06:00) Mexico City'     , 
	'America/Monterrey'            => '(UTC-06:00) Monterrey'       , 
	'America/New_York'             => '(UTC-05:00) Eastern Time (US &amp; Canada)', 
	'America/Bogota'               => '(UTC-05:00) Bogota, Lima, Quito', 
	'America/Lima'                 => '(UTC-05:00) Lima'            , 
	'America/Rio_Branco'           => '(UTC-05:00) Rio Branco'      , 
	'America/Indiana/Indianapolis' => '(UTC-05:00) Indiana (East)'  , 
	'America/Caracas'              => '(UTC-04:30) Caracas'         , 
	'America/Halifax'              => '(UTC-04:00) Atlantic Time (Canada)', 
	'America/Manaus'               => '(UTC-04:00) Manaus'          , 
	'America/Santiago'             => '(UTC-04:00) Santiago'        , 
	'America/La_Paz'               => '(UTC-04:00) La Paz'          , 
	'America/Cuiaba'               => '(UTC-04:00) Cuiaba'          , 
	'America/Asuncion'             => '(UTC-04:00) Asuncion'        , 
	'America/St_Johns'             => '(UTC-03:30) Newfoundland'    , 
	'America/Argentina/Buenos_Aires' => '(UTC-03:00) Buenos Aires'    , 
	'America/Sao_Paulo'            => '(UTC-03:00) Brasilia'        , 
	'America/Godthab'              => '(UTC-03:00) Greenland'       , 
	'America/Montevideo'           => '(UTC-03:00) Montevideo'      , 
	'Atlantic/South_Georgia'       => '(UTC-02:00) Mid-Atlantic'    , 
	'Atlantic/Azores'              => '(UTC-01:00) Azores'          , 
	'Atlantic/Cape_Verde'          => '(UTC-01:00) Cape Verde Is.'  , 
	'Europe/London'                => '(UTC) London, Edinburgh, Dublin, Lisbon', 
	'UTC'                          => '(UTC) Coordinated Universal Time, Greenwich Mean Time', 
	'Africa/Monrovia'              => '(UTC) Monrovia, Reykjavik'   , 
	'Africa/Casablanca'            => '(UTC) Casablanca'            , 
	'Europe/Belgrade'              => '(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague', 
	'Europe/Sarajevo'              => '(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb', 
	'Europe/Brussels'              => '(UTC+01:00) Brussels, Copenhagen, Madrid, Paris', 
	'Africa/Algiers'               => '(UTC+01:00) West Central Africa', 
	'Europe/Amsterdam'             => '(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna', 
	'Europe/Minsk'                 => '(UTC+02:00) Minsk'           , 
	'Africa/Cairo'                 => '(UTC+02:00) Cairo'           , 
	'Europe/Helsinki'              => '(UTC+02:00) Helsinki, Riga, Sofia, Tallinn, Vilnius', 
	'Europe/Athens'                => '(UTC+02:00) Athens, Bucharest', 
	'Europe/Istanbul'              => '(UTC+02:00) Istanbul'        , 
	'Asia/Jerusalem'               => '(UTC+02:00) Jerusalem'       , 
	'Asia/Amman'                   => '(UTC+02:00) Amman'           , 
	'Asia/Beirut'                  => '(UTC+02:00) Beirut'          , 
	'Africa/Windhoek'              => '(UTC+02:00) Windhoek'        , 
	'Africa/Harare'                => '(UTC+02:00) Harare'          , 
	'Asia/Kuwait'                  => '(UTC+03:00) Kuwait, Riyadh'  , 
	'Asia/Baghdad'                 => '(UTC+03:00) Baghdad'         , 
	'Africa/Nairobi'               => '(UTC+03:00) Nairobi'         , 
	'Asia/Tehran'                  => '(UTC+03:30) Tehran'          , 
	'Asia/Tbilisi'                 => '(UTC+04:00) Tbilisi'         , 
	'Europe/Moscow'                => '(UTC+03:00) Moscow, Volgograd', 
	'Asia/Muscat'                  => '(UTC+04:00) Abu Dhabi, Muscat', 
	'Asia/Baku'                    => '(UTC+04:00) Baku'            , 
	'Asia/Yerevan'                 => '(UTC+04:00) Yerevan'         , 
	'Asia/Karachi'                 => '(UTC+05:00) Islamabad, Karachi', 
	'Asia/Tashkent'                => '(UTC+05:00) Tashkent'        , 
	'Asia/Kolkata'                 => '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi', 
	'Asia/Colombo'                 => '(UTC+05:30) Sri Jayawardenepura', 
	'Asia/Katmandu'                => '(UTC+05:45) Kathmandu'       , 
	'Asia/Dhaka'                   => '(UTC+06:00) Dhaka'           , 
	'Asia/Almaty'                  => '(UTC+06:00) Almaty'          , 
	'Asia/Yekaterinburg'           => '(UTC+06:00) Ekaterinburg'    , 
	'Asia/Rangoon'                 => '(UTC+06:30) Yangon (Rangoon)', 
	'Asia/Novosibirsk'             => '(UTC+07:00) Novosibirsk'     , 
	'Asia/Bangkok'                 => '(UTC+07:00) Bangkok, Jakarta', 
	'Asia/Brunei'                  => '(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi', 
	'Asia/Krasnoyarsk'             => '(UTC+08:00) Krasnoyarsk'     , 
	'Asia/Ulaanbaatar'             => '(UTC+08:00) Ulaan Bataar'    , 
	'Asia/Kuala_Lumpur'            => '(UTC+08:00) Kuala Lumpur, Singapore', 
	'Asia/Taipei'                  => '(UTC+08:00) Taipei'          , 
	'Australia/Perth'              => '(UTC+08:00) Perth'           , 
	'Asia/Irkutsk'                 => '(UTC+09:00) Irkutsk'         , 
	'Asia/Seoul'                   => '(UTC+09:00) Seoul'           , 
	'Asia/Tokyo'                   => '(UTC+09:00) Tokyo'           , 
	'Australia/Darwin'             => '(UTC+09:30) Darwin'          , 
	'Australia/Adelaide'           => '(UTC+09:30) Adelaide'        , 
	'Australia/Canberra'           => '(UTC+10:00) Canberra, Melbourne, Sydney', 
	'Australia/Brisbane'           => '(UTC+10:00) Brisbane'        , 
	'Australia/Hobart'             => '(UTC+10:00) Hobart'          , 
	'Asia/Vladivostok'             => '(UTC+10:00) Vladivostok'     , 
	'Pacific/Guam'                 => '(UTC+10:00) Guam, Port Moresby', 
	'Asia/Yakutsk'                 => '(UTC+10:00) Yakutsk'         , 
	'Etc/GMT-11'				   => '(UTC+11:00) Solomon Is., New Caledonia',
	'Pacific/Fiji'                 => '(UTC+12:00) Fiji'            , 
	'Asia/Kamchatka'               => '(UTC+12:00) Kamchatka'       , 
	'Pacific/Auckland'             => '(UTC+12:00) Auckland'        , 
	'Asia/Magadan'                 => '(UTC+12:00) Magadan'         , 
	'Pacific/Tongatapu'            => '(UTC+13:00) Nukualofa'       , 
	'Summary'                      => 'Summary'                     , // TODO: Review
	'Detail'                       => 'Detail'                      , // TODO: Review
	'LBL_USER_LIST_DETAILS'        => 'Details'                     , // TODO: Review
	'LBL_USER_DELETED_SUCCESSFULLY' => 'Kullanici basariyla silindi',
        'LBL_ACTIVE_USERS' => 'Aktif Kullanicilar',
        'LBL_INACTIVE_USERS' => 'Etkin Kullanicilar',
        'LBL_DELETE_USER_PERMANENTLY' => 'Kalici Kullaniciyi Sil',
        'LBL_RESTORE' => 'Restore',
        'LBL_USER_RESTORED_SUCCESSFULLY' => 'Kullanici basariyla restore',
		'LBL_ALMOST_THERE'	=>	'Neredeyse orada!',
		'LBL_ABOUT_ME'		=>	'Hakkimda',
		'LBL_WE_PROMISE_TO_KEEP_THIS_PRIVATE'	=>	'(Biz bu özel tutmak için söz veriyorum)',
		'LBL_ALL_FIELDS_BELOW_ARE_REQUIRED'		=>	'(Altındaki tüm alanların doldurulması zorunludur)',
		'LBL_GET_STARTED'	=> 'Baslayin',
		'LBL_YOUR_CONTACT_NUMBER' => 'Sizin Iletisim Numarasi',
		'LBL_WHERE_ARE_YOU_FROM' =>	'Eger Nerelisin?',
		'LBL_SELECT_COUNTRY'	=> 'Ülke Seçin',
		'LBL_COMPANY_SIZE'		=> 'Şirket Büyüklüğü',
		'LBL_JOB_TITLE'			=> 'Meslek',
		'LBL_DEPARTMENT'		=> 'Bölüm',
		'LBL_BASE_CURRENCY'		=> 'Baz Döviz',
		'LBL_CHOOSE_BASE_CURRENCY'	=> 'Dövizi Seç',
		'LBL_OPERATING_CURRENCY'	=> 'Baz para birimi daha sonra değiştirilemez. İşletim para seçin',
		'LBL_LANGUAGE' => 'Dil',
		'LBL_CHOOSE_LANGUAGE'	=> 'Dil Seçin',
		'LBL_CHOOSE_TIMEZONE'	=> 'Dilimimi seçin',
		'LBL_DATE_FORMAT'		=> 'Tarih Biçimi',
		'LBL_CHOOSE_DATE_FORMAT'=> 'Tarih Biçimi seçin',
		'LBL_PHONE'	=> 'Telefon',
        'Space' => 'Uzay',
		//picklist values for Default Calendar View field in MyPreference Page
		'ListView' => 'Liste Görünümü',
		'MyCalendar' => 'Benim Takvim',
		'SharedCalendar' => 'Paylaşılan Takvim',
    
    'LBL_CHANGE_OWNER' => 'Sahibi Değiştir',
    'LBL_TRANSFER_OWNERSHIP' => 'Sahipliğini Transferi',
    'LBL_TRANSFER_OWNERSHIP_TO_USER' => 'Kullanıcıya Sahipliğini transfer',
    'LBL_OWNERSHIP_TRANSFERRED_SUCCESSFULLY' => 'CRM Sahibi başarıyla değiştirildi',
    'LBL_OWNERSHIP_TRANSFERRED_FAILED' => 'Başarısız değişen CRM sahibi',
    'Account Owner' => 'Hesap Sahibi',
    'Starting Day of the week' => 'Haftanın başlangıç günü',
    'Day starts at' => 'Gün başlar',
    'Default Event Status' => 'Standart Olay Durumu',
    'Default Activity Type' => 'Varsayılan Faaliyet Türü',
    'Default Record View' => 'Standart Kayıt Görüntüle',
    'Left Panel Hide' => 'Sol Paneli gizle',
    'Row Height' => 'Satır Yüksekliği',
	'LBL_RESTORE_USER_FAILED' => 'Kullanıcı geri alınamadı. Bu kullanıcı adı ile CRM kullanıcı zaten var.',
    
    'LBL_DUPLICATE_USER_EXISTS' => 'Kullanıcı Zaten Var',


	'LBL_CHANGE_USERNAME'          => 'Değişim adı'              ,
	'LBL_USERNAME_CHANGED'         => 'Kullanıcı başarıyla değiştirildi',
	'ERROR_CHANGE_USERNAME'        => 'Değişim adı hata. Daha sonra deneyin',

  'LBL_REMOVE_USER' => 'Sil',
  'LBL_MORE_OPTIONS' => 'Daha Fazla Seçenek',
  'LBL_RESTORE_USER' => 'Kullanıcı Geri ',
  'LBL_OLD_PASSWORD' => 'Eski Parola',
  'LBL_CHANGE_PASSWORD' => 'Şifre Değiştir',
  'LBL_NEW_PASSWORD' => 'Yeni Şifre',
  'LBL_CONFIRM_PASSWORD' => 'Parolayı Yeniden Girin',
	'LBL_CHANGE_ACCESS_KEY' => 'Değişim Erişim Anahtarı',
	'LBL_ACCESS_KEY_UPDATED_SUCCESSFULLY' => 'Erişim tuşu başarıyla güncellendi',
	'LBL_FAILED_TO_UPDATE_ACCESS_KEY' => 'erişim anahtarı güncellenemedi',
  'LBL_LOGIN_AS' => 'Giriş olarak ',
  'LBL_CREATE_USER' => 'Kullanıcı Oluşturma ',
  'LBL_DELETE_USER_PERMANENTLY_INFO' => 'Kalıcı bir kullanıcı silme, yeni kullanıcı için tüm kayıtları açıklamaları ve geçmiş dahil olmak üzere transfer olacak.',
  'LBL_TO_CRM' => 'Vtiger CRM giriş',
  'LBL_INVALID_USER_OR_PASSWORD' => 'Geçersiz kullanıcı adı veya parola.',
  'LBL_INVALID_USER_OR_EMAIL' => 'Geçersiz Kullanıcı adı veya e-Posta adresi.',
  'LBL_EMAIL_SEND' => 'E-posta şifrenizi sıfırlamak için gönderdik.',
  'ForgotPassword' => 'Şifremi Unuttum',
  'LBL_CONNECT_WITH_US' => 'BİZE ile Bağlan ',
  'LBL_GET_MORE' => 'Vtiger kurtulmak ',
  'LBL_TRANSFER_RECORDS_TO_USER' => 'Kullanıcı Transfer kayıtları ',
  'LBL_USER_TO_BE_DELETED' => 'Silinecek kullanıcı ',
  'LBL_USERS_SETTINGS' => 'KULLANICI AYARLARI',
  'LBL_TEMPLATES' => 'Şablonları',

);
$jsLanguageStrings = array(
		
	//Curency seperator validation messages
	'JS_ENTER_OLD_PASSWORD'=>'Please enter your old password.',
	'JS_ENTER_NEW_PASSWORD'=>'Please enter your new password.',
	'JS_ENTER_CONFIRMATION_PASSWORD'=>'Please enter your password confirmation.',
	'JS_REENTER_PASSWORDS'=>'Please re-enter passwords.  The \"new password\" and \"confirm password\" values do not match.',
	'JS_INVALID_PASSWORD'=>'You must specify a valid username and password.',
	'JS_PASSWORD_CHANGE_FAILED_1'=>'User password change failed for ',
	'JS_PASSWORD_CHANGE_FAILED_2'=>' failed.  The new password must be set.',
	'JS_PASSWORD_INCORRECT_OLD'=>'Incorrect old password specified. Re-enter password information.',
	'JS_ENTERED_CURRENT_USERNAME_MSG' => 'Geçerli kullanıcı adı girdi. Yeni adı girin.',
	'JS_NEW_ACCESS_KEY_REQUESTED' => 'Yeni Erişim anahtarı talep',
	'JS_CHANGE_ACCESS_KEY_CONFIRMATION' => 'Yeni Erişim anahtarı için istenen & lt olması,. Br &gt;&lt;br& gt;. Yeni Erişim tuşu hüküm ile, yeni yüklü tüm uzantıları biri & lt eski erişim anahtarı değiştirmek zorunda;br&gt;&lt;br&gt;Do devam etmek istiyor musunuz?',
);