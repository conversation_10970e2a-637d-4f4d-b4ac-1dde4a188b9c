<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD'               => 'Add User'                    , // TODO: Review
	'LBL_MY_PREFERENCES'           => 'Preferintele mele'           , 
	'LBL_MORE_INFORMATION'         => 'Mai multa info'              , 
	'LBL_USERLOGIN_ROLE'           => 'Login & Rol utilizator'      , 
	'LBL_USER_IMAGE_INFORMATION'   => 'Poza utilizator'             , 
	'LBL_CURRENCY_CONFIGURATION'   => 'Currency and Number Field Configuration', 
	'LBL_ADDRESS_INFORMATION'      => 'Adresa utilizator'           , 
	'LBL_USER_ADV_OPTIONS'         => 'Optiuni Avansate Utilizator' , 
	'Asterisk Configuration'       => 'Asterisk Configuration'      , 
	'LBL_HOME_PAGE_COMPONENTS'     => 'Componente index'            , 
	'LBL_TAG_CLOUD_DISPLAY'        => 'Tag Cloud Display'           , // TODO: Review
	'Role'                         => 'Rol'                         , 
	'Admin'                        => 'Admin'                       , 
	'User Name'                    => 'Nume Utilizator'             , 
	'Default Activity View'		   => 'Implicit MyCalendar View',
	'Default Calendar View'        => 'Vizualizare implicita Calendar'            ,
	'Default Lead View'            => 'Vizualizare implicita Prospectare', 
	'Title'                        => 'Titlu'                       , 
	'Office Phone'                 => 'Nr tel birou'                , 
	'Department'                   => 'Departament'                 , 
	'Reports To'                   => 'Raporteaza catre'            , 
	'Yahoo id'                     => 'Id Yahoo'                    , 
	'Home Phone'                   => 'Nr tel acasa'                , 
	'User Image'                   => 'Incarca poza'                , 
	'Date Format'                  => 'Format data'                 , 
	'Tag Cloud'                    => 'Tag Cloud'                   , 
	'Signature'                    => 'Semnatura'                   , 
	'Street Address'               => 'Adresa strada'               , 
	'Password'                     => 'Parola'                      , 
	'Confirm Password'             => 'Confirma parola'             , 
	'LBL_SHOWN'                    => 'Vizibile'                    , 
	'LBL_HIDDEN'                   => 'Ascunse'                     , 
	'LBL_SHOW'                     => 'Afiseaza'                    , 
	'LBL_HIDE'                     => 'Ascunde'                     , 
	'LBL_HOME_PAGE_COMPO'          => 'Componente index'            , 
	'LBL_LOGIN_HISTORY'            => 'Istoric Login'               , 
	'LBL_USERDETAIL_INFO'          => 'Se vizualizeaza detalii despre utilizator', 
	'LBL_DELETE_GROUP'             => 'Sterge grup'                 , 
	'LBL_DELETE_GROUPNAME'         => 'Grup spre a fi sters'        , 
	'LBL_TRANSFER_GROUP'           => 'Transfera proprietate catre: ', 
	'LBL_DELETE_USER'              => 'Utilizator spre a fi sters'  , 
	'LBL_TRANSFER_USER'            => 'Transfera drept de proprietate catre utilizator', 
	'LBL_DELETE_PROFILE'           => 'Sterge profil'               , 
	'LBL_TRANSFER_ROLES_TO_PROFILE' => 'Transfera roluri catre profil', 
	'LBL_PROFILE_TO_BE_DELETED'    => 'Profil spre a fi sters'      , 
	'INTERNAL_MAIL_COMPOSER'       => 'Compune email intern'        , 
	'Asterisk Extension'           => 'Extensie Asterisk'           , 
	' Receive Incoming Calls'      => 'Receive Incoming Calls'      , // TODO: Review
	'Reminder Interval'            => 'Interval Reminder'           , 
	'Webservice Access Key'        => 'Cheie Acces'                 , 
	'Language'                     => 'Limba'                       , 
	'Theme'                        => 'Theme'                       , 
	'Time Zone'                    => 'Time Zone'                   , 
	'Decimal Separator'            => 'Decimal Separator'           , 
	'Digit Grouping Pattern'       => 'Digit Grouping Pattern'      , 
	'Digit Grouping Separator'     => 'Digit Grouping Separator'    , 
	'Symbol Placement'             => 'Symbol Placement'            , 
	'Number Of Currency Decimals'  => 'Number Of Currency Decimals' , 
	'Truncate Trailing Zeros'      => 'Truncate Trailing Zeros'     , 
	'Default Call Duration'        => 'Default Call Duration (Mins)', // TODO: Review
	'Other Event Duration'         => 'Other Event Duration (Mins)' , // TODO: Review
	'Calendar Hour Format'         => 'Calendar Hour Format'        , // TODO: Review
	'Kwajalein'                    => '(UTC-12:00) International Date Line West', 
	'Pacific/Midway'               => '(UTC-11:00) Coordinated Universal Time-11', 
	'Pacific/Samoa'                => '(UTC-11:00) Samoa'           , 
	'Pacific/Honolulu'             => '(UTC-10:00) Hawaii'          , 
	'America/Anchorage'            => '(UTC-09:00) Alaska'          , 
	'America/Los_Angeles'          => '(UTC-08:00) Pacific Time (US &amp; Canada)', 
	'America/Tijuana'              => '(UTC-08:00) Tijuana, Baja California', 
	'America/Denver'               => '(UTC-07:00) Mountain Time (US &amp; Canada)', 
	'America/Chihuahua'            => '(UTC-07:00) Chihuahua, La Paz, Mazatlan', 
	'America/Mazatlan'             => '(UTC-07:00) Mazatlan'        , 
	'America/Phoenix'              => '(UTC-07:00) Arizona'         , 
	'America/Regina'               => '(UTC-06:00) Saskatchewan'    , 
	'America/Tegucigalpa'          => '(UTC-06:00) Central America' , 
	'America/Chicago'              => '(UTC-06:00) Central Time (US &amp; Canada)', 
	'America/Mexico_City'          => '(UTC-06:00) Mexico City'     , 
	'America/Monterrey'            => '(UTC-06:00) Monterrey'       , 
	'America/New_York'             => '(UTC-05:00) Eastern Time (US &amp; Canada)', 
	'America/Bogota'               => '(UTC-05:00) Bogota, Lima, Quito', 
	'America/Lima'                 => '(UTC-05:00) Lima'            , 
	'America/Rio_Branco'           => '(UTC-05:00) Rio Branco'      , 
	'America/Indiana/Indianapolis' => '(UTC-05:00) Indiana (East)'  , 
	'America/Caracas'              => '(UTC-04:30) Caracas'         , 
	'America/Halifax'              => '(UTC-04:00) Atlantic Time (Canada)', 
	'America/Manaus'               => '(UTC-04:00) Manaus'          , 
	'America/Santiago'             => '(UTC-04:00) Santiago'        , 
	'America/La_Paz'               => '(UTC-04:00) La Paz'          , 
	'America/Cuiaba'               => '(UTC-04:00) Cuiaba'          , 
	'America/Asuncion'             => '(UTC-04:00) Asuncion'        , 
	'America/St_Johns'             => '(UTC-03:30) Newfoundland'    , 
	'America/Argentina/Buenos_Aires' => '(UTC-03:00) Buenos Aires'    , 
	'America/Sao_Paulo'            => '(UTC-03:00) Brasilia'        , 
	'America/Godthab'              => '(UTC-03:00) Greenland'       , 
	'America/Montevideo'           => '(UTC-03:00) Montevideo'      , 
	'Atlantic/South_Georgia'       => '(UTC-02:00) Mid-Atlantic'    , 
	'Atlantic/Azores'              => '(UTC-01:00) Azores'          , 
	'Atlantic/Cape_Verde'          => '(UTC-01:00) Cape Verde Is.'  , 
	'Europe/London'                => '(UTC) London, Edinburgh, Dublin, Lisbon', 
	'UTC'                          => '(UTC) Coordinated Universal Time, Greenwich Mean Time', 
	'Africa/Monrovia'              => '(UTC) Monrovia, Reykjavik'   , 
	'Africa/Casablanca'            => '(UTC) Casablanca'            , 
	'Europe/Belgrade'              => '(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague', 
	'Europe/Sarajevo'              => '(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb', 
	'Europe/Brussels'              => '(UTC+01:00) Brussels, Copenhagen, Madrid, Paris', 
	'Africa/Algiers'               => '(UTC+01:00) West Central Africa', 
	'Europe/Amsterdam'             => '(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna', 
	'Europe/Minsk'                 => '(UTC+02:00) Minsk'           , 
	'Africa/Cairo'                 => '(UTC+02:00) Cairo'           , 
	'Europe/Helsinki'              => '(UTC+02:00) Helsinki, Riga, Sofia, Tallinn, Vilnius', 
	'Europe/Athens'                => '(UTC+02:00) Athens, Bucharest', 
	'Europe/Istanbul'              => '(UTC+02:00) Istanbul'        , 
	'Asia/Jerusalem'               => '(UTC+02:00) Jerusalem'       , 
	'Asia/Amman'                   => '(UTC+02:00) Amman'           , 
	'Asia/Beirut'                  => '(UTC+02:00) Beirut'          , 
	'Africa/Windhoek'              => '(UTC+02:00) Windhoek'        , 
	'Africa/Harare'                => '(UTC+02:00) Harare'          , 
	'Asia/Kuwait'                  => '(UTC+03:00) Kuwait, Riyadh'  , 
	'Asia/Baghdad'                 => '(UTC+03:00) Baghdad'         , 
	'Africa/Nairobi'               => '(UTC+03:00) Nairobi'         , 
	'Asia/Tehran'                  => '(UTC+03:30) Tehran'          , 
	'Asia/Tbilisi'                 => '(UTC+04:00) Tbilisi'         , 
	'Europe/Moscow'                => '(UTC+03:00) Moscow, Volgograd', 
	'Asia/Muscat'                  => '(UTC+04:00) Abu Dhabi, Muscat', 
	'Asia/Baku'                    => '(UTC+04:00) Baku'            , 
	'Asia/Yerevan'                 => '(UTC+04:00) Yerevan'         , 
	'Asia/Karachi'                 => '(UTC+05:00) Islamabad, Karachi', 
	'Asia/Tashkent'                => '(UTC+05:00) Tashkent'        , 
	'Asia/Kolkata'                 => '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi', 
	'Asia/Colombo'                 => '(UTC+05:30) Sri Jayawardenepura', 
	'Asia/Katmandu'                => '(UTC+05:45) Kathmandu'       , 
	'Asia/Dhaka'                   => '(UTC+06:00) Dhaka'           , 
	'Asia/Almaty'                  => '(UTC+06:00) Almaty'          , 
	'Asia/Yekaterinburg'           => '(UTC+06:00) Ekaterinburg'    , 
	'Asia/Rangoon'                 => '(UTC+06:30) Yangon (Rangoon)', 
	'Asia/Novosibirsk'             => '(UTC+07:00) Novosibirsk'     , 
	'Asia/Bangkok'                 => '(UTC+07:00) Bangkok, Jakarta', 
	'Asia/Brunei'                  => '(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi', 
	'Asia/Krasnoyarsk'             => '(UTC+08:00) Krasnoyarsk'     , 
	'Asia/Ulaanbaatar'             => '(UTC+08:00) Ulaan Bataar'    , 
	'Asia/Kuala_Lumpur'            => '(UTC+08:00) Kuala Lumpur, Singapore', 
	'Asia/Taipei'                  => '(UTC+08:00) Taipei'          , 
	'Australia/Perth'              => '(UTC+08:00) Perth'           , 
	'Asia/Irkutsk'                 => '(UTC+09:00) Irkutsk'         , 
	'Asia/Seoul'                   => '(UTC+09:00) Seoul'           , 
	'Asia/Tokyo'                   => '(UTC+09:00) Tokyo'           , 
	'Australia/Darwin'             => '(UTC+09:30) Darwin'          , 
	'Australia/Adelaide'           => '(UTC+09:30) Adelaide'        , 
	'Australia/Canberra'           => '(UTC+10:00) Canberra, Melbourne, Sydney', 
	'Australia/Brisbane'           => '(UTC+10:00) Brisbane'        , 
	'Australia/Hobart'             => '(UTC+10:00) Hobart'          , 
	'Asia/Vladivostok'             => '(UTC+10:00) Vladivostok'     , 
	'Pacific/Guam'                 => '(UTC+10:00) Guam, Port Moresby', 
	'Asia/Yakutsk'                 => '(UTC+10:00) Yakutsk'         , 
	'Etc/GMT-11'				   => '(UTC+11:00) Solomon Is., New Caledonia',
	'Pacific/Fiji'                 => '(UTC+12:00) Fiji'            , 
	'Asia/Kamchatka'               => '(UTC+12:00) Kamchatka'       , 
	'Pacific/Auckland'             => '(UTC+12:00) Auckland'        , 
	'Asia/Magadan'                 => '(UTC+12:00) Magadan'         , 
	'Pacific/Tongatapu'            => '(UTC+13:00) Nukualofa'       , 
	'Summary'                      => 'Summary'                     , // TODO: Review
	'Detail'                       => 'Detail'                      , // TODO: Review
	'LBL_USER_LIST_DETAILS'        => 'Details'                     , // TODO: Review
	'LBL_USER_DELETED_SUCCESSFULLY' => 'Utilizator șters cu succes',
    'LBL_ACTIVE_USERS' => 'Utilizatori activi',
    'LBL_INACTIVE_USERS' => 'Utilizatorii inactivi',
    'LBL_DELETE_USER_PERMANENTLY' => 'Sterge utilizator permanent',
    'LBL_RESTORE' => 'Restabili',
    'LBL_USER_RESTORED_SUCCESSFULLY' => 'Utilizatorul restaurat cu succes',
	'LBL_ALMOST_THERE'	=>	'Aproape acolo!',
	'LBL_ABOUT_ME'		=>	'Despre mine',
	'LBL_WE_PROMISE_TO_KEEP_THIS_PRIVATE'	=>	'(Am promisiunea de a menține acest privat)',
	'LBL_ALL_FIELDS_BELOW_ARE_REQUIRED'		=>	'(Toate campurile de mai jos sunt obligatorii)',
	'LBL_GET_STARTED'	=> 'Începeți',
	'LBL_YOUR_CONTACT_NUMBER' => 'Numărul dvs. de contact',
	'LBL_WHERE_ARE_YOU_FROM' =>	'De unde ești?',
	'LBL_SELECT_COUNTRY'	=> 'Selectați Țară',
	'LBL_COMPANY_SIZE'		=> 'Dimensiuni companie',
	'LBL_JOB_TITLE'			=> 'Denumirea postului',
	'LBL_DEPARTMENT'		=> 'Departament',
	'LBL_BASE_CURRENCY'		=> 'Moneda de bază',
	'LBL_CHOOSE_BASE_CURRENCY'	=> 'Alegeți Moneda de bază',
	'LBL_OPERATING_CURRENCY'	=> 'Valută de bază nu poate fi modificat ulterior. Selectați moneda dvs. de operare',
	'LBL_LANGUAGE' => 'Limbă',
	'LBL_CHOOSE_LANGUAGE'	=> 'Alegeți limba',
	'LBL_CHOOSE_TIMEZONE'	=> 'Alegeți Timezone',
	'LBL_DATE_FORMAT'		=> 'Format dată',
	'LBL_CHOOSE_DATE_FORMAT'=> 'Alegeți Format dată',
	'LBL_PHONE'	=> 'Telefon',
    'Space' => 'Spațiu',
	//picklist values for Default Calendar View field in MyPreference Page
	'ListView' => 'Vizualizare listă',
	'MyCalendar' => 'Calendarul meu',
	'SharedCalendar' => 'Calendar comun',
    
    'LBL_CHANGE_OWNER' => 'Schimbare Proprietar',
    'LBL_TRANSFER_OWNERSHIP' => 'Transfer de proprietate',
    'LBL_TRANSFER_OWNERSHIP_TO_USER' => 'Transfer de proprietate a utilizatorului',
    'LBL_OWNERSHIP_TRANSFERRED_SUCCESSFULLY' => 'CRM Proprietar schimbat cu succes',
    'LBL_OWNERSHIP_TRANSFERRED_FAILED' => 'Nu a reușit schimbarea proprietar CRM',
    'Account Owner' => 'Cont Proprietarul',
    'Starting Day of the week' => 'Zi a săptămânii',
    'Day starts at' => 'Ziua începe la',
    'Default Event Status' => 'Implicit Eveniment Stare',
    'Default Activity Type' => 'Implicit Tipul activității',
    'Default Record View' => 'Implicit Înregistrare Vezi',
    'Left Panel Hide' => 'Panou stânga Ascunde',
    'Row Height' => 'Row Înălțime',
	'LBL_RESTORE_USER_FAILED' => 'Nu a reușit pentru a restabili utilizator. Există deja un utilizator CRM cu acest nume de utilizator.',
    
    'LBL_DUPLICATE_USER_EXISTS' => 'Utilizatorul există deja',


	'LBL_CHANGE_USERNAME'          => 'Schimbare Utilizator'        ,
	'LBL_USERNAME_CHANGED'         => 'Utilizator schimbat cu succes',
	'ERROR_CHANGE_USERNAME'        => 'Eroare în schimbare nume de utilizator. Vă rugăm să încercați mai târziu',

  'LBL_REMOVE_USER' => 'Elimina',
  'LBL_MORE_OPTIONS' => 'Alte Opțiuni',
  'LBL_RESTORE_USER' => 'Repara De Utilizator',
  'LBL_OLD_PASSWORD' => 'Parola Veche',
  'LBL_CHANGE_PASSWORD' => 'Schimba Parola',
  'LBL_NEW_PASSWORD' => 'O Noua Parola',
  'LBL_CONFIRM_PASSWORD' => 'Confirmați Parola',
	'LBL_CHANGE_ACCESS_KEY' => 'Schimbare cheie de acces',
	'LBL_ACCESS_KEY_UPDATED_SUCCESSFULLY' => 'Cheia de acces a fost actualizat',
	'LBL_FAILED_TO_UPDATE_ACCESS_KEY' => 'Actualizarea nu a reușit cheie de acces',
  'LBL_LOGIN_AS' => 'Login ca ',
  'LBL_CREATE_USER' => 'A Crea Un Utilizator',
  'LBL_DELETE_USER_PERMANENTLY_INFO' => 'Ștergerea unui utilizator în mod constant va muta toate înregistrările, inclusiv comentariile și istorie de la un nou utilizator.',
  'LBL_TO_CRM' => 'Login voluntare rusă CRM',
  'LBL_INVALID_USER_OR_PASSWORD' => 'Greșit numele de utilizator sau parola.',
  'LBL_INVALID_USER_OR_EMAIL' => 'Greșit numele de utilizator sau adresa de e-mail.',
  'LBL_EMAIL_SEND' => 'Am trimis un e-mail pentru a reseta parola.',
  'ForgotPassword' => 'A Uitat Parola',
  'LBL_CONNECT_WITH_US' => 'Contactati-ne',
  'LBL_GET_MORE' => 'Obțineți mai mult de la lupte libere rusă',
  'LBL_TRANSFER_RECORDS_TO_USER' => 'Transferul de înregistrări de utilizator',
  'LBL_USER_TO_BE_DELETED' => 'Utilizator șters',
  'LBL_USERS_SETTINGS' => 'SETĂRILE DE UTILIZATOR',
  'LBL_TEMPLATES' => 'Template-uri',

);
$jsLanguageStrings = array(
		
	//Curency seperator validation messages
	'JS_ENTER_OLD_PASSWORD'=>'Please enter your old password.',
	'JS_ENTER_NEW_PASSWORD'=>'Please enter your new password.',
	'JS_ENTER_CONFIRMATION_PASSWORD'=>'Please enter your password confirmation.',
	'JS_REENTER_PASSWORDS'=>'Please re-enter passwords.  The \"new password\" and \"confirm password\" values do not match.',
	'JS_INVALID_PASSWORD'=>'You must specify a valid username and password.',
	'JS_PASSWORD_CHANGE_FAILED_1'=>'User password change failed for ',
	'JS_PASSWORD_CHANGE_FAILED_2'=>' failed.  The new password must be set.',
	'JS_PASSWORD_INCORRECT_OLD'=>'Incorrect old password specified. Re-enter password information.',
	'JS_ENTERED_CURRENT_USERNAME_MSG' => 'Ați introdus numele de utilizator curent. Vă rugăm introduceți numele de utilizator nou.',
	'JS_NEW_ACCESS_KEY_REQUESTED' => 'Tasta de acces nou solicitat',
	'JS_CHANGE_ACCESS_KEY_CONFIRMATION' => 'Ați solicitat o nouă cheie de acces. &lt;br&gt;&lt;br&gt;cu acces noua prevedere cheie, trebuie să înlocuiți cheia de acces vechi cu unul nou, în toate extensiile instalate. &lt;br&gt;&lt;br&gt;Do doriți să continuați?',
);