<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'Quotes'=>'Quotes',
	//DetailView Actions
	'SINGLE_Quotes' => 'Quote',
	'LBL_EXPORT_TO_PDF' => 'Export to PDF',
	'LBL_SEND_MAIL_PDF' => 'Send Email with PDF',

	//Basic strings
	'LBL_ADD_RECORD' => 'Add Quote',
	'LBL_RECORDS_LIST' => 'Quotes List',

	// Blocks
	'LBL_QUOTE_INFORMATION' => 'Quote Details',

	//Field Labels
	'Quote No'=>'Quote Number',
	'Quote Stage'=>'Quote Stage',
	'Valid Till'=>'Valid Until',
	'Inventory Manager'=>'Inventory Manager',
	'Related To' => 'Related To',
	//Added for existing Picklist Entries

	'Accepted'=>'Accepted',
	'Rejected'=>'Rejected',

	//Translation for product not found
	'LBL_THIS' => 'This',
	'LBL_IS_DELETED_FROM_THE_SYSTEM_PLEASE_REMOVE_OR_REPLACE_THIS_ITEM' => 'is deleted from the system.please remove or replace this item',
	'LBL_THIS_LINE_ITEM_IS_DELETED_FROM_THE_SYSTEM_PLEASE_REMOVE_THIS_LINE_ITEM' => 'This line item is deleted from the system,please remove this line items',

);

$jsLanguageStrings = array(
	'JS_PLEASE_REMOVE_LINE_ITEM_THAT_IS_DELETED' => 'Please remove line item that is deleted',
);
