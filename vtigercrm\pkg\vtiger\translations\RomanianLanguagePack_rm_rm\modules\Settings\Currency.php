<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD'               => 'Add Currency'                , // TODO: Review
	'LBL_EDIT_CURRENCY'            => 'Edit Currency'               , // TODO: Review
	'LBL_ADD_NEW_CURRENCY'         => 'Add New Currency'            , // TODO: Review
	'LBL_CURRENCY_NAME'            => 'Currency Name'               , // TODO: Review
	'LBL_CURRENCY_CODE'            => 'Currency Code'               , // TODO: Review
	'LBL_CURRENCY_SYMBOL'          => 'Symbol'                      , // TODO: Review
	'LBL_CONVERSION_RATE'          => 'Conversion Rate'             , // TODO: Review
	'LBL_ENTER_CONVERSION_RATE'    => 'Enter Conversion Rate'       , // TODO: Review
	'LBL_CURRENCY_STATUS_DESC'     => 'Enable checkbox to make currency Active', // TODO: Review
	'LBL_TRANSFER_CURRENCY'        => 'Transfer Currency'           , // TODO: Review
	'LBL_CURRENT_CURRENCY'         => 'Current Currency'            , // TODO: Review
	'Albania, Leke'                => 'Albania, Leke'               , // TODO: Review
	'Argentina, Pesos'             => 'Argentina, Pesos'            , // TODO: Review
	'Aruba, Guilders'              => 'Aruba, Guilders'             , // TODO: Review
	'Australia, Dollars'           => 'Australia, Dollars'          , // TODO: Review
	'Azerbaijan, New Manats'       => 'Azerbaijan, New Manats'      , // TODO: Review
	'Bahamas, Dollars'             => 'Bahamas, Dollars'            , // TODO: Review
	'Bahrain, Dinar'               => 'Bahrain, Dinar'              , // TODO: Review
	'Barbados, Dollars'            => 'Barbados, Dollars'           , // TODO: Review
	'Belarus, Rubles'              => 'Belarus, Rubles'             , // TODO: Review
	'Belize, Dollars'              => 'Belize, Dollars'             , // TODO: Review
	'Bermuda, Dollars'             => 'Bermuda, Dollars'            , // TODO: Review
	'Bolivia, Bolivianos'          => 'Bolivia, Bolivianos'         , // TODO: Review
	'Convertible Marka'            => 'Convertible Marka'           , // TODO: Review
	'Botswana, Pulas'              => 'Botswana, Pulas'             , // TODO: Review
	'Bulgaria, Leva'               => 'Bulgaria, Leva'              , // TODO: Review
	'Brazil, Reais'                => 'Brazil, Reais'               , // TODO: Review
	'Great Britain Pounds'         => 'Great Britain Pounds'        , // TODO: Review
	'Brunei Darussalam, Dollars'   => 'Brunei Darussalam, Dollars'  , // TODO: Review
	'Canada, Dollars'              => 'Canada, Dollars'             , // TODO: Review
	'Cayman Islands, Dollars'      => 'Cayman Islands, Dollars'     , // TODO: Review
	'Chile, Pesos'                 => 'Chile, Pesos'                , // TODO: Review
	'Colombia, Pesos'              => 'Colombia, Pesos'             , // TODO: Review
	'Costa Rica, Colón'           => 'Costa Rica, Colón'          , // TODO: Review
	'Croatia, Kuna'                => 'Croatia, Kuna'               , // TODO: Review
	'Cuba, Pesos'                  => 'Cuba, Pesos'                 , // TODO: Review
	'Cyprus, Pounds'               => 'Cyprus, Pounds'              , // TODO: Review
	'Czech Republic, Koruny'       => 'Czech Republic, Koruny'      , // TODO: Review
	'Denmark, Kroner'              => 'Denmark, Kroner'             , // TODO: Review
	'Dominican Republic, Pesos'    => 'Dominican Republic, Pesos'   , // TODO: Review
	'East Caribbean, Dollars'      => 'East Caribbean, Dollars'     , // TODO: Review
	'Egypt, Pounds'                => 'Egypt, Pounds'               , // TODO: Review
	'El Salvador, Colón'          => 'El Salvador, Colón'         , // TODO: Review
	'England, Pounds'              => 'England, Pounds'             , // TODO: Review
	'Estonia, Krooni'              => 'Estonia, Krooni'             , // TODO: Review
	'Euro'                         => 'Euro'                        , // TODO: Review
	'Falkland Islands, Pounds'     => 'Falkland Islands, Pounds'    , // TODO: Review
	'Fiji, Dollars'                => 'Fiji, Dollars'               , // TODO: Review
	'Ghana, Cedis'                 => 'Ghana, Cedis'                , // TODO: Review
	'Gibraltar, Pounds'            => 'Gibraltar, Pounds'           , // TODO: Review
	'Guatemala, Quetzales'         => 'Guatemala, Quetzales'        , // TODO: Review
	'Guernsey, Pounds'             => 'Guernsey, Pounds'            , // TODO: Review
	'Guyana, Dollars'              => 'Guyana, Dollars'             , // TODO: Review
	'Honduras, Lempiras'           => 'Honduras, Lempiras'          , // TODO: Review
	'LvHong Kong, Dollars '        => 'LvHong Kong, Dollars '       , // TODO: Review
	'Hungary, Forint'              => 'Hungary, Forint'             , // TODO: Review
	'Iceland, Krona'               => 'Iceland, Krona'              , // TODO: Review
	'India, Rupees'                => 'India, Rupees'               , // TODO: Review
	'Indonesia, Rupiahs'           => 'Indonesia, Rupiahs'          , // TODO: Review
	'Iran, Rials'                  => 'Iran, Rials'                 , // TODO: Review
	'Isle of Man, Pounds'          => 'Isle of Man, Pounds'         , // TODO: Review
	'Israel, New Shekels'          => 'Israel, New Shekels'         , // TODO: Review
	'Jamaica, Dollars'             => 'Jamaica, Dollars'            , // TODO: Review
	'Japan, Yen'                   => 'Japan, Yen'                  , // TODO: Review
	'Jersey, Pounds'               => 'Jersey, Pounds'              , // TODO: Review
	'Kazakhstan, Tenge'            => 'Kazakhstan, Tenge'           , // TODO: Review
	'Korea (North), Won'           => 'Korea (North), Won'          , // TODO: Review
	'Korea (South), Won'           => 'Korea (South), Won'          , // TODO: Review
	'Kyrgyzstan, Soms'             => 'Kyrgyzstan, Soms'            , // TODO: Review
	'Laos, Kips'                   => 'Laos, Kips'                  , // TODO: Review
	'Latvia, Lati'                 => 'Latvia, Lati'                , // TODO: Review
	'Lebanon, Pounds'              => 'Lebanon, Pounds'             , // TODO: Review
	'Liberia, Dollars'             => 'Liberia, Dollars'            , // TODO: Review
	'Switzerland Francs'           => 'Switzerland Francs'          , // TODO: Review
	'Lithuania, Litai'             => 'Lithuania, Litai'            , // TODO: Review
	'Macedonia, Denars'            => 'Macedonia, Denars'           , // TODO: Review
	'Malaysia, Ringgits'           => 'Malaysia, Ringgits'          , // TODO: Review
	'Malta, Liri'                  => 'Malta, Liri'                 , // TODO: Review
	'Mauritius, Rupees'            => 'Mauritius, Rupees'           , // TODO: Review
	'Mexico, Pesos'                => 'Mexico, Pesos'               , // TODO: Review
	'Mongolia, Tugriks'            => 'Mongolia, Tugriks'           , // TODO: Review
	'Mozambique, Meticais'         => 'Mozambique, Meticais'        , // TODO: Review
	'Namibia, Dollars'             => 'Namibia, Dollars'            , // TODO: Review
	'Nepal, Rupees'                => 'Nepal, Rupees'               , // TODO: Review
	'Netherlands Antilles, Guilders' => 'Netherlands Antilles, Guilders', // TODO: Review
	'New Zealand, Dollars'         => 'New Zealand, Dollars'        , // TODO: Review
	'Nicaragua, Cordobas'          => 'Nicaragua, Cordobas'         , // TODO: Review
	'Nigeria, Nairas'              => 'Nigeria, Nairas'             , // TODO: Review
	'North Korea, Won'             => 'North Korea, Won'            , // TODO: Review
	'Norway, Krone'                => 'Norway, Krone'               , // TODO: Review
	'Oman, Rials'                  => 'Oman, Rials'                 , // TODO: Review
	'Pakistan, Rupees'             => 'Pakistan, Rupees'            , // TODO: Review
	'Panama, Balboa'               => 'Panama, Balboa'              , // TODO: Review
	'Paraguay, Guarani'            => 'Paraguay, Guarani'           , // TODO: Review
	'Peru, Nuevos Soles'           => 'Peru, Nuevos Soles'          , // TODO: Review
	'Philippines, Pesos'           => 'Philippines, Pesos'          , // TODO: Review
	'Poland, Zlotych'              => 'Poland, Zlotych'             , // TODO: Review
	'Qatar, Rials'                 => 'Qatar, Rials'                , // TODO: Review
	'Romania, New Lei'             => 'Romania, New Lei'            , // TODO: Review
	'Russia, Rubles'               => 'Russia, Rubles'              , // TODO: Review
	'Saint Helena, Pounds'         => 'Saint Helena, Pounds'        , // TODO: Review
	'Saudi Arabia, Riyals'         => 'Saudi Arabia, Riyals'        , // TODO: Review
	'Serbia, Dinars'               => 'Serbia, Dinars'              , // TODO: Review
	'Seychelles, Rupees'           => 'Seychelles, Rupees'          , // TODO: Review
	'Singapore, Dollars'           => 'Singapore, Dollars'          , // TODO: Review
	'Solomon Islands, Dollars'     => 'Solomon Islands, Dollars'    , // TODO: Review
	'Somalia, Shillings'           => 'Somalia, Shillings'          , // TODO: Review
	'South Africa, Rand'           => 'South Africa, Rand'          , // TODO: Review
	'South Korea, Won'             => 'South Korea, Won'            , // TODO: Review
	'Sri Lanka, Rupees'            => 'Sri Lanka, Rupees'           , // TODO: Review
	'Sweden, Kronor'               => 'Sweden, Kronor'              , // TODO: Review
	'Switzerland, Francs'          => 'Switzerland, Francs'         , // TODO: Review
	'Suriname, Dollars'            => 'Suriname, Dollars'           , // TODO: Review
	'Syria, Pounds'                => 'Syria, Pounds'               , // TODO: Review
	'Taiwan, New Dollars'          => 'Taiwan, New Dollars'         , // TODO: Review
	'Thailand, Baht'               => 'Thailand, Baht'              , // TODO: Review
	'Trinidad and Tobago, Dollars' => 'Trinidad and Tobago, Dollars', // TODO: Review
	'Turkey, New Lira'             => 'Turkey, New Lira'            , // TODO: Review
	'Turkey, Liras'                => 'Turkey, Liras'               , // TODO: Review
	'Tuvalu, Dollars'              => 'Tuvalu, Dollars'             , // TODO: Review
	'Ukraine, Hryvnia'             => 'Ukraine, Hryvnia'            , // TODO: Review
	'United Kingdom, Pounds'       => 'United Kingdom, Pounds'      , // TODO: Review
	'USA, Dollars'                 => 'USA, Dollars'                , // TODO: Review
	'Uruguay, Pesos'               => 'Uruguay, Pesos'              , // TODO: Review
	'Uzbekistan, Sums'             => 'Uzbekistan, Sums'            , // TODO: Review
	'Venezuela, Bolivares Fuertes' => 'Venezuela, Bolivares Fuertes', // TODO: Review
	'Vietnam, Dong'                => 'Vietnam, Dong'               , // TODO: Review
	'Zimbabwe Dollars'             => 'Zimbabwe Dollars'            , // TODO: Review
	'China, Yuan Renminbi'         => 'China, Yuan Renminbi'        , // TODO: Review
	'Afghanistan, Afghanis'        => 'Afghanistan, Afghanis'       , // TODO: Review
	'Cambodia, Riels'              => 'Cambodia, Riels'             , // TODO: Review
	'Jordan, Dinar'                => 'Jordan, Dinar'               , // TODO: Review
	'Kenya, Shilling'              => 'Kenya, Shilling'             , // TODO: Review
	'MADAGASCAR, Malagasy Ariary'  => 'MADAGASCAR, Malagasy Ariary' , // TODO: Review
	'United Arab Emirates, Dirham' => 'United Arab Emirates, Dirham', // TODO: Review
	'United Republic of Tanzania, Shilling' => 'United Republic OF Tanzania, Shilling', // TODO: Review
	'Yemen, Rials'                 => 'Yemen, Rials'                , // TODO: Review
	'Zambia, Kwacha'               => 'Zambia, Kwacha'              , // TODO: Review
	'Malawi, kwacha'               => 'Malawi, kwacha'              , // TODO: Review
	'Tunisian, Dinar'              => 'Tunisian, Dinar'             , // TODO: Review
	'Moroccan, Dirham'             => 'Moroccan, Dirham'            , // TODO: Review
);
$jsLanguageStrings = array(
	'JS_CURRENCY_DETAILS_SAVED'    => 'Currency Details Saved'      , // TODO: Review
	'JS_CURRENCY_DELETED_SUEESSFULLY' => 'Currency Deleted Successfully', // TODO: Review
);