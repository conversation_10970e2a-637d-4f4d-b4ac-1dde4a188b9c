<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is: vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'SINGLE_Emails' => 'E-post',
	'E-post' => 'E-post',
	'LBL_SELECT_EMAIL_IDS' => 'Välj E-postadress',
	'LBL_SUBJECT' => 'Ämne',
	'LBL_ATTACHMENT' => 'Bilagor',
	'LBL_BROWSE_CRM' => 'Bläddra CRM',
	'LBL_SEND' => 'Skicka',
	'LBL_SAVE_AS_DRAFT' => 'Spara som Utkast',
	'LBL_GO_TO_PREVIEW' => 'Gå till Förhandsgranska',
	'LBL_SELECT_EMAIL_TEMPLATE' => 'Välj E-postmall',
	'LBL_COMPOSE_EMAIL' => 'Skapa E-post',
	'LBL_TO' => 'Till',
 	'LBL_CC' => 'Cc',
   	'LBL_BCC' => 'Bcc',
   	'LBL_ADD_CC' => 'Lägg till Cc',
   	'LBL_ADD_BCC' => 'Lägg till Bcc',
	'LBL_MAX_UPLOAD_SIZE' => 'Maximal uppladdningsbar filstorlek är',
	'LBL_EXCEEDED' => 'Överskriden',
    'LBL_EMAILTEMPLATE_WARNING'    => 'Är dina merge-taggar korrigera',
    'LBL_EMAILTEMPLATE_WARNING_CONTENT' => 'Se till att den mall du valt har fusionera-taggar är relevanta för mottagaren rekord. 
                                            Om du skickar ett mail till leda, men kopplings-taggar tillhör Kontaktmodul (ex: $contacts-lastname$), 
                                            då värdena inte kommer att slås samman.',    
	
	//Button Namns translation
	'LBL_FORWARD' => 'Vidarebefordra',
	'LBL_PRINT' => 'Skriv ut',
	'LBL_DESCRIPTION' => 'Beskrivning',
	'LBL_FROM' => 'Från',
	'LBL_INFO' => 'Info',
	'LBL_DRAFTED_ON' => 'Utkast på',
	'LBL_SENT_ON' => 'Vidarebefordra',
	'LBL_OWNER' => 'Ägare',
	
	'Datum & Time Sent' => 'Datum Skickat',
    'LBL_EMAIL_INFORMATION' => 'Email Information',
	'Draft'=>'Utkast',
    'Parent ID' => 'Förälder Record',

  'Emails' => 'E-post',
  'Date & Time Sent' => 'Datum Skickas',
  'Time Start' => 'Tid Skickas',

);

$jsLanguageStrings = array(
    'JS_WARNING' => 'varning',
);  