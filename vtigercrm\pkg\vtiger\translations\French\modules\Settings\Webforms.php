<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'SINGLE_Settings:Webforms'     => 'Webform'                     , // TODO: Review
	'WebForm Name'                 => 'Webform Name'                , // TODO: Review
	'Public Id'                    => 'Public Id'                   , // TODO: Review
	'Enabled'                      => 'Status'                      , // TODO: Review
	'Module'                       => 'Module'                      , // TODO: Review
	'Return Url'                   => 'Return Url'                  , // TODO: Review
	'Post Url'                     => 'Post Url'                    , // TODO: Review
    'Captcha Enabled'              => 'Captcha activé'              ,
	'SINGLE_Webforms'              => 'Webform'                     , // TODO: Review
	'LBL_SHOW_FORM'                => 'Show Form'                   , // TODO: Review
	'LBL_DUPLICATES_EXIST'         => 'Webform Name already exists' , // TODO: Review
	'LBL_WEBFORM_INFORMATION'      => 'Webform Information'         , // TODO: Review
	'LBL_FIELD_INFORMATION'        => 'Field Information'           , // TODO: Review
	'LBL_FIELD_NAME'               => 'Field Name'                  , // TODO: Review
	'LBL_OVERRIDE_VALUE'           => 'Override Value'              , // TODO: Review
	'LBL_MANDATORY'                => 'Mandatory'                   , // TODO: Review
	'LBL_WEBFORM_REFERENCE_FIELD'  => 'Webforms reference Field'    , // TODO: Review
	'LBL_SELECT_FIELDS_OF_TARGET_MODULE' => 'Select Fields for Target Module...', // TODO: Review
	'LBL_ALLOWS_YOU_TO_MANAGE_WEBFORMS' => 'Allows you to manage webforms', // TODO: Review
	'LBL_ADD_FIELDS'               => 'Add Fields'                  , // TODO: Review
	'LBL_EMBED_THE_FOLLOWING_FORM_IN_YOUR_WEBSITE' => 'Embed the following form in your website', // TODO: Review
	'LBL_SELECT_VALUE'             => 'Select Value'                , // TODO: Review
	'LBL_LABEL'                    => 'label'                       , // TODO: Review
	'LBL_SAVE_FIELDS_ORDER' => 'Enregistrer ordre des champs', 
	'LBL_HIDDEN' => 'Caché',
	'LBL_ENABLE_TARGET_MODULES_FOR_WEBFORM' => 'Activer modules cibles pour formulaire en ligne',
	'LBL_ASSIGN_USERS' => 'Affectation de l\'utilisateur',
    'LBL_ASSIGN_ROUND_ROBIN' => 'Affecter des utilisateurs en Round Robin',
    'LBL_ROUNDROBIN_USERS_LIST' => 'Round Robin Liste des utilisateurs',

  'LBL_ADD_RECORD' => 'Ajouter Webform',

	'LBL_UPLOAD_DOCUMENTS' => 'Documents Téléchargement',
	'LBL_ADD_FILE_FIELD' => 'File Upload champs',
	'LBL_FIELD_LABEL' => 'Titre du document',
	'LBL_FILE_FIELD_INFO' => 'Pour chaque fichier téléchargé du web forment un nouveau document est créé avec le fichier joint. Le document est également lié à ce nouveau%s.',
	'LBL_NO_FILE_FIELD' => 'Aucun champ de fichier ajouté.',
	'LBL_COPY_TO_CLIPBOARD' => 'Copier dans le presse-papier',
);
$jsLanguageStrings = array(
	'JS_WEBFORM_DELETED_SUCCESSFULLY' => 'Webform deleted successfully', // TODO: Review
	'JS_LOADING_TARGET_MODULE_FIELDS' => 'Loadding Target Module Fields', // TODO: Review
	'JS_SELECT_VALUE'              => 'Select Vlaue'                , // TODO: Review
	'JS_MANDATORY_FIELDS_WITHOUT_OVERRIDE_VALUE_CANT_BE_HIDDEN' => 'Les champs obligatoires sans valeurs de remplacement ne peux pas être cachés',
	'JS_REFERENCE_FIELDS_CANT_BE_MANDATORY_WITHOUT_OVERRIDE_VALUE' => 'champs de référence ne peux pas être obligatoire sans valeur de remplacement',
	'JS_TYPE_TO_SEARCH' => 'Tapez à la recherche',
	"JS_WEBFORM_WITH_THIS_NAME_ALREADY_EXISTS" => 'Webform avec ce nom existe déjà',

  'JS_SELECT_AN_OPTION' => 'Sélectionnez une Option',
  'JS_LABEL' => 'étiquette',

	'JS_MAX_FILE_FIELDS_LIMIT' => 'Maximum vous pouvez ajouter des champs de fichiers%.',
	'JS_COPIED_SUCCESSFULLY' => 'Copié avec succès.',
	'JS_COPY_FAILED' => "Échec de la copie. S'il vous plaît copier manuellement.",
);