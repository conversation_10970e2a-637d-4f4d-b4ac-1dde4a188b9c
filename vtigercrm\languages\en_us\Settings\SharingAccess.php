<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
    'SharingAccess' => 'Sharing Rules',
	'Accounts' => 'Organizations & Contacts',
	'LBL_ADD_CUSTOM_RULE' => 'Add Custom Rule',
	'Read Only' => 'Read Only',
	'Read Write' => 'Read/Write',
	'LBL_ADD_CUSTOM_RULE_TO' => 'Add Custom Rule to',
	'LBL_CAN_ACCESSED_BY' => 'Can be accessed by',
	'LBL_PRIVILEGES' => 'Privileges',
	'LBL_SHARING_RULE' =>  'Sharing Rules',
	'LBL_RULE_NO' => 'Rule Number',
	'LBL_MODULE' => 'Module',
	'LBL_ADVANCED_SHARING_RULES' => 'Advanced Sharing Rules',
	'LBL_WITH_PERMISSIONS' => 'With Permissions',
	'LBL_APPLY_NEW_SHARING_RULES' => 'Apply New Sharing Rules',
	'LBL_READ' => 'Read',
	'LBL_READ_WRITE' => 'Read and Write',
	'LBL_CUSTOM_ACCESS_MESG' => 'No Custom Access Rules defined',
	'SINGLE_Groups' => 'Group',
	'SINGLE_Roles' => 'Role',
	'SINGLE_RoleAndSubordinates' => 'RoleAndSubordinate',
);

$jsLanguageStrings = array(
	'JS_CUSTOM_RULE_SAVED_SUCCESSFULLY' => 'Custom Sharing Rule Saved Successfully',
	'JS_SELECT_ANY_OTHER_ACCESSING_USER' => 'Select any other accessing user',
	'JS_NEW_SHARING_RULES_APPLIED_SUCCESSFULLY' => 'New Sharing Rules Applied Successfully',
	'JS_DEPENDENT_PRIVILEGES_SHOULD_CHANGE' => 'Opportunities, Tickets, Quotes, SalesOrder & Invoice Access must be set to Private when the Organization Access is set to Private',
);