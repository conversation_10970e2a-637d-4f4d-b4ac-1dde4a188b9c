<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_TRANSFORM_OWNERSHIP'      => 'Transfer ownership'          , 
	'SINGLE_Groups'                => 'Group'                       , // TODO: Review
	'LBL_TO_OTHER_GROUP'           => 'To Other Group '             , 
	'LBL_ADD_RECORD'               => 'Add Group'                   , // TODO: Review
	'LBL_GROUP_NAME'               => 'Group Name'                  , // TODO: Review
	'LBL_GROUP_MEMBERS'            => 'Group Members'               , // TODO: Review
	'LBL_ADD_USERS_ROLES'          => 'Add Users, Roles...'         , // TODO: Review
	'LBL_ROLEANDSUBORDINATE'       => 'Role and Subordinates'       , // TODO: Review
	'RoleAndSubordinates'          => 'Role and Subordinates'       , // TODO: Review

  'LBL_DUPLICATES_EXIST' => 'Grup cu acest nume există deja',

);
$jsLanguageStrings = array(
	'JS_PLEASE_SELECT_ATLEAST_ONE_MEMBER_FOR_A_GROUP' => 'Please select atleast one member for a group', // TODO: Review
	'JS_RECORD_DELETED_SUCCESSFULLY' => 'Group deleted successfully'  , // TODO: Review
	'JS_COMMA_NOT_ALLOWED_GROUP' => 'Caractere speciale cum ar fi ,"<> nu sunt permise în numele grupului.',
);