<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
    'PurchaseOrder' => 'Purchase Orders',
	//DetailView Actions
	'SINGLE_PurchaseOrder' => 'Purchase Order',
	'LBL_EXPORT_TO_PDF' => 'Export to PDF',
    'LBL_SEND_MAIL_PDF' => 'Send Email with PDF',

	//Basic strings
	'LBL_ADD_RECORD' => 'Add Purchase Order',
	'LBL_RECORDS_LIST' => 'Purchase Order List',
	'LBL_COPY_SHIPPING_ADDRESS' => 'Copy Shipping Address',
	'LBL_COPY_BILLING_ADDRESS' => 'Copy Billing Address',

	// Blocks
	'LBL_PO_INFORMATION' => 'Purchase Order Details',

	//Field Labels
	'PurchaseOrder No' => 'Purchase Order Number',
	'Requisition No' => 'Requisition Number',
	'Tracking Number' => 'Tracking Number',
	'Sales Commission' => 'Sales Commission',
    'LBL_PAID' => 'Paid',
    'LBL_BALANCE' => 'Balance',

	//Added for existing Picklist Entries

	'Received Shipment'=>'Received Shipment',
	
	//Translation for product not found
	'LBL_THIS' => 'This',
	'LBL_IS_DELETED_FROM_THE_SYSTEM_PLEASE_REMOVE_OR_REPLACE_THIS_ITEM' => 'is deleted from the system.please remove or replace this item',
	'LBL_THIS_LINE_ITEM_IS_DELETED_FROM_THE_SYSTEM_PLEASE_REMOVE_THIS_LINE_ITEM' => 'This line item is deleted from the system,please remove this line items',
        'LBL_LIST_PRICE'               => 'List Price',
        'List Price'                   => 'List Price',
    
    'LBL_COPY_COMPANY_ADDRESS' => 'Copy Company Address',
    'LBL_COPY_ACCOUNT_ADDRESS' => 'Copy Organization Address',
	'LBL_SELECT_ADDRESS_OPTION' => 'Select Address to copy',
	'LBL_BILLING_ADDRESS' => 'Billing Address',
	'LBL_COMPANY_ADDRESS' => 'Company Address',
	'LBL_ACCOUNT_ADDRESS' => 'Organization Address',
	'LBL_VENDOR_ADDRESS' => 'Vendor Address',
	'LBL_CONTACT_ADDRESS' => 'Contact Address'
	
);

$jsLanguageStrings = array(
	'JS_PLEASE_REMOVE_LINE_ITEM_THAT_IS_DELETED' => 'Please remove line item that is deleted',
    'JS_ORGANIZATION_NOT_FOUND'=> 'Organization empty!',
    'JS_ORGANIZATION_NOT_FOUND_MESSAGE'=> 'Please select an organization before you copy address',
	'JS_ACCOUNT_NOT_FOUND' => 'Organization empty!',
	'JS_ACCOUNT_NOT_FOUND_MESSAGE' =>  'Please select an organization before you copy address',
	'JS_VENDOR_NOT_FOUND' => 'Vendor Empty', 
	'JS_VENDOR_NOT_FOUND_MESSAGE' => 'Please select an vendor before you copy address',
	'JS_CONTACT_NOT_FOUND' => 'Contact Empty', 
	'JS_CONTACT_NOT_FOUND_MESSAGE' =>  'Please select an contact before you copy address',
);
