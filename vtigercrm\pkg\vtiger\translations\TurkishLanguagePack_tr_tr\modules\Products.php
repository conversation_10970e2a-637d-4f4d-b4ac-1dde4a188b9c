<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'Products'                     => 'Ürünler'                   , 
	'SINGLE_Products'              => 'Ürün'                      , 
	'LBL_ADD_RECORD'               => 'Ürün <PERSON>kle'                 , 
	'LBL_RECORDS_LIST'             => 'Products List'               , 
	'LBL_PRODUCT_INFORMATION'      => 'Ürün Bilgisi'              , 
	'LBL_IMAGE_INFORMATION'        => 'Ürün resim bilgisi:'       , 
	'LBL_STOCK_INFORMATION'        => 'Stok Bilgisi:'               , 
	'LBL_MORE_CURRENCIES'          => 'başa dövizler'             , 
	'LBL_PRICES'                   => 'Ürün Fiyatları'           , 
	'LBL_PRICE'                    => 'Fiyat'                       , 
	'LBL_RESET_PRICE'              => 'Fiyatı Temizle'             , 
	'LBL_RESET'                    => 'Temizle'                     , 
	'LBL_ADD_TO_PRICEBOOKS'        => 'Add to PriceBooks'           , 
	'Product No'                   => 'Ürün No'                   , 
	'Part Number'                  => 'Parça Numarası'            , 
	'Product Active'               => 'Aktif'                       , 
	'Manufacturer'                 => 'Üretici'                    , 
	'Product Category'             => 'Ürün Kategorisi'           , 
	'Website'                      => 'Website'                     , 
	'Mfr PartNo'                   => 'Üretim Kodu'                , 
	'Vendor PartNo'                => 'Üretici Kodu'               , 
	'Usage Unit'                   => 'Kullanilan Birim'            , 
	'Handler'                      => 'Satış Temsilcisi'          , 
	'Reorder Level'                => 'Yeniden Siparis Seviyesi'    , 
	'Tax Class'                    => 'Vergi Sınıfı'             , 
	'Serial No'                    => 'Seri No'                     , 
	'Qty In Stock'                 => 'Stok'                        , 
	'Product Sheet'                => 'Ürün Sayfasi'              , 
	'Qty In Demand'                => 'Talep Miktari'               , 
	'GL Account'                   => 'Statü'                      , 
	'Product Image'                => 'Ürün Resmi'                , 
	'Unit Price'                   => 'Birim Fiyatı'               , 
	'Commission Rate'              => 'Komisyon Oranı (%)'         , 
	'Qty/Unit'                     => 'Miktar/Birim'                , 
	'--None--'                     => '--Hiç Biri--'               , 
	'Hardware'                     => 'Doanım'                     , 
	'Software'                     => 'Yazılım'                   , 
	'CRM Applications'             => 'CRM'                         , 
	'300-Sales-Software'           => '300-Sales-Software'          , 
	'301-Sales-Hardware'           => '301-Sales-Hardware'          , 
	'302-Rental-Income'            => '302-Rental-Income'           , 
	'303-Interest-Income'          => '303-Interest-Income'         , 
	'304-Sales-Software-Support'   => '304-Sales-Software-Support'  , 
	'305-Sales Other'              => '305-Sales Other'             , 
	'306-Internet Sales'           => '306-Internet Sales'          , 
	'307-Service-Hardware Labor'   => '307-Service-Hardware Labor'  , 
	'308-Sales-Books'              => '308-Sales-Books'             , 
	'Box'                          => 'Kutu'                        , 
	'Carton'                       => 'Karton'                      , 
	'Caton'                        => 'Caton'                       , 
	'Dozen'                        => 'Dazon'                       , 
	'Each'                         => 'Her biri'                    , 
	'Hours'                        => 'Saat'                        , 
	'Impressions'                  => 'Impressions'                 , 
	'Lb'                           => 'Lb'                          , 
	'M'                            => 'M'                           , 
	'Pack'                         => 'Paket'                       , 
	'Pages'                        => 'Sayfa'                       , 
	'Pieces'                       => 'Adet'                        , 
	'Reams'                        => 'Reams'                       , 
	'Sheet'                        => 'Sayfa'                       , 
	'Spiral Binder'                => 'Spiral Binder'               , 
	'Sq Ft'                        => 'Sq Ft'                       , 
	'LBL_CONVERSION_RATE'          => 'Conversion Rate'             , // TODO: Review
    'LBL_PRODUCTSMOD_DISABLED'     => 'Ürünleri görmek için Ürünler Modülü etkinleştirin',
	'LBL_SHOW_BUNDLE_IN_INVENTORY' => 'Fatura/Kurlar/Satış Sipariş/Satın Alma Emri paket ürün göster',
	'LBL_BUNDLE_TOTAL_COST' => 'Bundle toplam maliyeti',
	'LBL_UPDATE_BUNDLE_PRICE' => 'Güncelleme Paketi Fiyatı',

  'LBL_PRODUCT_NAME' => 'Ürün İsmi',
  'LBL_PRICE_QUANTITY' => 'Fiyat x Miktar',

);

$jsLanguageStrings = array(
	'JS_SUB_PRODUCTS_WILL_BE_SHOWN_IN_INVENTORY' => 'Alt ürünler envanterine gösterilir',
	'JS_SUB_PRODUCTS_WILL_NOT_BE_SHOWN_IN_INVENTORY' => 'Alt ürünler envanterine gösterilmeyecek',
	'JS_SUCCESSFULLY_CHANGED_BUNDLE_COST' => 'Demetinin başarıyla değiştirildi toplam maliyeti',
	'JS_DELETION_OR_IN_ACTIVATION_CHILD_PRODUCT_MESSAGE' => 'Bu ürün, bir ürün demetinin bir parçasıdır. Bir demet öğesi silinmiş veya inaktive olduğunda paket fiyatı değişmeden kalır. Devam etmek istediğinizden emin misiniz?',
);