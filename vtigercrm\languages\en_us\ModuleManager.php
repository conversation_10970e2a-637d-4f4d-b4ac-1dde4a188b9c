<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'ModuleManager' => 'Module Manager',
	'LBL_IMPORT_MODULE' => 'Import Module',
	'LBL_VTIGER_EXTENSION_STORE' => 'Vtiger Extension Store',
	'LBL_PUBLISHER' => 'Publisher',
	'LBL_LICENSE' => 'License',
	'LBL_PUBLISHED_ON' => 'Published on',
	'LBL_INSTALL' => 'Install',
	'LBL_UPGRADE' => 'Upgrade',
	'LBL_VERSION' => 'Version',
	'LBL_DECLINE' => 'Decline',
	'LBL_ACCEPT_AND_INSTALL' => 'Accept and Install',
	'LBL_ALREADY_EXISTS' => 'Already Exists',
	'LBL_OK' => 'OK',
	'LBL_EXTENSION_NOT_COMPATABLE' => 'Extension is not Vtiger CRM Compatable',
	'LBL_INVALID_FILE' => 'Invalid File',
	'LBL_NO_LICENSE_PROVIDED' => 'No License Provided',
	'LBL_INSTALLATION' => 'Installation',
	'LBL_FAILED' => 'Failed',
	'LBL_SUCCESSFULL' => 'Successfull',
	'LBL_INSTALLATION_LOG' => 'Installation Log',
	//Install From file labels
	'LBL_VERIFY_IMPORT_DETAILS' => 'Verify Import Details',
	'LBL_MODULE_NAME' => 'Module Name',
	'LBL_REQ_VTIGER_VERSION' => 'Vtiger Version',
	'LBL_LICENSE_ACCEPT_AGREEMENT' => 'I accept the license agreement.',
	'LBL_EXISTS'=>'Exists',
	'LBL_PROCEED_WITH_IMPORT' => 'Do you want to proceed with the import?',
	'LBL_VERSION_NOT_SUPPORTED' => 'Module version is not compatible with vtiger version',
	'LBL_FINISH' => 'Finish',
	'LBL_IMPORT_ZIP' => 'Install From File',
	'LBL_NOT_FOUND' => "Sorry, no extensions are currently available for the current version of your CRM",
	'LBL_INVALID_IMPORT_TRY_AGAIN' => ' provided for module import! Try Again.',
	'LBL_IMPORTED_MODULE' => ' module was imported successfully.',
	'LBL__IMPORTING_MODULE' => 'Importing Module',
	'LBL_EXTENSION_STORE' => 'Extension Store',
	'LBL_ACCEPT_WITH_THE_DISCLAIMER' => 'I accept with disclaimer and would like to proceed',
	'LBL_INSTALL_FROM_ZIP' => 'Install from Zip',
	'LBL_DELETE_EXIST_DIRECTORY' => 'Please delete the module directory that exist to proceed with import module',

	'LBL_UPDATE_MODULE_FROM_FILE' => 'Update Module From File',
	'LBL_UPDATED_MODULE' => ' module was updated successfully.',
	'LBL__UPDATING_MODULE' => 'Updating Module',
	'LBL_PHP_EXTENSION_LOADER_IS_NOT_AVAIABLE' => 'Please install extension loader',
	'LBL_IMPORT_MODULE_FROM_ZIP' => 'Import Module from Zip',
	'LBL_UPDATE_NOW' => 'Update Now',
	'LBL_IMPORT_NOW' => 'Import Now',
	'LBL_DISCLAIMER_FOR_IMPORT_FROM_ZIP' => '<b>Disclaimer - Custom Files could put your CRM at risk</b> <br>  
												 This functionality has been made available for Developers to test new modules.
													If you are installing from a file provided 3rd party, please proceed only if you trust the publisher. Custom Modules could include security vulnerabilities and overwrite system files putting your CRM at risk. The module you are about to install has not been reviewed by Vtiger.',
);
$jsLanguageStrings = array(
	'JS_ON' => 'on',
	'JS_PLEASE_INSTALL_EXTENSION_LOADER_TO_INSTALL_THIS_EXTENSION_FROM_BELOW_LINK' => 'Please install extension loader to install this extension from below link',
	'JS_EXTENSION_STORE_IS_NOT_SUPPOTED_WOULD_YOU_LIKE_TO_INSTALL' => 'Extension is not supported, Would you like to install?',
	'JS_PHP_EXTENSION_LOADER_NOT_INSTALLED' => 'Php extension loader not installed ',
	'JS_CLICK_HERE' => 'Click here ',
	'JS_FOR_INSTALLATION' => 'for installation',
	'JS_IMPORT_SUCCESSFULL' => 'Import Successfull',
	'JS_IMPORTED_MODULE' => ' module was imported successfully.',
	'JS_UPDATE_SUCCESSFULL' => 'Update Successfull',
	'JS_UPDATED_MODULE' => 'module was updated successfully',
);