<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD' => 'Add Picklist Dependency',
    'PickListDependency' => 'PickList Dependency',
	'LBL_PICKLIST_DEPENDENCY' => 'Picklist Dependency',
	'LBL_SELECT_MODULE' => 'Module',
	'LBL_SOURCE_FIELD' => 'Source Field',
	'LBL_TARGET_FIELD' => 'Target Field',
	'LBL_SELECT_FIELD' => 'Select field',
	'LBL_CONFIGURE_DEPENDENCY_INFO' => 'Click on the respective cell to change the mapping for picklist values of target field',
	'LBL_CONFIGURE_DEPENDENCY_HELP_1' => 'Only mapped picklist values of the Source field will be shown below (except for first time)',
	'LBL_CONFIGURE_DEPENDENCY_HELP_2' => "If you want to see or change the mapping for the other picklist values of Source field, <br/>
										then you can select the values by clicking on <b>'Select Source values'</b> button on the right side",
	'LBL_CONFIGURE_DEPENDENCY_HELP_3' => 'Selected values of the Target field values, are highlighted as',
	'LBL_SELECT_SOURCE_VALUES' => 'Select Source Values',
	'LBL_SELECT_SOURCE_PICKLIST_VALUES' => 'Select Source Picklist Values',
	'LBL_ERR_CYCLIC_DEPENDENCY' => 'This dependency setup is not allowed as it ends up in some cyclic dependency',
	'LBL_SELECT_ALL_VALUES' => 'Select All',
	'LBL_UNSELECT_ALL_VALUES' => 'Unselect All',
    'LBL_CYCLIC_DEPENDENCY_ERROR' => 'This could end up in cyclic redundancy as %s field is already configured for %s field',
);

$jsLanguageStrings = array(
	'JS_LBL_ARE_YOU_SURE_YOU_WANT_TO_DELETE' => 'Are you sure you want to delete this picklist dependency?',
	'JS_DEPENDENCY_DELETED_SUEESSFULLY' => 'Dependency deleted successfully',
	'JS_PICKLIST_DEPENDENCY_SAVED' => 'Picklist Dependency Saved',
    'JS_DEPENDENCY_ATLEAST_ONE_VALUE' => 'You need to select atleast one value for',
	'JS_SOURCE_AND_TARGET_FIELDS_SHOULD_NOT_BE_SAME' => 'Source field and Target field should not be same',
	'JS_SELECT_SOME_VALUE' => 'Select some value'
);
