<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'Accounts'                     => 'Контрагенты'      , 
	'LBL_ADD_CUSTOM_RULE'          => 'Добавить Пользовательское Правило', 
	'Read Only'                    => 'Только чтение'                   , 
	'Read Write'                   => 'Чтение/Запись'   , // KEY 5.x: Read/Write
	'LBL_ADD_CUSTOM_RULE_TO'       => 'Добавить пользовательское правило, чтобы'          , 
	'LBL_CAN_ACCESSED_BY'          => 'доступно для'     , // KEY 5.x: LBL_CAN_BE_ACCESSED_BY
	'LBL_PRIVILEGES'               => 'Права'                  , 
	'LBL_SHARING_RULE'             => 'Обмен Правила'               , 
	'LBL_RULE_NO'                  => 'Правило №.'         , 
	'LBL_MODULE'                   => 'Модуль CRM'            , 
	'LBL_ADVANCED_SHARING_RULES'   => 'Расширенный Правила сети'      , 
	'LBL_WITH_PERMISSIONS'         => 'С разрешений'            , 
	'LBL_APPLY_NEW_SHARING_RULES'  => 'Применить новые правила распределения доходов'     , 
	'LBL_READ'                     => 'Прочитанное'      , 
	'LBL_READ_WRITE'               => 'Чтение и запись'              , 
	'LBL_CUSTOM_ACCESS_MESG'       => 'Не установлено пользовательских прав Совместного доступа.', 
	'SINGLE_Groups'                => 'Группа'                , // KEY 5.x: LBL_GROUP
	'SINGLE_Roles'                 => 'Роли'                    , // KEY 5.x: LBL_LIST_CONTACT_ROLE
	'SINGLE_RoleAndSubordinates'   => 'RoleAndSubordinate'          , 

  'SharingAccess' => 'Правила Общего Доступа',

);
$jsLanguageStrings = array(
	'JS_CUSTOM_RULE_SAVED_SUCCESSFULLY' => 'Пользовательские Обмен Правило успешно сохранены', 
	'JS_SELECT_ANY_OTHER_ACCESSING_USER' => 'Выберите любой другой доступом пользователя', 
	'JS_NEW_SHARING_RULES_APPLIED_SUCCESSFULLY' => 'Новый Правила сети успешно применяться', 
	'JS_DEPENDENT_PRIVILEGES_SHOULD_CHANGE' => 'Возможности, Билеты, котировки, SalesOrder & Счет Доступ должен быть установлен в частный, когда Организация доступа установлен в частный', 
);