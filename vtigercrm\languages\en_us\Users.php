<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD' => 'Add User',

	//MyPreference Blocks
	'LBL_MY_PREFERENCES'  =>  'My Preferences',
	'LBL_MORE_INFORMATION'  =>  'More Information',
	'LBL_USERLOGIN_ROLE'  =>  'User Login & Role',
	'LBL_USER_IMAGE_INFORMATION' => 'User Photograph',
	'LBL_CURRENCY_CONFIGURATION' =>  'Currency and Number Field Configuration',
	'LBL_ADDRESS_INFORMATION' => 'User Address',
	'LBL_USER_ADV_OPTIONS' => 'User Advanced Options',
	'Asterisk Configuration'  =>  'Asterisk Configuration',
	'LBL_HOME_PAGE_COMPONENTS' => 'Home Page Components',
	'LBL_TAG_CLOUD_DISPLAY' => 'Tag Cloud Display',

	//MyPreference Fields
	'Role' => 'Role',
	'Admin' => 'Admin',
	'User Name' => 'User Name',
	'Default Activity View' => 'Default MyCalendar View',
	'Default Calendar View' => 'Default Calendar View',
	'Default Lead View' => 'Default Lead View',
	'Title' => 'Title',
	'Office Phone' => 'Office Phone',
	'Department' => 'Department',
	'Reports To' => 'Reports To',
	'Yahoo id' => 'Yahoo id',
	'Home Phone' => 'Home Phone',
	'User Image' => 'User Image',
	'Date Format' => 'Date Format',
	'Tag Cloud' => 'Tag Cloud',
	'Signature' => 'Signature',
	'Street Address' => 'Street Address',
	'Password' => 'Password',
	'Confirm Password' => 'Confirm Password',
	'LBL_SHOWN' => 'Shown',
	'LBL_HIDDEN' => 'Hidden',
	'LBL_SHOW' => 'Show',
	'LBL_HIDE' => 'Hide',
	'LBL_HOME_PAGE_COMPO' => 'Home Page Components',
	'LBL_LOGIN_HISTORY' => 'Login History',
	'LBL_USERDETAIL_INFO' => 'Viewing user details',
	'LBL_DELETE_GROUP' => 'Delete Group',
	'LBL_DELETE_GROUPNAME' => 'Group to be Deleted',
	'LBL_TRANSFER_GROUP' => 'Transfer Ownership to: ',
	'LBL_DELETE_USER' => 'User to be Deleted',
	'LBL_REMOVE_USER' => 'Delete',
	'LBL_MORE_OPTIONS' => 'More Options',
	'LBL_TRANSFER_USER' => 'Transfer Ownership to User',
	'LBL_RESTORE_USER' => 'Restore User',
	'LBL_DELETE_PROFILE' => 'Delete Profile',
	'LBL_TRANSFER_ROLES_TO_PROFILE' => 'Transfer Roles to Profile',
	'LBL_PROFILE_TO_BE_DELETED' => 'Profile to be Deleted',
	'INTERNAL_MAIL_COMPOSER' => 'Internal Mail Composer',
	'Asterisk Extension'  =>  'Asterisk Extension',
	' Receive Incoming Calls'  =>  'Receive Incoming Calls',
	'Webservice Access Key' => 'Access Key',
	'Language' => 'Language',
	'Theme' =>  'Theme',
	'Time Zone'  =>  'Time Zone',
	'Decimal Separator'  =>  'Decimal Separator',
	'Digit Grouping Pattern'  =>  'Digit Grouping Pattern',
	'Digit Grouping Separator'  =>  'Digit Grouping Separator',
	'Symbol Placement'  =>  'Symbol Placement',
	'Number Of Currency Decimals'  =>  'Number Of Currency Decimals',
	'Truncate Trailing Zeros'  =>   'Truncate Trailing Zeros',
	'Default Call Duration' => 'Default Call Duration (Mins)',
	'Other Event Duration' => 'Other Event Duration (Mins)',
	'Calendar Hour Format' => 'Calendar Hour Format',
	'Tag Cloud' => 'Tag Cloud',

	//Time zones-Dont change any value
	'Kwajalein'  =>  '(UTC-12:00) International Date Line West',
	'Pacific/Midway'  =>  '(UTC-11:00) Coordinated Universal Time-11',
	'Pacific/Samoa'  =>  '(UTC-11:00) Samoa',
	'Pacific/Honolulu'  =>  '(UTC-10:00) Hawaii',
	'America/Anchorage'  =>  '(UTC-09:00) Alaska',
	'America/Los_Angeles'  =>  '(UTC-08:00) Pacific Time (US &amp; Canada)',
	'America/Tijuana'  =>  '(UTC-08:00) Tijuana, Baja California',
	'America/Denver'  =>  '(UTC-07:00) Mountain Time (US &amp; Canada)',
	'America/Chihuahua'  =>  '(UTC-07:00) Chihuahua, La Paz, Mazatlan',
	'America/Mazatlan'  =>  '(UTC-07:00) Mazatlan',
	'America/Phoenix'  =>  '(UTC-07:00) Arizona',
	'America/Regina'  =>  '(UTC-06:00) Saskatchewan',
	'America/Tegucigalpa'  =>  '(UTC-06:00) Central America',
	'America/Chicago'  =>  '(UTC-06:00) Central Time (US &amp; Canada)',
	'America/Mexico_City'  =>  '(UTC-06:00) Mexico City',
	'America/Monterrey'  =>  '(UTC-06:00) Monterrey',
	'America/New_York'  =>  '(UTC-05:00) Eastern Time (US &amp; Canada)',
	'America/Bogota'  =>  '(UTC-05:00) Bogota, Lima, Quito',
	'America/Lima'  =>  '(UTC-05:00) Lima',
	'America/Rio_Branco'  =>  '(UTC-05:00) Rio Branco',
	'America/Indiana/Indianapolis'  =>  '(UTC-05:00) Indiana (East)',
	'America/Caracas'  =>  '(UTC-04:30) Caracas',
	'America/Halifax'  =>  '(UTC-04:00) Atlantic Time (Canada)',
	'America/Manaus'  =>  '(UTC-04:00) Manaus',
	'America/Santiago'  =>  '(UTC-04:00) Santiago',
	'America/La_Paz'  =>  '(UTC-04:00) La Paz',
	'America/Cuiaba'  =>  '(UTC-04:00) Cuiaba',
	'America/Asuncion'  =>  '(UTC-04:00) Asuncion',
	'America/St_Johns'  =>  '(UTC-03:30) Newfoundland',
	'America/Argentina/Buenos_Aires'  =>  '(UTC-03:00) Buenos Aires',
	'America/Sao_Paulo'  =>  '(UTC-03:00) Brasilia',
	'America/Godthab'  =>  '(UTC-03:00) Greenland',
	'America/Montevideo'  =>  '(UTC-03:00) Montevideo',
	'Atlantic/South_Georgia'  =>  '(UTC-02:00) Mid-Atlantic',
	'Atlantic/Azores'  =>  '(UTC-01:00) Azores',
	'Atlantic/Cape_Verde'  =>  '(UTC-01:00) Cape Verde Is.',
	'Europe/London'  =>  '(UTC) London, Edinburgh, Dublin, Lisbon',
	'UTC'  =>  '(UTC) Coordinated Universal Time, Greenwich Mean Time',
	'Africa/Monrovia'  =>  '(UTC) Monrovia, Reykjavik',
	'Africa/Casablanca'  =>  '(UTC) Casablanca',
	'Europe/Belgrade'  =>  '(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague',
	'Europe/Sarajevo'  =>  '(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb',
	'Europe/Brussels'  =>  '(UTC+01:00) Brussels, Copenhagen, Madrid, Paris',
	'Africa/Algiers'  =>  '(UTC+01:00) West Central Africa',
	'Europe/Amsterdam'  =>  '(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna',
	'Europe/Minsk'  =>  '(UTC+02:00) Minsk',
	'Africa/Cairo'  =>  '(UTC+02:00) Cairo',
	'Europe/Helsinki'  =>  '(UTC+02:00) Helsinki, Riga, Sofia, Tallinn, Vilnius',
	'Europe/Athens'  =>  '(UTC+02:00) Athens, Bucharest',
	'Europe/Istanbul'  =>  '(UTC+02:00) Istanbul',
	'Asia/Jerusalem'  =>  '(UTC+02:00) Jerusalem',
	'Asia/Amman'  =>  '(UTC+02:00) Amman',
	'Asia/Beirut'  =>  '(UTC+02:00) Beirut',
	'Africa/Windhoek'  =>  '(UTC+02:00) Windhoek',
	'Africa/Harare'  =>  '(UTC+02:00) Harare',
	'Asia/Kuwait'  =>  '(UTC+03:00) Kuwait, Riyadh',
	'Asia/Baghdad'  =>  '(UTC+03:00) Baghdad',
	'Africa/Nairobi'  =>  '(UTC+03:00) Nairobi',
	'Europe/Moscow'  =>  '(UTC+03:00) Moscow, Volgograd',
	'Asia/Tehran'  =>  '(UTC+03:30) Tehran',
	'Asia/Tbilisi'  =>  '(UTC+04:00) Tbilisi',
	'Asia/Muscat'  =>  '(UTC+04:00) Abu Dhabi, Muscat',
	'Asia/Baku'  =>  '(UTC+04:00) Baku',
	'Asia/Yerevan'  =>  '(UTC+04:00) Yerevan',
	'Asia/Karachi'  =>  '(UTC+05:00) Islamabad, Karachi',
	'Asia/Tashkent'  =>  '(UTC+05:00) Tashkent',
	'Asia/Kolkata'  =>  '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi',
	'Asia/Colombo'  =>  '(UTC+05:30) Sri Jayawardenepura',
	'Asia/Katmandu'  =>  '(UTC+05:45) Kathmandu',
	'Asia/Dhaka'  =>  '(UTC+06:00) Dhaka',
	'Asia/Almaty'  =>  '(UTC+06:00) Almaty',
	'Asia/Yekaterinburg'  =>  '(UTC+06:00) Ekaterinburg',
	'Asia/Rangoon'  =>  '(UTC+06:30) Yangon (Rangoon)',
	'Asia/Novosibirsk'  =>  '(UTC+07:00) Novosibirsk',
	'Asia/Bangkok'  =>  '(UTC+07:00) Bangkok, Jakarta',
	'Asia/Brunei'  =>  '(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi',
	'Asia/Krasnoyarsk'  =>  '(UTC+08:00) Krasnoyarsk',
	'Asia/Ulaanbaatar'  =>  '(UTC+08:00) Ulaan Bataar',
	'Asia/Kuala_Lumpur'  =>  '(UTC+08:00) Kuala Lumpur, Singapore',
	'Asia/Taipei'  =>  '(UTC+08:00) Taipei',
	'Australia/Perth'  =>  '(UTC+08:00) Perth',
	'Asia/Irkutsk'  =>  '(UTC+09:00) Irkutsk',
	'Asia/Seoul'  =>  '(UTC+09:00) Seoul',
	'Asia/Tokyo'  =>  '(UTC+09:00) Tokyo',
	'Australia/Darwin'  =>  '(UTC+09:30) Darwin',
	'Australia/Adelaide'  =>  '(UTC+09:30) Adelaide',
	'Australia/Canberra'  =>  '(UTC+10:00) Canberra, Melbourne, Sydney',
	'Australia/Brisbane'  =>  '(UTC+10:00) Brisbane',
	'Australia/Hobart'  =>  '(UTC+10:00) Hobart',
	'Asia/Vladivostok'  =>  '(UTC+10:00) Vladivostok',
	'Pacific/Guam'  =>  '(UTC+10:00) Guam, Port Moresby',
	'Asia/Yakutsk'  =>  '(UTC+10:00) Yakutsk',
	'Etc/GMT-11' => '(UTC+11:00) Solomon Is., New Caledonia',
	'Pacific/Fiji'  =>  '(UTC+12:00) Fiji',
	'Asia/Kamchatka'  =>  '(UTC+12:00) Kamchatka',
	'Pacific/Auckland'  =>  '(UTC+12:00) Auckland',
	'Asia/Magadan'  =>  '(UTC+12:00) Magadan',
	'Pacific/Tongatapu'  =>  '(UTC+13:00) Nukualofa',

	'Summary' => 'Summary',
	'Detail' => 'Detail',
	'LBL_USER_LIST_DETAILS' => 'Details',

	'LBL_OLD_PASSWORD' => 'Old Password',
	'LBL_CHANGE_PASSWORD' => 'Change Password',
	'LBL_NEW_PASSWORD' => 'New Password',
	'LBL_CONFIRM_PASSWORD' => 'Confirm Password',
	'LBL_CHANGE_ACCESS_KEY' => 'Change Access Key',
	'LBL_ACCESS_KEY_UPDATED_SUCCESSFULLY' => 'Access key updated successfully',
	'LBL_FAILED_TO_UPDATE_ACCESS_KEY' => 'Failed to update access key',
	'LBL_TRANSFER_OWNERSHIP' => 'Transfer Owner',
	'LBL_LOGIN_AS' => 'Login as ',
	'LBL_USER_DELETED_SUCCESSFULLY' => 'User deleted successfully',
	'LBL_ACTIVE_USERS' => 'Active Users',
	'LBL_INACTIVE_USERS' => 'Inactive Users',
	'LBL_CREATE_USER' => 'Create User',
	'LBL_DELETE_USER_PERMANENTLY' => 'Delete User Permanently',
	'LBL_DELETE_USER_PERMANENTLY_INFO' => 'Deleting a user permanently will transfer all records including comments and history to new user.',
	'LBL_RESTORE' => 'Restore',
	'LBL_USER_RESTORED_SUCCESSFULLY' => 'User restored successfully',
	//Login strings
	'LBL_TO_CRM' => 'Login to Vtiger CRM',
	'LBL_INVALID_USER_OR_PASSWORD' => 'Invalid username or password.',
	'LBL_INVALID_USER_OR_EMAIL' => 'Invalid Username or Email address.',
	'LBL_EMAIL_SEND' => 'We have sent you email to reset your password.',
	'ForgotPassword' => 'Forgot Password',
	'LBL_CONNECT_WITH_US' => 'Connect with US',
	'LBL_GET_MORE' => 'Get more out of Vtiger',

	'LBL_TRANSFER_RECORDS_TO_USER' => 'Transfer records to user',
	'LBL_USER_TO_BE_DELETED' => 'User to be Deleted',
	'LBL_ALMOST_THERE'	=>	'Almost there!',
	'LBL_ABOUT_ME'		=>	'About Me',
	'LBL_WE_PROMISE_TO_KEEP_THIS_PRIVATE'	=>	'(We promise to keep this private)',
	'LBL_ALL_FIELDS_BELOW_ARE_REQUIRED'		=>	'(All fields below are required)',
	'LBL_GET_STARTED'	=> 'Get Started',
	'LBL_YOUR_CONTACT_NUMBER' => 'Your Contact Number',
	'LBL_WHERE_ARE_YOU_FROM' =>	'Where are you from?',
	'LBL_SELECT_COUNTRY'	=> 'Select Country',
	'LBL_COMPANY_SIZE'		=> 'Company Size',
	'LBL_JOB_TITLE'			=> 'Job Title',
	'LBL_DEPARTMENT'		=> 'Department',
	'LBL_BASE_CURRENCY'		=> 'Base Currency',
	'LBL_CHOOSE_BASE_CURRENCY'	=> 'Choose Base Currency',
	'LBL_OPERATING_CURRENCY'	=> 'Base currency cannot be modified later. Select your operating currency',
	'LBL_LANGUAGE' => 'Language',
	'LBL_CHOOSE_LANGUAGE'	=> 'Choose Language',
	'LBL_CHOOSE_TIMEZONE'	=> 'Choose Timezone',
	'LBL_DATE_FORMAT'		=> 'Date Format',
	'LBL_CHOOSE_DATE_FORMAT'=> 'Choose Date Format',
	'LBL_PHONE'	=> 'Phone',
	'Space' => 'Space',
	//picklist values for Default Calendar View field in MyPreference Page
	'ListView' => 'List View',
	'MyCalendar' => 'My Calendar',
	'SharedCalendar' => 'Shared Calendar',

	'LBL_CHANGE_OWNER' => 'Change Owner',
	'LBL_TRANSFER_OWNERSHIP' => 'Transfer Ownership',
	'LBL_TRANSFER_OWNERSHIP_TO_USER' => 'Transfer Ownership to User',
	'LBL_OWNERSHIP_TRANSFERRED_SUCCESSFULLY' => 'CRM Owner changed successfully',
	'LBL_OWNERSHIP_TRANSFERRED_FAILED' => 'Failed changing CRM owner',
	'Account Owner' => 'Account Owner',
	'Starting Day of the week' => 'Starting Day of the week',
	'Day starts at' => 'Day starts at',
	'Default Event Status' => 'Default Event Status',
	'Default Activity Type' => 'Default Activity Type',
	'Default Record View' => 'Default Record View',
	'Left Panel Hide' => 'Left Panel Hide',
	'Row Height' => 'Row Height',

	//Change Username labels
	'LBL_CHANGE_USERNAME' => 'Change Username',
	'LBL_USERNAME_CHANGED' => 'Username changed successfully',
	'ERROR_CHANGE_USERNAME' => 'Error in change username. Please try later',

	'LBL_RESTORE_USER_FAILED' => 'Failed to restore user. There is already a CRM user with this user name.',

	'LBL_DUPLICATE_USER_EXISTS' => 'User Already Exists',

	/* For Vtiger7*/
	'LBL_USERS_SETTINGS' => 'USERS SETTINGS',
	'LBL_TEMPLATES' => 'Templates',
);

$jsLanguageStrings = array(

	//Curency seperator validation messages
	'JS_ENTER_OLD_PASSWORD'=>'Please enter your old password.',
	'JS_ENTER_NEW_PASSWORD'=>'Please enter your new password.',
	'JS_ENTER_CONFIRMATION_PASSWORD'=>'Please enter your password confirmation.',
	'JS_REENTER_PASSWORDS'=>'Please re-enter passwords.  The \"new password\" and \"confirm password\" values do not match.',
	'JS_INVALID_PASSWORD'=>'You must specify a valid username and password.',
	'JS_PASSWORD_CHANGE_FAILED_1'=>'User password change failed for ',
	'JS_PASSWORD_CHANGE_FAILED_2'=>' failed.  The new password must be set.',
	'JS_PASSWORD_INCORRECT_OLD'=>'Incorrect old password specified. Re-enter password information.',
	'JS_ENTERED_CURRENT_USERNAME_MSG' => 'You entered the current username. Please enter new username.',
	'JS_NEW_ACCESS_KEY_REQUESTED' => 'New Access key requested',
	'JS_CHANGE_ACCESS_KEY_CONFIRMATION' => 'You have requested for a new Access key.&lt;br&gt;&lt;br&gt;With the new Access key provision, you have to replace the old access key with the new one in all installed extensions.&lt;br&gt;&lt;br&gt;Do you want to continue?',
);