<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	// Basic Strings
	'Contacts' => 'Contacts',
	'SINGLE_Contacts' => 'Contact',
	'LBL_ADD_RECORD' => 'Add Contact',
	'LBL_RECORDS_LIST' => 'Contacts List',

	// Blocks
	'LBL_CONTACT_INFORMATION' => 'Basic Information',
	'LBL_CUSTOMER_PORTAL_INFORMATION' => 'Customer Portal Details',
	'LBL_IMAGE_INFORMATION' => 'Profile Picture',
	'LBL_COPY_OTHER_ADDRESS' => 'Copy Other Address',
	'LBL_COPY_MAILING_ADDRESS' => 'Copy Mailing Address',

	//Field Labels
	'Office Phone' => 'Office Phone',
	'Home Phone' => 'Home Phone',
	'Title' => 'Title',
	'Department' => 'Department',
	'Birthdate' => 'Date of Birth',
	'Reports To' => 'Reports To',
	'Assistant' => 'Assistant',
	'Assistant Phone' => 'Assistant Phone',
	'Do Not Call' => 'Do Not Call',
	'Reference' => 'Reference',
	'Portal User' => 'Portal User',
	'Mailing Street' => 'Mailing Street',
	'Mailing City' => 'Mailing City',
	'Mailing State' => 'Mailing State',
	'Mailing Zip' => 'Mailing Zip',
	'Mailing Country' => 'Mailing Country',
	'Mailing Po Box' => 'Mailing P.O. Box',
	'Other Street' => 'Other Street',
	'Other City' => 'Other City',
	'Other State' => 'Other State',
	'Other Zip' => 'Other Zip',
	'Other Country' => 'Other Country',
	'Other Po Box' => 'Other P.O. Box',
	'Contact Image' => 'Contact Image',
	'Other Phone' => 'Secondary Phone',
	'Email' => 'Primary Email',
	'Secondary Email' => 'Secondary Email',
	'Contact Id' => 'Contact Id',
    'Support Start Date' => 'Support Start Date',
    'Support End Date'   => 'Support End Date',
	
	//Added for Picklist Values
	'Mr.'=>'Mr.',
	'Ms.'=>'Ms.',
	'Mrs.'=>'Mrs.',
	'Dr.'=>'Dr.',
	'Prof.'=>'Prof.',
	
	'User List'=>'User List',
);

$jsLanguageStrings = array(
 );