<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'Reports'=>'Reports',
	'SINGLE_Reports' => 'Report',

	// Basic Strings
	'LBL_FOLDER_NOT_EMPTY' => 'Folder is not empty',
	'LBL_MOVE_REPORT' => 'Move Reports',
	'LBL_CUSTOMIZE' => 'Customize',
	'LBL_REPORT_EXPORT_EXCEL' => 'Export Excel',
	'LBL_REPORT_PRINT' => 'Print',
	'LBL_CREATING_REPORT' => 'Creating Report',
	'LBL_EDITING_REPORT' => 'Editing Report',
	'LBL_REPORT_DETAILS' => 'Report Details',
	'LBL_SELECT_COLUMNS' => 'Select Columns',
	'LBL_FILTERS' => 'Filters',
	'LBL_FOLDERS' => 'Folders',
	'LBL_ADD_NEW_FOLDER' => 'Add New Folder',
	'LBL_FOLDER_NAME' => 'Folder Name',
	'LBL_FOLDER_DESCRIPTION' => 'Folder Description',
	'LBL_WRITE_YOUR_DESCRIPTION_HERE' => 'Enter Description',
	'LBL_DUPLICATES_EXIST' => 'Report Name already Exists',
	'LBL_FOLDERS_LIST' => 'Folders List',
	'LBL_DENIED_REPORTS' => 'Denied Reports',
	'LBL_NO_OF_RECORDS' => 'Total records : ',
    'LBL_MORE_RECORDS_TXT'=>'Only 500 records are shown below. Please export to see all Records',
	'LBL_SCHEDULE_REPORTS' => 'Schedule Reports',
	'LBL_AT_TIME' => 'At Time',

	'LBL_DAILY' => 'Daily',
	'LBL_WEEKLY' => 'Weekly',
	'LBL_MONTHLY_BY_DATE' => 'Monthly by Date',
	'LBL_YEARLY' => 'Yearly',
	'LBL_SPECIFIC_DATE' => 'On Specific Date',
	'LBL_CHOOSE_DATE' => 'Choose Date',
	'LBL_ON_THESE_DAYS' => 'On these days',
	'LBL_SELECT_MONTH_AND_DAY' => 'Select Month and Date',
	'LBL_SELECTED_DATES' => 'Selected Dates',
	'LBL_EXCEEDING_MAXIMUM_LIMIT' => 'Maximum limit exceeded',
	'LBL_NEXT_TRIGGER_TIME' => 'Next trigger time on',
	'LBL_RUN_REPORT' =>'Run Report',
	'LBL_SELECT_RECIEPIENTS' => 'Select Recipients',
	'LBL_SPECIFIC_EMAIL_ADDRESS' => 'Send to specific email',
	'LBL_SAME_LEVEL_ROLES' => 'Same Level Roles',
	'LBL_SUBORDINATE_ROLES' => 'Subordinate Roles',
	//ListView Actions
	'LBL_ADD_RECORD' => 'Add Report',
	'LBL_ADD_FOLDER' => 'Add Folder',
	'LBL_REPORT_DELETE_DENIED' => 'Permission denied to delete the Report',

	//Folder Actions
	'LBL_FOLDER_NOT_EMPTY' => 'Folder is not empty',
	'LBL_FOLDER_CAN_NOT_BE_DELETED' => 'This folder can not be deleted',

	//Mass Actions
	'LBL_REPORTS_LIST' => 'Reports list',

	//Step1 Strings
	'LBL_REPORT_NAME' => 'Report Name',
	'LBL_REPORT_FOLDER' => 'Report Folder',
	'LBL_DESCRIPTION' => 'Description',
	'PRIMARY_MODULE' => 'Primary Module',
	'LBL_SELECT_RELATED_MODULES' => 'Select Related Modules',
	'LBL_MAX' => 'Max',
	'LBL_NEXT' => 'Next',
	'LBL_REPORTS' => 'Reports List',
	'LBL_SELECT_RELATED_MODULES' => 'Select Related Modules',

	//Step2 Strings
	'LBL_GROUP_BY' => 'Group By',
	'LBL_SORT_ORDER' => 'Sort Order',
	'LBL_ASCENDING' => 'Ascending',
	'LBL_DESCENDING' => 'Descending',
	'LBL_CALCULATIONS' =>'Calculations',
	'LBL_COLUMNS' => 'Columns',
	'LBL_SUM_VALUE' => 'Sum',
	'LBL_AVERAGE' => 'Average',
	'LBL_LOWEST_VALUE' => 'Lowest Value',
	'LBL_HIGHEST_VALUE' => 'Highest Value',

	//Step3 Strings
	'LBL_GENERATE_REPORT' => 'Save & Generate Report',

	//DetailView
	'LBL_SUM' => 'SUM',
	'LBL_AVG' => 'AVG',
	'LBL_MAX' => 'MAX',
	'LBL_MIN' => 'MIN',
	'LBL_FIELD_NAMES' => 'Field Names',
	'LBL_REPORT_CSV' => 'Export CSV',
	'LBL_VIEW_DETAILS' => 'View Details',
	'LBL_GENERATE_NOW' => 'Generate now',

	//List View Headers
	'Report Name' => 'Report Name',

	//Default Folders Names, Report Names and Description
	'Account and Contact Reports'=>'Organization and Contact Reports',
	'Lead Reports'=>'Lead Reports',
	'Potential Reports'=>'Opportunity Reports',
	'Activity Reports'=>'Activity Reports',
	'HelpDesk Reports'=>'Tickets Reports',
	'Product Reports'=>'Product Reports',
	'Quote Reports'=>'Quote Reports',
	'PurchaseOrder Reports'=>'Purchase Order Reports',
	'SalesOrder Reports'=>'Sales Order Reports', //Added for SO
	'Invoice Reports'=>'Invoice Reports',
	'Campaign Reports'=>'Campaign Reports', //Added for Campaigns
	'Contacts by Accounts'=>'Contacts by Organizations',
	'Contacts without Accounts'=>'Contacts without Organizations',
	'Contacts by Potentials'=>'Contacts by Opportunities',
	'Contacts related to Accounts'=>'Contacts related to Organizations',
	'Contacts not related to Accounts'=>'Contacts not related to Organizations',
	'Contacts related to Potentials'=>'Contacts related to Opportunities',
	'Lead by Source'=>'Lead by Source',
	'Lead Status Report'=>'Lead Status Report',
	'Potential Pipeline'=>'Opportunity Pipeline',
	'Closed Potentials'=>'Closed Opportunities',
	'Potential that have Won'=>'Won Opportunities',
	'Tickets by Products'=>'Tickets by Products',
	'Tickets by Priority'=>'Tickets by Priority',
	'Open Tickets'=>'Open Tickets',
	'Tickets related to Products'=>'Tickets related to Products',
	'Tickets that are Open'=>'Open Tickets',
	'Product Details'=>'Product Details',
	'Products by Contacts'=>'Products by Contacts',
	'Product Detailed Report'=>'Product Detailed Report',
	'Products related to Contacts'=>'Products related to Contacts',
	'Open Quotes'=>'Open Quotes',
	'Quotes Detailed Report'=>'Quotes Detailed Report',
	'Quotes that are Open'=>'Open Quotes',
	'PurchaseOrder by Contacts'=>'Purchase Order by Contacts',
	'PurchaseOrder Detailed Report'=>'Purchase Order Detailed Report',
	'PurchaseOrder related to Contacts'=>'Purchase Order Related to Contacts',
	'Invoice Detailed Report'=>'Invoice Detailed Report',
	'Last Month Activities'=>'Last Month Activities',
	'This Month Activities'=>'This Month Activities',
	'Campaign Expectations and Actuals'=>'Campaign Expectations and Actuals', //Added for Campaigns
	'SalesOrder Detailed Report'=>'Sales Order Detailed Report', //Added for SO

	'Email Reports'=>'Email Reports',
	'Contacts Email Report'=>'Contacts Email Report',
	'Accounts Email Report'=>'Organizations Email Report',
	'Leads Email Report'=>'Leads Email Report',
	'Vendors Email Report'=>'Vendors Email Report',

	'Emails sent to Contacts' => 'Emails sent to Contacts',
	'Emails sent to Organizations' => 'Emails sent to Organizations',
	'Emails sent to Leads' => 'Emails sent to Leads',
	'Emails sent to Vendors' => 'Emails sent to Vendors',

	'LBL_PRINT_REPORT' => 'Print Report',
	'LBL_RECORDS' => 'Records',
	'LBL_LIMIT_EXCEEDED' => 'Only 1000 + records are displayed. Use CSV or Excel Export to see all the records',
	'LBL_TOP' => 'Top',
	'LBL_ALL_REPORTS' => 'All Reports',
	'LBL_CALCULATION_CONVERSION_MESSAGE' => 'Calculation are based on the My Preferences currency of your CRM',

    //Summary/Pivot Reports
    'LBL_CREATING_PIVOT_REPORT' => 'Create Pivot Report',
    'LBL_EDITING_PIVOT_REPORT' => 'Edit Pivot Report',
    'LBL_SELECT_PIVOT_FIELDS' => 'Select Pivot Fields',
    'LBL_SELECT_ROWS' => 'Select Rows ',
    'LBL_SELECT_DATA_FIELDS' => 'Select Data Fields ',
    'LBL_ADD_ROWS' => 'Add Rows',
    'LBL_ADD_COLUMNS' => 'Add Columns',
    'LBL_ADD_DATA_FIELDS' => 'Add Data Fields',
    'LBL_PIVOT_FIELDS' => 'Selected Pivot Fields',
    'LBL_RECORD_COUNT' => 'Record Count',
    'LBL_SELECT_PIVOT_FIELDS_WARNING' => 'Warning : Please select at least one Row field, Column field and Data field.',
    'LBL_PIVOT_DATA_FIELDS_WARNING' => 'Warning : In Data Column - aggregation functions(sum,avg,min and max) should not repeat.',
    'LBL_MODIFY_CONDITIONS' => 'Modify Conditions',
    'LBL_PIVOT_PREVIEW_EX' => 'Pivot Report Preview(Example)',

	//charts labels
	'LBL_SELECT_CHART_TYPE' => 'Select chart type',
	'LBL_CLICK_THROUGH_NOT_AVAILABLE' => 'Click through not available as you have selected more than one module',
	'LBL_TOTAL_SUM_OF' => 'Total Sum of %s',
	'LBL_AVG_OF' => 'Average of %s',
	'LBL_MIN_OF' => 'Minimum of %s',
	'LBL_MAX_OF' => 'Maximum of %s',
	'LBL_RECORD_COUNT' => 'Record Count',
	'LBL_PIE_CHART' => 'Pie Chart',
	'LBL_VERTICAL_BAR_CHART' => 'Vertical Bar Chart',
	'LBL_HORIZONTAL_BAR_CHART' => 'Horizontal Bar Chart',
	'LBL_LINE_CHART' => 'Line Chart',
	'LBL_SELECT_CHART' => 'Select Chart',
	'LBL_CREATING_CHART_REPORT' => 'Creating Chart Report',
	'LBL_EDITING_CHART_REPORT' => 'Editing Chart Report',
	'LBL_GENERATE_CHART' => 'Generate Chart',
	'LBL_SELECT_GROUP_BY_FIELD' => 'Select Groupby Field',
	'LBL_SELECT_DATA_FIELD' => 'Select Data Fields',
	'LBL_MODIFY_CONDITION' => 'Modify Conditions',
	'LBL_PLEASE_SELECT_ATLEAST_ONE_GROUP_FIELD_AND_DATA_FIELD' => 'Please select at least one Groupby field and one Data field.',
	'LBL_FOR_BAR_GRAPH_AND_LINE_GRAPH_SELECT_3_MAX_DATA_FIELDS' => 'For Bar and Line graph, you can select maximum of 3 Data fields.',
	'LBL_DETAIL_REPORT' => 'Detail Report',
	'LBL_PIVOT_REPORT' => 'Pivot Report',
	'LBL_CHARTS' => 'Charts',
    //Schedule Reports - Mail Content
    'LBL_AUTO_GENERATED_REPORT_EMAIL' => 'This is an auto-generated email sent on behalf of a scheduled report.',
    'LBL_PIN_CHART_TO_DASHBOARD' => 'Pin Chart To DashBoard',
    'LBL_FILE_FORMAT' => 'File Format',
    
    'Report Type' => 'Report Type',
    'tabular' => 'Detail',
    'summary' => 'Tabular',
    'pivot' => 'Pivot',
    'chart' => 'Chart',

    'LBL_REPORTS_MOVED_SUCCESSFULLY'=>'Reports Moved Successfully.',
    'LBL_SAME_SOURCE_AND_TARGET_FOLDER'=>'Target Folder Is Same As Source Folder.',
	'LBL_SHARE_REPORT' => 'Share Report',
	'LBL_SHARED_REPORTS' => 'Shared With Me',
	'LBL_PINNED' => 'Pinned',
	'LBL_UNPINNED' => 'UnPinned',
	'LBL_SEARCH_FOR_FOLDERS' => 'Search for folders',
	'LBL_CHART_REPORT' => 'Chart Report',
	'LBL_UNPIN_CHART_FROM_DASHBOARD' => 'Unpin chart from dashboard',
	'LBL_REPORTS_DELETED_SUCCESSFULLY' => 'Reports Deleted Successfully',
);
$jsLanguageStrings = array(
	'JS_DUPLICATE_RECORD' => 'Duplicate Report',
	'JS_CALCULATION_LINE_ITEM_FIELDS_SELECTION_LIMITATION' => 'Limitation: Line Item fields(List Price, Discount & Quantity) can only be used when other calculation fields are not selected.',
	'JS_NO_CHART_DATA_AVAILABLE' => 'Data not available, please check the selected fields',
    'JS_CHART_PINNED_TO_DASHBOARD' => 'Chart Pinned To DashBoard',
    'JS_CHART_ALREADY_PINNED_TO_DASHBOARD' => 'Chart Already Pinned To DashBoard',
	'JS_MOVE_REPORTS'=>'Move Reports',
    'JS_SCHEDULED_DATE_TIME_ERROR' => 'Scheduled date and time should be greater than current date and time',
	'JSLBL_PIN_CHART_TO_DASHBOARD' => 'Pin Chart To DashBoard',
	'JSLBL_UNPIN_CHART_FROM_DASHBOARD' => 'Unpin chart from dashboard',
	'JS_CHART_REMOVED_FROM_DASHBOARD' => 'Chart removed from dashboard',
);
