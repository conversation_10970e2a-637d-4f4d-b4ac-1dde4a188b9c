<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_NEW'                      => 'New'                         , // TODO: Review
	'LBL_WORKFLOW'                 => 'Workflow'                    , // TODO: Review
	'LBL_CREATING_WORKFLOW'        => 'Creating WorkFlow'           , // TODO: Review
	'LBL_NEXT'                     => 'Next'                        , // TODO: Review
	'LBL_STEP_1'                   => 'Step 1'                      , // TODO: Review
	'LBL_ENTER_BASIC_DETAILS_OF_THE_WORKFLOW' => 'Enter basic details of the Workflow', // TODO: Review
	'LBL_SPECIFY_WHEN_TO_EXECUTE'  => 'Specify when to execute this Workflow', // TODO: Review
	'ON_FIRST_SAVE'                => 'Only on the first save'      , // TODO: Review
	'ONCE'                         => 'Until the first time the condition is true', // TODO: Review
	'ON_EVERY_SAVE'                => 'Every time the record is saved', // TODO: Review
	'ON_MODIFY'                    => 'Every time a record is modified', // TODO: Review
	'MANUAL'                       => 'System'                      , // TODO: Review
	'SCHEDULE_WORKFLOW'            => 'Schedule Workflow'           , // TODO: Review
	'ADD_CONDITIONS'               => 'Add Conditions'              , // TODO: Review
	'ADD_TASKS'                    => 'Ajouter Actions'             ,
	'LBL_EXPRESSION'               => 'Expression'                  , // TODO: Review
	'LBL_FIELD_NAME'               => 'Field'                       , // TODO: Review
	'LBL_SET_VALUE'                => 'Set Value'                   , // TODO: Review
	'LBL_USE_FIELD'                => 'Use Field'                   , // TODO: Review
	'LBL_USE_FUNCTION'             => 'Use Function'                , // TODO: Review
	'LBL_RAW_TEXT'                 => 'Raw text'                    , // TODO: Review
	'LBL_ENABLE_TO_CREATE_FILTERS' => 'Enable to create Filters'    , // TODO: Review
	'LBL_CREATED_IN_OLD_LOOK_CANNOT_BE_EDITED' => 'This workflow was created in older look. Conditions created in older look cannot be edited. You can choose to recreate the conditions, or use the existing conditions without changing them.', // TODO: Review
	'LBL_USE_EXISTING_CONDITIONS'  => 'Use existing conditions'     , // TODO: Review
	'LBL_RECREATE_CONDITIONS'      => 'Recreate Conditions'         , // TODO: Review
	'LBL_SAVE_AND_CONTINUE'        => 'Save & Continue'             , // TODO: Review
	'LBL_ACTIVE'                   => 'Active'                      , // TODO: Review
	'LBL_TASK_TYPE'                => 'Type d\'action'               ,
	'LBL_TASK_TITLE'               => 'Titre de l\'action'           ,
	'LBL_ADD_TASKS_FOR_WORKFLOW'   => 'Ajouter action pour workflow',
	'LBL_EXECUTE_TASK'             => 'Exécuter une action'        ,
	'LBL_SELECT_OPTIONS'           => 'Select Options'              , // TODO: Review
	'LBL_ADD_FIELD'                => 'Add Field'                   , // TODO: Review
	'LBL_ADD_TIME'                 => 'Add time'                    , // TODO: Review
	'LBL_TITLE'                    => 'Title'                       , // TODO: Review
	'LBL_PRIORITY'                 => 'Priority'                    , // TODO: Review
	'LBL_ASSIGNED_TO'              => 'Assigned to'                 , // TODO: Review
	'LBL_TIME'                     => 'Time'                        , // TODO: Review
	'LBL_DUE_DATE'                 => 'Due Date'                    , // TODO: Review
	'LBL_THE_SAME_VALUE_IS_USED_FOR_START_DATE' => 'The same value is used for the start date', // TODO: Review
	'LBL_EVENT_NAME'               => 'Event Name'                  , // TODO: Review
	'LBL_TYPE'                     => 'Type'                        , // TODO: Review
	'LBL_METHOD_NAME'              => 'Method Name'                 , // TODO: Review
	'LBL_RECEPIENTS'               => 'Recepients'                  , // TODO: Review
	'LBL_ADD_FIELDS'               => 'Add Fields'                  , // TODO: Review
	'LBL_SMS_TEXT'                 => 'Sms Text'                    , // TODO: Review
	'LBL_SET_FIELD_VALUES'         => 'Set Field Values'            , // TODO: Review
	'LBL_IN_ACTIVE'                => 'In Active'                   , // TODO: Review
	'LBL_SEND_NOTIFICATION'        => 'Send Notification'           , // TODO: Review
	'LBL_START_TIME'               => 'Start Time'                  , // TODO: Review
	'LBL_START_DATE'               => 'Start Date'                  , // TODO: Review
	'LBL_END_TIME'                 => 'End Time'                    , // TODO: Review
	'LBL_END_DATE'                 => 'End Date'                    , // TODO: Review
	'LBL_ENABLE_REPEAT'            => 'Enable Repeat'               , // TODO: Review
	'LBL_NO_METHOD_IS_AVAILABLE_FOR_THIS_MODULE' => 'No method is available for this module', // TODO: Review
	'LBL_FINISH'                   => 'Finish'                      , // TODO: Review
	'LBL_NO_TASKS_ADDED'           => 'Aucune action'               ,
	'LBL_CANNOT_DELETE_DEFAULT_WORKFLOW' => 'You Cannot delete default Workflow', // TODO: Review
	'LBL_MODULES_TO_CREATE_RECORD' => 'Créer un enregistrement dans',
	'LBL_EXAMPLE_EXPRESSION'       => 'Expression'                  , // TODO: Review
	'LBL_EXAMPLE_RAWTEXT'          => 'Rawtext'                     , // TODO: Review
	'LBL_VTIGER'                   => 'Vtiger'                      , // TODO: Review
	'LBL_EXAMPLE_FIELD_NAME'       => 'Field'                       , // TODO: Review
	'LBL_NOTIFY_OWNER'             => 'notify_owner'                , // TODO: Review
	'LBL_ANNUAL_REVENUE'           => 'annual_revenue'              , // TODO: Review
	'LBL_EXPRESSION_EXAMPLE2'      => 'if mailingcountry == \'India\' then concat(firstname,\' \',lastname) else concat(lastname,\' \',firstname) end', // TODO: Review
	'LBL_FROM' => 'À partir de',
	'Optional' => 'Optionnel',
	'LBL_ADD_TASK'                 => 'Ajouter une action'          ,
    'Portal Pdf Url' =>'Portail Client lien PDF',
	'LBL_ADD_TEMPLATE' => 'Ajouter un modèle',
	'LBL_LINEITEM_BLOCK_GROUP' => 'Bloc LineItems pour groupe fiscal',
    'LBL_LINEITEM_BLOCK_INDIVIDUAL' => 'Bloc LineItems pour impôts des particuliers',
    'LBL_ADD_PDF' => 'Ajouter pdf',
	
	
	//Translation for module
	'Calendar' => 'Pour faire',
	'Send Mail' => 'Envoyer un mail',
	'Invoke Custom Function' => 'Appelez la fonction personnalisée',
	'Create Todo' => 'Créer Todo',
	'Create Event' => 'Créer un événement',
	'Update Fields' => 'Update Fields',
	'Create Entity'                => 'Créer enregistrement'       ,
	'SMS Task' => 'Tâche SMS',
	'Mobile Push Notification' => 'Mobile notification push',
	'LBL_ACTION_TYPE' => 'Type d\'action (Count actif)',
	'LBL_VTEmailTask' => 'Email',
    'LBL_VTEntityMethodTask' => 'Fonction personnalisée',
    'LBL_VTCreateTodoTask' => 'Tâche',
    'LBL_VTCreateEventTask' => 'Événement',
    'LBL_VTUpdateFieldsTask' => 'Actualiser champ',
    'LBL_VTSMSTask' => 'SMS', 
    'LBL_VTPushNotificationTask' => 'Notification mobile',
    'LBL_VTCreateEntityTask' => 'Créer enregistrement',
	'LBL_MAX_SCHEDULED_WORKFLOWS_EXCEEDED' => 'Nombre maximum (%s) des flux de travail programmés a été dépassé',

  'LBL_EDITING_WORKFLOW' => 'Flux De Travail De Montage',
  'LBL_ADD_RECORD' => 'Nouveau Flux De Travail',
  'ON_SCHEDULE' => 'L\'annexe',
  'LBL_RUN_WORKFLOW' => 'Exécuter Le Workflow',
  'LBL_AT_TIME' => 'Au Moment',
  'LBL_HOURLY' => 'Horaire',
  'ENTER_FROM_EMAIL_ADDRESS' => 'Entrez une adresse email',
  'LBL_DAILY' => 'Quotidien',
  'LBL_WEEKLY' => 'Hebdomadaire',
  'LBL_ON_THESE_DAYS' => 'Ces jours',
  'LBL_MONTHLY_BY_DATE' => 'Mensuel par Date',
  'LBL_MONTHLY_BY_WEEKDAY' => 'Mensuelle par jour de la Semaine',
  'LBL_YEARLY' => 'Annuelle',
  'LBL_SPECIFIC_DATE' => 'Sur La Date Précise',
  'LBL_CHOOSE_DATE' => 'Choisir Une Date',
  'LBL_SELECT_MONTH_AND_DAY' => 'Sélectionnez le Mois et la Date',
  'LBL_SELECTED_DATES' => 'Les Dates Sélectionnées',
  'LBL_EXCEEDING_MAXIMUM_LIMIT' => 'Limite maximale dépassée',
  'LBL_NEXT_TRIGGER_TIME' => 'Suivant l\'heure de déclenchement sur',
  'LBL_MESSAGE' => 'Message',
  'LBL_WORKFLOW_NAME' => 'Nom Du Flux De Travail',
  'LBL_TARGET_MODULE' => 'Objectif Du Module',
  'LBL_WORKFLOW_TRIGGER' => 'Flux De Travail De Déclenchement',
  'LBL_TRIGGER_WORKFLOW_ON' => 'Déclencher Un Workflow Sur',
  'LBL_RECORD_CREATION' => 'Création De La Notice',
  'LBL_RECORD_UPDATE' => 'Mise À Jour Des Enregistrements',
  'LBL_TIME_INTERVAL' => 'Intervalle De Temps',
  'LBL_RECURRENCE' => 'La récurrence',
  'LBL_FIRST_TIME_CONDITION_MET' => 'Seulement la première fois que trois conditions sont remplies',
  'LBL_EVERY_TIME_CONDITION_MET' => 'Chaque fois que trois conditions sont remplies',
  'LBL_WORKFLOW_CONDITION' => 'Condition De Flux De Travail',
  'LBL_WORKFLOW_ACTIONS' => 'Actions De Flux De Travail',
  'LBL_DELAY_ACTION' => 'Retarder L\'Action',
  'LBL_FREQUENCY' => 'Fréquence',
  'LBL_SELECT_FIELDS' => 'Sélectionnez Les Champs',
  'LBL_INCLUDES_CREATION' => 'Comprend La Création',
  'LBL_ACTION_FOR_WORKFLOW' => 'D\'Action pour les Flux de travail',
  'LBL_WORKFLOW_SEARCH' => 'Recherche par Nom',

);
$jsLanguageStrings = array(
	'JS_STATUS_CHANGED_SUCCESSFULLY' => 'Status changed Successfully' , // TODO: Review
	'JS_TASK_DELETED_SUCCESSFULLY' => 'Action supprimé avec succès',
	'JS_SAME_FIELDS_SELECTED_MORE_THAN_ONCE' => 'Same fields selected more than once', // TODO: Review
	'JS_WORKFLOW_SAVED_SUCCESSFULLY' => 'Workflow saved successfully' , // TODO: Review
    'JS_CHECK_START_AND_END_DATE'=>'Date de fin et lheure doivent être supérieure ou égale à la Date de début et lheure',

  'JS_TASK_STATUS_CHANGED' => 'Le statut de la tâche modifiée avec succès.',
  'JS_WORKFLOWS_STATUS_CHANGED' => 'État du flux de travail modifié avec succès.',
  'VTEmailTask' => 'Envoyer Un Mail',
  'VTEntityMethodTask' => 'Invoquer La Fonction Personnalisée',
  'VTCreateTodoTask' => 'Créer Une Tâche',
  'VTCreateEventTask' => 'Créer Un Événement',
  'VTUpdateFieldsTask' => 'Mise À Jour Des Champs',
  'VTSMSTask' => 'SMS Tâche',
  'VTPushNotificationTask' => 'Mobile De Notification Push',
  'VTCreateEntityTask' => 'Créer Un Enregistrement',
  'LBL_EXPRESSION_INVALID' => 'Expression Non Valide',

);