<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * Portions created by JPL TSolucio, S.L. are Copyright (C) jpl tsolucio.
 * All Rights Reserved.
 * ********************************************************************************
 *  Language     : Español es_es
 *  Version      : 6.0.0
 *  Created Date : 2012-10-26
 *  Author       : JPL <PERSON>olucio, S. L. <PERSON>
 *  Last change  : 2013-05-03
 *  Author       : JPL TSolucio, S. L. <PERSON>
 ************************************************************************************/
$languageStrings = array(
	'Services'                     => 'Servicios',
	'SINGLE_Services'              => 'Servicio',
	'LBL_ADD_RECORD'               => 'Añadir Servicio',
	'LBL_RECORDS_LIST'             => 'Lista de Servicios',
	'LBL_SERVICE_INFORMATION'      => 'Detalles de Servicio',
	'LBL_MORE_CURRENCIES'          => 'más monedas',
	'LBL_PRICES'                   => 'Precios Servicio',
	'LBL_PRICE'                    => 'Precio',
	'LBL_RESET_PRICE'              => 'Restaurar Precio',
	'LBL_RESET'                    => 'Restaurar',
	'LBL_ADD_TO_PRICEBOOKS'        => 'Añadir a Tarifas',
	'Service Name'                 => 'Nombre Servicio',
	'Service Active'               => 'Activo',
	'Service Category'             => 'Categoría',
	'Service No'                   => 'Número Servicio',
	'Owner'                        => 'Responsable',
	'No of Units'                  => 'Número de Unidades',
	'Commission Rate'              => 'Comisión',
	'Price'                        => 'Precio',
	'Usage Unit'                   => 'Unidades de consumo',
	'Tax Class'                    => 'Tipo impuesto',
	'Website'                      => 'Página Web',
);