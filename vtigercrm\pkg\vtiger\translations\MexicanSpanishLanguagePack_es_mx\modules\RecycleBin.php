<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
    'Recycle Bin'                  => 'Papelera de reciclaje'                 , 
	'RecycleBin'                   => 'Papelera de reciclaje'                 , 
	'LBL_SELECT_MODULE'            => 'Seleccione el módulo'               , 
	'LBL_EMPTY_RECYCLEBIN'         => 'Vaciar papelera de reciclaje'           , 
	'LBL_RESTORE'                  => 'Restaurar'                     , 
	'LBL_NO_PERMITTED_MODULES'     => 'No hay módulos permitidos disponibles', 
	'LBL_RECORDS_LIST'             => 'Lista de papelera de reciclaje'            , 
	'LBL_NO_RECORDS_FOUND'         => 'No se encontraron registros para restaurar en el módulo', 
);
$jsLanguageStrings = array(
	'JS_MSG_EMPTY_RB_CONFIRMATION' => '¿Está seguro de que quiere eliminar de forma permanente todos los registros borrados de su base de datos?', 
	'JS_LBL_RESTORE_RECORDS_CONFIRMATION' => '¿Está seguro de que quiere restaurar los registros?',
    'JS_LBL_RESTORE_RECORD_CONFIRMATION' => '¿Está seguro de que desea restaurar el registro?',
    'JS_RESTORING_RECORD' => 'Restaurando el registro',
    'JS_RESTORE_AND_UNTRASH_FILE_IN_DRIVE' => 'Restaurar en Vtiger y en Drive',

  'JS_RESTORING_RECORDS' => 'Restaurando registros',

);
