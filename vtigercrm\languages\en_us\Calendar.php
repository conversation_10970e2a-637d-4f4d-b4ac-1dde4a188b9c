<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
    // Basic Strings
	'Calendar'=>'Calendar',
	'SINGLE_Calendar' => 'Task',
	'SINGLE_Events' => 'Event',
	'LBL_ADD_TASK' => 'Add Task',
	'LBL_ADD_EVENT' => 'Add Event',
	'LBL_RECORDS_LIST' => 'Calendar List',
	'LBL_EVENTS' => 'Events',
	'LBL_TODOS' => 'Task',
	'LBL_CALENDAR_SETTINGS' => 'Calendar Settings',
	'LBL_CALENDAR_SHARING' => 'Calendar Sharing',
	'LBL_DEFAULT_EVENT_DURATION' => 'Default Event Duration',
	'LBL_CALL' => 'Call',
	'LBL_OTHER_EVENTS' => 'Other Events',
	'LBL_MINUTES' => 'Minutes',
	'LBL_SELECT_USERS' => 'Select Users',
	'LBL_EVENT_OR_TASK' => 'Event / Task',
        'LBL_DEFAULT_STATUS_TYPE' => 'Default Status & Type',
        'LBL_STATUS' => 'Status',
        'LBL_TYPE' => 'Type',
	// Blocks
	'LBL_TASK_INFORMATION' => 'Task Details',
    'LBL_EVENT_INFORMATION'=> 'Event Details',

	//Fields
	'Subject' => 'Subject',
	'Start Date & Time' => 'Start Date & Time',
	'Activity Type'=>'Activity Type',
	'Send Notification'=>'Send Notification',
	'Location'=>'Location',
	'End Date & Time' => 'End Date & Time',
	'Visibility' => 'Visibility',
	'Recurrence' => 'Repeat',
	
	//Visibility picklist values
	'Private' => 'Private',
	'Public' => 'Public',
	
	//Side Bar Names
	'LBL_ACTIVITY_TYPES' => 'Activity Types',
	'LBL_CONTACTS_SUPPORT_END_DATE' => 'Support End Date',
	'LBL_CONTACTS_BIRTH_DAY' => 'Date of Birth',
	'LBL_ADDED_CALENDARS' => 'Added Calendars',


	//Activity Type picklist values
	'Call' => 'Call',
	'Meeting' => 'Meeting',
	'Task' => 'Task',

	//Status picklist values
	'Planned' => 'Planned',
	'Completed' => 'Completed',
	'Pending Input' => 'Pending Input',
	'Not Started' => 'Not Started',
	'Deferred' => 'Deferred',
	'Held' => 'Held',
	'Not Held' => 'Not Held',
	
	//Priority picklist values
	'Medium' => 'Medium',

	'LBL_CHANGE_OWNER' => 'Change Owner',

	'LBL_EVENT' => 'Event',
	'LBL_TASK' => 'Task',
	'LBL_TASKS' => 'Task',

	'LBL_RECORDS_LIST' => 'List View',
	'LBL_CALENDAR_VIEW' => 'My Calendar',
	'LBL_SHARED_CALENDAR' => 'Shared Calendar',

	//Repeat Lables - used by getTranslatedString
	'LBL_DAY0' => 'Sunday',
	'LBL_DAY1' => 'Monday',
	'LBL_DAY2' => 'Tuesday',
	'LBL_DAY3' => 'Wednesday',
	'LBL_DAY4' => 'Thursday',
	'LBL_DAY5' => 'Friday',
	'LBL_DAY6' => 'Saturday',

	'first' => 'First',
	'last' => 'Last',
	'LBL_DAY_OF_THE_MONTH' => 'day of the month',
	'LBL_ON' => 'on',

	'Daily'=>'Day(s)',
	'Weekly'=>'Week(s)',
	'Monthly'=>'Month(s)',
	'Yearly'=>'Year',
	
	//Import and Export Labels
	'LBL_IMPORT_RECORDS' => 'Import Records',
	'LBL_RESULT' => 'Result',
	'LBL_FINISH' => 'Finish',
	'LBL_TOTAL_TASKS_IMPORTED' => 'No. of Tasks Successfully Imported ',
	'LBL_TOTAL_TASKS_SKIPPED' => 'No. of Tasks Skipped as they were missing one or more required field ',
	'LBL_TOTAL_EVENTS_IMPORTED' => 'No. of Events Successfully Imported ',
	'LBL_TOTAL_EVENTS_SKIPPED' => 'No. of Events Skipped as they were missing one or more required field ',
	'LBL_TOTAL_EVENTS_DUPLICATED' => 'No. of duplicate Events skipped',
	'LBL_TOTAL_TASKS_DUPLICATED' => 'No. of duplicate Tasks skipped',
	
	'ICAL_FORMAT' => 'iCal Format',
	'LBL_LAST_IMPORT_UNDONE'=>'Your Last Import Was Undone',
	'LBL_UNDO_LAST_IMPORT' => 'Undo Last Import',
	
	//Fixing colors for Shared Calendar and My Calendar
	'LBL_EDIT_COLOR' => 'Edit Color',
	'LBL_ADD_CALENDAR_VIEW' => 'Add Calendar View',
	'LBL_SELECT_USER_CALENDAR' => 'Select User Calendar',
	'LBL_SELECT_CALENDAR_COLOR' => 'Select Calendar Color',
	'LBL_EDITING_CALENDAR_VIEW' => 'Editing Calendar View',
	'LBL_DELETE_CALENDAR' => 'Delete Calendar',
	'LBL_SELECT_ACTIVITY_TYPE' => 'Select Activity Type',
	'Tasks' => 'Tasks',
	'LBL_SELECT_FIELDS_FOR_RANGE' => 'Select Fields for Range',
	'LBL_DUPLICATE_VIEW_EXIST' => 'Calendar View already exists',
    
    // For Event Invitation
    'LBL_ACTIVITY_NOTIFICATION' => 'This is a notification that an activity is assigned to you that has been',
    'LBL_ACTIVITY_INVITATION' => 'You have been invited for an activity',
    'LBL_DETAILS_STRING' => 'The details are',
    'LBL_CREATED' => 'created',
    'LBL_UPDATED' => 'updated',
    'Due Date' => 'Due Date',
    'Priority' => 'Priority',
    'Related To' => 'Related To',
    'LBL_CONTACT_LIST' => 'Contact List',
    'LBL_APP_DESCRIPTION' => 'Description',
    'LBL_REGARDS_STRING' => 'Thanks & Regards',
    'LBL_EVENT_INFORMATION' => 'Event Details',
	'LBL_UPDATED_INVITATION' => 'Updated Invitation',
	'LBL_INVITATION' => 'Invitation',
	
	//Recurring Events
	'LBL_EDIT_RECURRING_EVENT' => 'Edit Recurring Event',
	'LBL_ALL_EVENTS_EDIT_INFO' => 'All events in the series will be changed.</br> Any changes made to other events will be kept.',
	'LBL_FUTURE_EVENTS_EDIT_INFO' => 'This and all the following events will be changed.</br> Any changes to future events will be lost.',
	'LBL_ONLY_THIS_EVENT_EDIT_INFO' => 'All other events in the series will remain the same.',
	'LBL_EDIT_RECURRING_EVENTS_INFO' => 'Would you like to save the changes for',
	
	'LBL_DELETE_RECURRING_EVENT' => 'Delete Recurring Event',
	'LBL_ALL_EVENTS_DELETE_INFO' => 'All events in the series will be deleted.',
	'LBL_FUTURE_EVENTS_DELETE_INFO' => 'This and all the following events will be deleted.',
	'LBL_ONLY_THIS_EVENT_DELETE_INFO' => 'All other events in the series will remain the same.',
	'LBL_DELETE_RECURRING_EVENTS_INFO' => 'Would you like to delete only this event, all events in the series or this and all future events in the series?',
	'LBL_ONLY_THIS_EVENT' => 'Only This Event',
	'LBL_FUTURE_EVENTS' => 'Following Events',
	'LBL_ALL_EVENTS' => 'All Events',
	
	//Reminder Email
	'LBL_REMINDER_NOTIFICATION' => 'This is a reminder notification for the Activity',
    'LBL_SELECT_EVENT_TYPE' => 'Activity Type',
    'LBL_THIS_WEEK' => 'This Week',
    'LBL_ADD_TASK_AND_PRESS_ENTER' => 'Add Task and press Enter',

	//Months
	'LBL_JANUARY' => 'January',
	'LBL_FEBRUARY' => 'February',
	'LBL_MARCH' => 'March',
	'LBL_APRIL' => 'April',
	'LBL_MAY' => 'May',
	'LBL_JUNE' => 'June',
	'LBL_JULY' => 'July',
	'LBL_AUGUST' => 'August',
	'LBL_SEPTEMBER' => 'September',
	'LBL_OCTOBER' => 'October',
	'LBL_NOVEMBER' => 'November',
	'LBL_DECEMBER' => 'December',
	'LBL_CLICK_HERE_TO_VIEW' => 'Click here to view',
);

$jsLanguageStrings = array(
	'LBL_ADD_EVENT_TASK' => 'Add Event/Task',
	'JS_TASK_IS_SUCCESSFULLY_ADDED_TO_YOUR_CALENDAR' => 'Task is successfully added to your Calendar',
        'LBL_CANT_SELECT_CONTACT_FROM_LEADS' => 'Cannot select related Contacts for Leads',
        'JS_FUTURE_EVENT_CANNOT_BE_HELD' => 'Cannot Be Held For Future',
	
	//Calendar view label translation
	'LBL_MONTH' => 'Month',
	'LBL_TODAY' => 'Today',
    'LBL_TOMORROW' => 'Tomorrow',
	'LBL_DAY' => 'Day',
	'LBL_WEEK' => 'Week',
	
	'LBL_SUNDAY' => 'Sunday',
	'LBL_MONDAY' => 'Monday',
	'LBL_TUESDAY' => 'Tuesday',
	'LBL_WEDNESDAY' => 'Wednesday',
	'LBL_THURSDAY' => 'Thursday',
	'LBL_FRIDAY' => 'Friday',
	'LBL_SATURDAY' => 'Saturday',
	
	'LBL_SUN' => 'Sun',
	'LBL_MON' => 'Mon',
	'LBL_TUE' => 'Tue',
	'LBL_WED' => 'Wed',
	'LBL_THU' => 'Thu',
	'LBL_FRI' => 'Fri',
	'LBL_SAT' => 'Sat',
	
	'LBL_JANUARY' => 'January',
	'LBL_FEBRUARY' => 'February',
	'LBL_MARCH' => 'March',
	'LBL_APRIL' => 'April',
	'LBL_MAY' => 'May',
	'LBL_JUNE' => 'June',
	'LBL_JULY' => 'July',
	'LBL_AUGUST' => 'August',
	'LBL_SEPTEMBER' => 'September',
	'LBL_OCTOBER' => 'October',
	'LBL_NOVEMBER' => 'November',
	'LBL_DECEMBER' => 'December',
	
	'LBL_JAN' => 'Jan',
	'LBL_FEB' => 'Feb',
	'LBL_MAR' => 'Mar',
	'LBL_APR' => 'Apr',
	'LBL_MAY' => 'May',
	'LBL_JUN' => 'Jun',
	'LBL_JUL' => 'Jul',
	'LBL_AUG' => 'Aug',
	'LBL_SEP' => 'Sep',
	'LBL_OCT' => 'Oct',
	'LBL_NOV' => 'Nov',
	'LBL_DEC' => 'Dec',
	'LBL_ALL_DAY' => 'All-Day',
	//End
	
	//Fixing colors for Shared Calendar and My Calendar
	'JS_CALENDAR_VIEW_COLOR_UPDATED_SUCCESSFULLY' => 'Calendar view color updated successfully',
	'JS_CALENDAR_VIEW_DELETE_CONFIRMATION' => 'Are you sure you want to delete this Calendar view ?',
	'JS_CALENDAR_VIEW_ADDED_SUCCESSFULLY' => 'Calendar View added successfully',
	'JS_CALENDAR_VIEW_DELETED_SUCCESSFULLY' => 'Calendar View deleted successfully',
	'JS_NO_CALENDAR_VIEWS_TO_ADD' => 'No Calendar View to add',
	'JS_EDIT_CALENDAR' => 'Edit Calendar',
    
    //v7
    'JS_EVENT_UPDATED' => 'Event Updated',
    'JS_NO_EVENTS_F0R_THE_DAY' => 'No events for the day',
    'LBL_AGENDA' => 'Agenda',
    'JS_CALENDAR_VIEW_YOU_ARE_EDITING_NOT_FOUND' => 'Calendar view not found',
    
    'JS_DELETE' => 'Delete',
    'JS_EDIT' => 'Edit',
    'JS_MARK_AS_HELD' => 'Mark as held',
    'JS_CREATE_FOLLOW_UP' => 'Create follow up',
    'JS_RECURRING_EVENT' => 'Recurring event',
    'JS_DETAILS' => 'More&nbsp;Details',
    'JS_CHECK_START_AND_END_DATE'=>'End Date & Time should be greater than or equal to Start Date & Time',
    'JS_CHECK_START_AND_END_DATE_SHOULD_BE_GREATER'=> 'End Date & Time should be greater than Start Date & Time',
);
