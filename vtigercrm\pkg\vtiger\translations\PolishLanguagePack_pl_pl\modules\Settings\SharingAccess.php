<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'LBL_ADD_CUSTOM_RULE' => 'Dodaj przykładową role',
	'Read Only' => 'R',
	'Read Write' => 'R+W',
	'LBL_CREATE_CUSTOM_RULE' => 'Dodaj now<PERSON> przykładową role',
	'LBL_CAN_ACCESSED_BY' => 'Może być dostępna przez',
	'LBL_PRIVILEGES' => 'Uprawnienia',
	'LBL_SHARING_RULE' =>  'Zasady udostępniania',
	'LBL_RULE_NO' => 'Numer reguły',
	'LBL_PRIVILAGES' => 'Uprawnienia',

  'SharingAccess' => 'Zasady Udostępniania',
  'Accounts' => 'Organizacji I Kontaktów',
  'LBL_ADD_CUSTOM_RULE_TO' => 'Dodaj własne zasady',
  'LBL_MODULE' => 'Moduł',
  'LBL_ADVANCED_SHARING_RULES' => 'Zaawansowane Ustawienia Udostępniania Zasady',
  'LBL_WITH_PERMISSIONS' => 'Za Zgodą',
  'LBL_APPLY_NEW_SHARING_RULES' => 'Stosować Nowe Zasady Udostępniania',
  'LBL_READ' => 'Przeczytać',
  'LBL_READ_WRITE' => 'Czytać i pisać',
  'LBL_CUSTOM_ACCESS_MESG' => 'Nie konfigurowalnych reguł dostępu, określonych',
  'SINGLE_Groups' => 'Grupa',
  'SINGLE_Roles' => 'Rola',
  'SINGLE_RoleAndSubordinates' => 'RoleAndSubordinate',

);