<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'Services'                     => 'Services'                    , 
	'SINGLE_Services'              => 'Service'                     , 
	'LBL_ADD_RECORD'               => 'Ajouter un Service'          , 
	'LBL_RECORDS_LIST'             => 'Liste des Services'               , 
	'LBL_SERVICE_INFORMATION'      => 'Information du Service'      , 
	'LBL_MORE_CURRENCIES'          => 'Plus de devises'             , 
	'LBL_PRICES'                   => 'Service Prices'              , 
	'LBL_PRICE'                    => 'Prix'                        , 
	'LBL_RESET_PRICE'              => 'Prix remisé'                , 
	'LBL_RESET'                    => 'Remise'                      , 
	'LBL_ADD_TO_PRICEBOOKS'        => 'Add to PriceBooks'           , 
	'Service Name'                 => 'Service'                     , 
	'Service Active'               => 'Actif'                       , 
	'Service Category'             => 'Catégorie'                  , 
	'Service No'                   => 'N° Service'                 , 
	'Owner'                        => 'Propriétaire'               , 
	'No of Units'                  => 'Nombre d’unités'          , 
	'Commission Rate'              => 'Commission (en %)'           , 
	'Price'                        => 'Prix'                        , 
	'Usage Unit'                   => 'Unité de vente'             , 
	'Tax Class'                    => 'Type de taxe'                , 
	'Website'                      => 'Site WEB'                    , 
);