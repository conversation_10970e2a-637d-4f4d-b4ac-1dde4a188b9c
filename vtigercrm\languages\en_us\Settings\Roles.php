<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	// Basic Strings
	'Roles' => 'Roles',
	'SINGLE_Roles' => 'Role',
	'LBL_ADD_RECORD' => 'Add Role',
	'LBL_DELETE_ROLE' => 'Delete Role',
	'LBL_TRANSFER_OWNERSHIP' => 'Transfer Ownership',
	'LBL_TO_OTHER_ROLE' => 'To other Role',
	'LBL_CLICK_TO_EDIT_OR_DRAG_TO_MOVE' => 'Click to edit/Drag to move',
	'LBL_ASSIGN_ROLE' => 'Assign Role',
	'LBL_CHOOSE_PROFILES' => 'Choose profiles',
	'LBL_COPY_PRIVILEGES_FROM' => 'Copy privileges from',
    'LBL_TRANSFER_TO_OTHER_ROLE' =>'Transfer ownership to other role',
	
	//Edit View
	'LBL_PROFILE' => 'Profile',
	'LBL_REPORTS_TO' => 'Reports To',
	'LBL_NAME' => 'Name',
	'LBL_ASSIGN_NEW_PRIVILEGES' => 'Assign privileges directly to Role',
	'LBL_ASSIGN_EXISTING_PRIVILEGES' => 'Assign priviliges from existing profiles',
	'LBL_PRIVILEGES' => 'Privileges',
	'LBL_DUPLICATES_EXIST' => 'Duplicate Role Exists',
	
	//Assign Records to
	'LBL_CAN_ASSIGN_RECORDS_TO' => 'Can Assign Records To',
	'LBL_ALL_USERS' => 'All Users',
	'LBL_USERS_WITH_LOWER_LEVEL' => 'Users having Subordinate Role',
	'LBL_USERS_WITH_SAME_OR_LOWER_LEVEL' => 'Users having Same Role or Subordinate Role',
    
    /*Vtiger7 String*/
    'LBL_EDIT_ROLE' => 'Edit Role',
	'LBL_CREATE_ROLE' => 'Create Role',
);

$jsLanguageStrings = array(
	'JS_PERMISSION_DENIED' => 'Permissions Denied',
	'JS_NO_PERMISSIONS_TO_MOVE' => 'No Permissions to Move',
);