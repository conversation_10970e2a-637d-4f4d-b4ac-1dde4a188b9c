<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
    'Project'                      => 'Project'                     , 
	'SINGLE_Project'               => 'Project'                     , 
	'LBL_ADD_RECORD'               => 'Voeg Project toe'            , 
	'LBL_RECORDS_LIST'             => 'Projectenlijst'              , 
	'LBL_PROJECT_INFORMATION'      => 'Projectinformatie'           , 
	'Project Name'                 => 'Project naam'                , 
	'Start Date'                   => 'Startdatum'                  , 
	'Target End Date'              => 'Doel Einddatum'              , 
	'Actual End Date'              => 'Actuele Einddatum'           , 
	'Project No'                   => 'Projectnummer'               , 
	'Target Budget'                => 'Doel Budget'                 , 
	'Project Url'                  => 'Project Url'                 , 
	'Progress'                     => 'Voortgang'                   , 
        'Type'                         => 'Type'                        , 
    'Related to'                   => 'Gerelateerd aan'             ,
	'LBL_TASKS_OPEN'               => 'Taken Open'                  , 
	'LBL_TASKS_DUE'                => 'Taken Due'                   , 
	'LBL_TASKS_COMPLETED'          => 'Tasken Completed'            , 
	'LBL_PEOPLE'                   => 'Mensen'                      , 
	'LBL_CHARTS'                   => 'Grafieken'                   , 
	'LBL_TASKS_LIST'               => 'Tasks List'                  , // TODO: Review
	'LBL_MILESTONES'               => 'Milestones'                  , // TODO: Review
	'LBL_TASKS'                    => 'Tasks'                       , // TODO: Review
	'LBL_STATUS_IS'                => 'Status is'                   , // TODO: Review
	'LBL_STATUS'                   => 'Status'                      , // TODO: Review
	'LBL_TICKET_PRIORITY'          => 'Priority'                    , // TODO: Review
	'LBL_DOWNLOAD_FILE'            => 'Download File'               , // TODO: Review
    'LBL_MILESTONES_LIST'          => 'Mijlpalen Lijst'             ,
    'LBL_TASKS_HIGH'               => 'Hoge prioriteit'             ,
    'LBL_TASKS_NORMAL'             => 'Normaal Prioriteit'          ,
    'LBL_TASKS_LOW'                => 'Lage prioriteit'             ,
    'LBL_TASKS_OTHER'              => 'Andere prioriteiten'         ,  
    'LBL_SELECT_PROGRESS'			=> 'Progress - Alle',
	'LBL_SELECT_STATUS'				=> 'Status - Alle',
	
	//picklist values
	'prospecting' => 'Prospectie',
    'in progress' => 'In Progress',
    'initiated' => 'Gestart',
	'waiting for feedback' => 'Wachten op feedback',
    'on hold' => 'On Hold',
    'archived' => 'Gearchiveerd',
    'completed' => 'Voltooid',
    'delivered' => 'Geleverd',
	'administrative' => 'Administratief',
    'operative' => 'Arbeider',
    'other' => 'Ander',
    'low' => 'Laag',
    'normal' => 'Normaal',
    'high' => 'Hoog',
    
    //Gantt chart 
    'LBL_CHART' => 'Gantt-diagram',
    'LBL_PROGRESS_CHART' => 'Gantt-diagram',
    'LBL_TASK_NAME' => 'taak naam',
    'LBL_START_DATE' => 'startdatum',
    'LBL_END_DATE' => 'einddatum',
    'LBL_DURATION' => 'duur',
    'LBL_INFO' => 'Info',
    'LBL_GANTT_INFO1' => 'Schuif aan de rechterkant van de taak te duur uit te breiden',
    'LBL_GANTT_INFO2' => 'Drag links of in het midden van de taak om begin-en einddatum te veranderen.',
    'LBL_EDIT_PROJECT_TASK_STATUS_COLOR' => 'Bewerk Project Taak Status Kleur',
    'LBL_SELECT_PROJECT_TASK_STATUS_COLOR' => 'Selecteer Project Task Status Kleur',
    'LBL_SELECT_STATUS' => 'Selecteer Status',
    'LBL_EDIT_COLOR' => 'Kleur bewerken',
);

$jsLanguageStrings = array(
    'JS_COLOR_SAVED_SUCESSFULLY' => 'Kleur Opgeslagen succesvol',
);