<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_NEW'                      => 'New'                         , // TODO: Review
	'LBL_WORKFLOW'                 => 'Workflow'                    , // TODO: Review
	'LBL_CREATING_WORKFLOW'        => 'Creating WorkFlow'           , // TODO: Review
	'LBL_NEXT'                     => 'Next'                        , // TODO: Review
	'LBL_STEP_1'                   => 'Step 1'                      , // TODO: Review
	'LBL_ENTER_BASIC_DETAILS_OF_THE_WORKFLOW' => 'Enter basic details of the Workflow', // TODO: Review
	'LBL_SPECIFY_WHEN_TO_EXECUTE'  => 'Specify when to execute this Workflow', // TODO: Review
	'ON_FIRST_SAVE'                => 'Only on the first save'      , // TODO: Review
	'ONCE'                         => 'Until the first time the condition is true', // TODO: Review
	'ON_EVERY_SAVE'                => 'Every time the record is saved', // TODO: Review
	'ON_MODIFY'                    => 'Every time a record is modified', // TODO: Review
	'MANUAL'                       => 'System'                      , // TODO: Review
	'SCHEDULE_WORKFLOW'            => 'Schedule Workflow'           , // TODO: Review
	'ADD_CONDITIONS'               => 'Add Conditions'              , // TODO: Review
	'ADD_TASKS'                    => 'Eylemler ekle'               ,
	'LBL_EXPRESSION'               => 'Expression'                  , // TODO: Review
	'LBL_FIELD_NAME'               => 'Field'                       , // TODO: Review
	'LBL_SET_VALUE'                => 'Set Value'                   , // TODO: Review
	'LBL_USE_FIELD'                => 'Use Field'                   , // TODO: Review
	'LBL_USE_FUNCTION'             => 'Use Function'                , // TODO: Review
	'LBL_RAW_TEXT'                 => 'Raw text'                    , // TODO: Review
	'LBL_ENABLE_TO_CREATE_FILTERS' => 'Enable to create Filters'    , // TODO: Review
	'LBL_CREATED_IN_OLD_LOOK_CANNOT_BE_EDITED' => 'This workflow was created in older look. Conditions created in older look cannot be edited. You can choose to recreate the conditions, or use the existing conditions without changing them.', // TODO: Review
	'LBL_USE_EXISTING_CONDITIONS'  => 'Use existing conditions'     , // TODO: Review
	'LBL_RECREATE_CONDITIONS'      => 'Recreate Conditions'         , // TODO: Review
	'LBL_SAVE_AND_CONTINUE'        => 'Save & Continue'             , // TODO: Review
	'LBL_ACTIVE'                   => 'Active'                      , // TODO: Review
	'LBL_TASK_TYPE'                => 'Eylem Türü'                ,
	'LBL_TASK_TITLE'               => 'Eylem Adı'                  ,
	'LBL_ADD_TASKS_FOR_WORKFLOW'   => 'İş Akışı için Eylem Ekle',
	'LBL_EXECUTE_TASK'             => 'Eylem Yürütme'             ,
	'LBL_SELECT_OPTIONS'           => 'Select Options'              , // TODO: Review
	'LBL_ADD_FIELD'                => 'Add Field'                   , // TODO: Review
	'LBL_ADD_TIME'                 => 'Add time'                    , // TODO: Review
	'LBL_TITLE'                    => 'Title'                       , // TODO: Review
	'LBL_PRIORITY'                 => 'Priority'                    , // TODO: Review
	'LBL_ASSIGNED_TO'              => 'Assigned to'                 , // TODO: Review
	'LBL_TIME'                     => 'Time'                        , // TODO: Review
	'LBL_DUE_DATE'                 => 'Due Date'                    , // TODO: Review
	'LBL_THE_SAME_VALUE_IS_USED_FOR_START_DATE' => 'The same value is used for the start date', // TODO: Review
	'LBL_EVENT_NAME'               => 'Event Name'                  , // TODO: Review
	'LBL_TYPE'                     => 'Type'                        , // TODO: Review
	'LBL_METHOD_NAME'              => 'Method Name'                 , // TODO: Review
	'LBL_RECEPIENTS'               => 'Recepients'                  , // TODO: Review
	'LBL_ADD_FIELDS'               => 'Add Fields'                  , // TODO: Review
	'LBL_SMS_TEXT'                 => 'Sms Text'                    , // TODO: Review
	'LBL_SET_FIELD_VALUES'         => 'Set Field Values'            , // TODO: Review
	'LBL_IN_ACTIVE'                => 'In Active'                   , // TODO: Review
	'LBL_SEND_NOTIFICATION'        => 'Send Notification'           , // TODO: Review
	'LBL_START_TIME'               => 'Start Time'                  , // TODO: Review
	'LBL_START_DATE'               => 'Start Date'                  , // TODO: Review
	'LBL_END_TIME'                 => 'End Time'                    , // TODO: Review
	'LBL_END_DATE'                 => 'End Date'                    , // TODO: Review
	'LBL_ENABLE_REPEAT'            => 'Enable Repeat'               , // TODO: Review
	'LBL_NO_METHOD_IS_AVAILABLE_FOR_THIS_MODULE' => 'No method is available for this module', // TODO: Review
	'LBL_FINISH'                   => 'Finish'                      , // TODO: Review
	'LBL_NO_TASKS_ADDED'           => 'Hiçbir Eylemleri'           ,
	'LBL_CANNOT_DELETE_DEFAULT_WORKFLOW' => 'You Cannot delete default Workflow', // TODO: Review
	'LBL_MODULES_TO_CREATE_RECORD' => 'Bir kaydı oluşturun'       ,
	'LBL_EXAMPLE_EXPRESSION'       => 'Expression'                  , // TODO: Review
	'LBL_EXAMPLE_RAWTEXT'          => 'Rawtext'                     , // TODO: Review
	'LBL_VTIGER'                   => 'Vtiger'                      , // TODO: Review
	'LBL_EXAMPLE_FIELD_NAME'       => 'Field'                       , // TODO: Review
	'LBL_NOTIFY_OWNER'             => 'notify_owner'                , // TODO: Review
	'LBL_ANNUAL_REVENUE'           => 'annual_revenue'              , // TODO: Review
	'LBL_EXPRESSION_EXAMPLE2'      => 'if mailingcountry == \'India\' then concat(firstname,\' \',lastname) else concat(lastname,\' \',firstname) end', // TODO: Review
	'LBL_FROM' => 'Itibaren',
	'Optional' => 'Isteğe bağlı',
	'LBL_ADD_TASK'                 => 'Eylem Ekle'                  ,
    'Portal Pdf Url' =>'Müşteri Portalı Pdf bağlantı',
    'LBL_ADD_TEMPLATE' => 'Şablon ekle',
    'LBL_LINEITEM_BLOCK_GROUP' => 'LineItems bloğu için Grup Taxt',
    'LBL_LINEITEM_BLOCK_INDIVIDUAL' => 'LineItems bloğu için bireysel vergi',
    'LBL_ADD_PDF' => 'ekle pdf',
	
	//Translation for module
	'Calendar'						=> 'Yapılacak'						,
	'Send Mail'						=> 'Posta Gönder',
	'Invoke Custom Function'		=> 'Özel Fonksiyonu çağırır',
	'Create Todo'					=> 'Todo Oluştur',
	'Create Event'					=> 'Etkinlik Oluştur',
	'Update Fields'					=> 'Güncelleme Alanları',
	'Create Entity'                => 'Kayıt Oluştur'             ,
	'SMS Task'						=> 'SMS Görev',
	'Mobile Push Notification'		=> 'Mobil Push Bildirim',
	'LBL_ACTION_TYPE' => 'Eylem Türü (Aktif Sayısı)',
	'LBL_VTEmailTask' => 'E-posta',
    'LBL_VTEntityMethodTask' => 'Özel Fonksiyon',
    'LBL_VTCreateTodoTask' => 'Görev',
    'LBL_VTCreateEventTask' => 'Olay',
    'LBL_VTUpdateFieldsTask' => 'Saha Güncelleme',
    'LBL_VTSMSTask' => 'SMS', 
    'LBL_VTPushNotificationTask' => 'Mobil Bildirim',
    'LBL_VTCreateEntityTask' => 'Kayıt Oluştur',
	'LBL_MAX_SCHEDULED_WORKFLOWS_EXCEEDED' => 'planlanan iş akışlarının maksimum sayısı (%s) aşıldı',

  'LBL_EDITING_WORKFLOW' => 'Düzenleme İş Akışı',
  'LBL_ADD_RECORD' => 'Yeni İş Akışı',
  'ON_SCHEDULE' => 'Zamanlama',
  'LBL_RUN_WORKFLOW' => 'Çalışma Akışı',
  'LBL_AT_TIME' => 'Zaman',
  'LBL_HOURLY' => 'Saatlik',
  'ENTER_FROM_EMAIL_ADDRESS' => 'E-posta adresi girin ',
  'LBL_DAILY' => 'Günlük',
  'LBL_WEEKLY' => 'Haftalık',
  'LBL_ON_THESE_DAYS' => 'Bu günlerde ',
  'LBL_MONTHLY_BY_DATE' => 'Tarihe göre aylık ',
  'LBL_MONTHLY_BY_WEEKDAY' => 'Günlük aylık ',
  'LBL_YEARLY' => 'Yıllık',
  'LBL_SPECIFIC_DATE' => 'Belirli Bir Tarih ',
  'LBL_CHOOSE_DATE' => 'Tarih Seçin ',
  'LBL_SELECT_MONTH_AND_DAY' => 'Seçin Ay ve Tarih',
  'LBL_SELECTED_DATES' => 'Seçili Tarihler',
  'LBL_EXCEEDING_MAXIMUM_LIMIT' => 'Maksimum sınırı aştı',
  'LBL_NEXT_TRIGGER_TIME' => 'Bir dahaki sefere tetik',
  'LBL_MESSAGE' => 'Mesaj',
  'LBL_WORKFLOW_NAME' => 'İş Akışı Adı',
  'LBL_TARGET_MODULE' => 'Hedef Modülü',
  'LBL_WORKFLOW_TRIGGER' => 'İş Akışı Tetikleyicisi',
  'LBL_TRIGGER_WORKFLOW_ON' => 'Tetik İş Akışı ',
  'LBL_RECORD_CREATION' => 'Kayıt Oluşturma',
  'LBL_RECORD_UPDATE' => 'Kayıt Güncelleme',
  'LBL_TIME_INTERVAL' => 'Zaman Aralığı',
  'LBL_RECURRENCE' => 'Nüks',
  'LBL_FIRST_TIME_CONDITION_MET' => 'Sadece ilk defa şartlarına araya geldi ',
  'LBL_EVERY_TIME_CONDITION_MET' => 'Her zaman şartlarına araya geldi ',
  'LBL_WORKFLOW_CONDITION' => 'İş Akışı Durumu',
  'LBL_WORKFLOW_ACTIONS' => 'İş Akışı Eylemleri',
  'LBL_DELAY_ACTION' => 'Gecikme Eylem',
  'LBL_FREQUENCY' => 'Frekans',
  'LBL_SELECT_FIELDS' => 'Alanları Seç',
  'LBL_INCLUDES_CREATION' => 'Oluşturma İçerir ',
  'LBL_ACTION_FOR_WORKFLOW' => 'İş Akışı için eylem ',
  'LBL_WORKFLOW_SEARCH' => 'Ada göre arama ',

);
$jsLanguageStrings = array(
	'JS_STATUS_CHANGED_SUCCESSFULLY' => 'Status changed Successfully' , // TODO: Review
	'JS_TASK_DELETED_SUCCESSFULLY' => 'Eylem Başarıyla silindi'   ,
	'JS_SAME_FIELDS_SELECTED_MORE_THAN_ONCE' => 'Same fields selected more than once', // TODO: Review
	'JS_WORKFLOW_SAVED_SUCCESSFULLY' => 'Workflow saved successfully' , // TODO: Review
    'JS_CHECK_START_AND_END_DATE'=>'Bitiş Tarihi ve Saati Tarih ve Saat Başlat eşit veya daha büyük olmalıdır',

  'JS_TASK_STATUS_CHANGED' => 'Görev durumunu başarılı bir şekilde değiştirildi.',
  'JS_WORKFLOWS_STATUS_CHANGED' => 'İş akışı durumu başarılı bir şekilde değiştirildi.',
  'VTEmailTask' => 'Mail Gönder ',
  'VTEntityMethodTask' => 'Çağırmak Özel İşlev',
  'VTCreateTodoTask' => 'Görev Oluşturma ',
  'VTCreateEventTask' => 'Olay Yaratmak ',
  'VTUpdateFieldsTask' => 'Güncelleme Alanları',
  'VTSMSTask' => 'SMS Görev',
  'VTPushNotificationTask' => 'Mobil İtme Bildirim',
  'VTCreateEntityTask' => 'Kayıt Oluştur ',
  'LBL_EXPRESSION_INVALID' => 'İfade Geçersiz',

);