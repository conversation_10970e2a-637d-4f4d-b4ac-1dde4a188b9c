<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
    'Recycle Bin'                  => 'Корзина'              , 
	'RecycleBin'                   => 'Корзина'              , 
	'LBL_SELECT_MODULE'            => 'Выберите Модуль', 
	'LBL_EMPTY_RECYCLEBIN'         => 'Очистить Корзину', 
	'LBL_RESTORE'                  => 'Востановить'      , // KEY 5.x: LBL_MASS_RESTORE
	'LBL_NO_PERMITTED_MODULES'     => 'Нет доступных модулей', 
	'LBL_RECORDS_LIST'             => 'Корзина Список'            , 
	'LBL_NO_RECORDS_FOUND'         => 'Не найдено записей для Востановления в модуле', // KEY 5.x: LBL_EMPTY_MODULE
);
$jsLanguageStrings = array(
	'JS_MSG_EMPTY_RB_CONFIRMATION' => 'Вы уверены, что хотите навсегда удалить все удаленные записи из базы данных?', 
	'JS_LBL_RESTORE_RECORDS_CONFIRMATION' => 'Вы уверены, что хотите восстановить записи?', 
    'JS_LBL_RESTORE_RECORD_CONFIRMATION' => 'Вы уверены, что вы хотите восстановить запись?',
    'JS_RESTORING_RECORD' => 'Восстановление рекорд',
    'JS_RESTORE_AND_UNTRASH_FILE_IN_DRIVE' => 'Восстановление в Vtiger и Drive',

  'JS_RESTORING_RECORDS' => 'Восстановление записей',

);
