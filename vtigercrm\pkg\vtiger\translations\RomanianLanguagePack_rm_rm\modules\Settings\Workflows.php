<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_NEW'                      => 'New'                         , // TODO: Review
	'LBL_WORKFLOW'                 => 'Workflow'                    , // TODO: Review
	'LBL_CREATING_WORKFLOW'        => 'Creating WorkFlow'           , // TODO: Review
	'LBL_NEXT'                     => 'Next'                        , // TODO: Review
	'LBL_STEP_1'                   => 'Step 1'                      , // TODO: Review
	'LBL_ENTER_BASIC_DETAILS_OF_THE_WORKFLOW' => 'Enter basic details of the Workflow', // TODO: Review
	'LBL_SPECIFY_WHEN_TO_EXECUTE'  => 'Specify when to execute this Workflow', // TODO: Review
	'ON_FIRST_SAVE'                => 'Only on the first save'      , // TODO: Review
	'ONCE'                         => 'Until the first time the condition is true', // TODO: Review
	'ON_EVERY_SAVE'                => 'Every time the record is saved', // TODO: Review
	'ON_MODIFY'                    => 'Every time a record is modified', // TODO: Review
	'MANUAL'                       => 'System'                      , // TODO: Review
	'SCHEDULE_WORKFLOW'            => 'Schedule Workflow'           , // TODO: Review
	'ADD_CONDITIONS'               => 'Add Conditions'              , // TODO: Review
	'ADD_TASKS'                    => 'Adăugați Oferte'           ,
	'LBL_EXPRESSION'               => 'Expression'                  , // TODO: Review
	'LBL_FIELD_NAME'               => 'Field'                       , // TODO: Review
	'LBL_SET_VALUE'                => 'Set Value'                   , // TODO: Review
	'LBL_USE_FIELD'                => 'Use Field'                   , // TODO: Review
	'LBL_USE_FUNCTION'             => 'Use Function'                , // TODO: Review
	'LBL_RAW_TEXT'                 => 'Raw text'                    , // TODO: Review
	'LBL_ENABLE_TO_CREATE_FILTERS' => 'Enable to create Filters'    , // TODO: Review
	'LBL_CREATED_IN_OLD_LOOK_CANNOT_BE_EDITED' => 'This workflow was created in older look. Conditions created in older look cannot be edited. You can choose to recreate the conditions, or use the existing conditions without changing them.', // TODO: Review
	'LBL_USE_EXISTING_CONDITIONS'  => 'Use existing conditions'     , // TODO: Review
	'LBL_RECREATE_CONDITIONS'      => 'Recreate Conditions'         , // TODO: Review
	'LBL_SAVE_AND_CONTINUE'        => 'Save & Continue'             , // TODO: Review
	'LBL_ACTIVE'                   => 'Active'                      , // TODO: Review
	'LBL_TASK_TYPE'                => 'Acțiunea Tipul'             ,
	'LBL_TASK_TITLE'               => 'Acțiune Titlu'              ,
	'LBL_ADD_TASKS_FOR_WORKFLOW'   => 'Adauga Acțiune pentru flux de lucru',
	'LBL_EXECUTE_TASK'             => 'Executare de acțiune'       ,
	'LBL_SELECT_OPTIONS'           => 'Select Options'              , // TODO: Review
	'LBL_ADD_FIELD'                => 'Add Field'                   , // TODO: Review
	'LBL_ADD_TIME'                 => 'Add time'                    , // TODO: Review
	'LBL_TITLE'                    => 'Title'                       , // TODO: Review
	'LBL_PRIORITY'                 => 'Priority'                    , // TODO: Review
	'LBL_ASSIGNED_TO'              => 'Assigned to'                 , // TODO: Review
	'LBL_TIME'                     => 'Time'                        , // TODO: Review
	'LBL_DUE_DATE'                 => 'Due Date'                    , // TODO: Review
	'LBL_THE_SAME_VALUE_IS_USED_FOR_START_DATE' => 'The same value is used for the start date', // TODO: Review
	'LBL_EVENT_NAME'               => 'Event Name'                  , // TODO: Review
	'LBL_TYPE'                     => 'Type'                        , // TODO: Review
	'LBL_METHOD_NAME'              => 'Method Name'                 , // TODO: Review
	'LBL_RECEPIENTS'               => 'Recepients'                  , // TODO: Review
	'LBL_ADD_FIELDS'               => 'Add Fields'                  , // TODO: Review
	'LBL_SMS_TEXT'                 => 'Sms Text'                    , // TODO: Review
	'LBL_SET_FIELD_VALUES'         => 'Set Field Values'            , // TODO: Review
	'LBL_IN_ACTIVE'                => 'In Active'                   , // TODO: Review
	'LBL_SEND_NOTIFICATION'        => 'Send Notification'           , // TODO: Review
	'LBL_START_TIME'               => 'Start Time'                  , // TODO: Review
	'LBL_START_DATE'               => 'Start Date'                  , // TODO: Review
	'LBL_END_TIME'                 => 'End Time'                    , // TODO: Review
	'LBL_END_DATE'                 => 'End Date'                    , // TODO: Review
	'LBL_ENABLE_REPEAT'            => 'Enable Repeat'               , // TODO: Review
	'LBL_NO_METHOD_IS_AVAILABLE_FOR_THIS_MODULE' => 'No method is available for this module', // TODO: Review
	'LBL_FINISH'                   => 'Finish'                      , // TODO: Review
	'LBL_NO_TASKS_ADDED'           => 'Nu Oferte'                   ,
	'LBL_CANNOT_DELETE_DEFAULT_WORKFLOW' => 'You Cannot delete default Workflow', // TODO: Review
	'LBL_MODULES_TO_CREATE_RECORD' => 'Creați un record în'       ,
	'LBL_EXAMPLE_EXPRESSION'       => 'Expression'                  , // TODO: Review
	'LBL_EXAMPLE_RAWTEXT'          => 'Rawtext'                     , // TODO: Review
	'LBL_VTIGER'                   => 'Vtiger'                      , // TODO: Review
	'LBL_EXAMPLE_FIELD_NAME'       => 'Field'                       , // TODO: Review
	'LBL_NOTIFY_OWNER'             => 'notify_owner'                , // TODO: Review
	'LBL_ANNUAL_REVENUE'           => 'annual_revenue'              , // TODO: Review
	'LBL_EXPRESSION_EXAMPLE2'      => 'if mailingcountry == \'India\' then concat(firstname,\' \',lastname) else concat(lastname,\' \',firstname) end', // TODO: Review
	'LBL_FROM' => 'Din',
	'Optional' => 'Facultativ',
	'LBL_ADD_TASK'                 => 'Adăugați acțiune'         ,
    'Portal Pdf Url' =>'Portal pentru clienți link Pdf',
    'LBL_ADD_TEMPLATE' => 'Adauga şablon',
    'LBL_LINEITEM_BLOCK_GROUP' => 'LineItems bloc pentru grup fiscal',
    'LBL_LINEITEM_BLOCK_INDIVIDUAL' => 'LineItems bloc de impozitare individuale',
    'LBL_ADD_PDF' => 'Adauga pdf',
	
	//Translation for module
	'Calendar'					   =>'Pentru a face'						,
	'Send Mail'					   => 'Trimite e-mail',
	'Invoke Custom Function'	   => 'Invocă Funcția personalizată',
	'Create Todo'				   => 'Creați Todo',
	'Create Event'				   => 'Creați eveniment',
	'Update Fields'				   => 'Actualizare Domenii',
	'Create Entity'                => 'Creați înregistrare'       ,
	'SMS Task'					   => 'SMS Sarcina',
	'Mobile Push Notification'	   => 'Push Mobile Notificarea',
	'LBL_ACTION_TYPE' => 'Tipul de acțiune (Contele Active)',
	'LBL_VTEmailTask' => 'Email',
    'LBL_VTEntityMethodTask' => 'Personalizat Funcția',
    'LBL_VTCreateTodoTask' => 'Sarcină',
    'LBL_VTCreateEventTask' => 'Eveniment',
    'LBL_VTUpdateFieldsTask' => 'Câmp Actualizare',
    'LBL_VTSMSTask' => 'SMS', 
    'LBL_VTPushNotificationTask' => 'Notificare mobil',
    'LBL_VTCreateEntityTask' => 'Creați înregistrare',
	'LBL_MAX_SCHEDULED_WORKFLOWS_EXCEEDED' => 'Numărul maxim (%s) a fluxurilor de lucru programate a fost depășită',

  'LBL_EDITING_WORKFLOW' => 'Numărul De În Proces De Editare',
  'LBL_ADD_RECORD' => 'Un Flux De Lucru Nou',
  'ON_SCHEDULE' => 'Programul',
  'LBL_RUN_WORKFLOW' => 'Lansarea Procesului De Lucru',
  'LBL_AT_TIME' => 'Pe Timpul',
  'LBL_HOURLY' => 'Pe oră',
  'ENTER_FROM_EMAIL_ADDRESS' => 'Introduceți adresa de El. mail',
  'LBL_DAILY' => 'De zi cu zi',
  'LBL_WEEKLY' => 'Săptămânal',
  'LBL_ON_THESE_DAYS' => 'În aceste zile',
  'LBL_MONTHLY_BY_DATE' => 'Lunar, de la data',
  'LBL_MONTHLY_BY_WEEKDAY' => 'Lunar în funcție de ziua săptămânii',
  'LBL_YEARLY' => 'Anual',
  'LBL_SPECIFIC_DATE' => 'La O Anumită Dată',
  'LBL_CHOOSE_DATE' => 'Alege Data',
  'LBL_SELECT_MONTH_AND_DAY' => 'Selectați lună și data',
  'LBL_SELECTED_DATES' => 'De Data',
  'LBL_EXCEEDING_MAXIMUM_LIMIT' => 'Limita maximă a depășit',
  'LBL_NEXT_TRIGGER_TIME' => 'Următoarea declanșare timp',
  'LBL_MESSAGE' => 'Mesaj',
  'LBL_WORKFLOW_NAME' => 'Numele Fluxului De Lucru',
  'LBL_TARGET_MODULE' => 'Modulul Țintă',
  'LBL_WORKFLOW_TRIGGER' => 'Fluxul De Lucru De Declanșare',
  'LBL_TRIGGER_WORKFLOW_ON' => 'A Alerga De Afaceri Pe',
  'LBL_RECORD_CREATION' => 'Când Creați O Înregistrare',
  'LBL_RECORD_UPDATE' => 'Actualizare Înregistrare',
  'LBL_TIME_INTERVAL' => 'Intervalul De Timp',
  'LBL_RECURRENCE' => 'Repetarea',
  'LBL_FIRST_TIME_CONDITION_MET' => 'Doar prima dată, condițiile de',
  'LBL_EVERY_TIME_CONDITION_MET' => 'De fiecare dată când condițiile vor fi îndeplinite',
  'LBL_WORKFLOW_CONDITION' => 'Condiții De Flux De Lucru',
  'LBL_WORKFLOW_ACTIONS' => 'Acțiuni De Flux De Lucru',
  'LBL_DELAY_ACTION' => 'Întârziere De Acțiune',
  'LBL_FREQUENCY' => 'Frecvența',
  'LBL_SELECT_FIELDS' => 'Selectați Câmpurile',
  'LBL_INCLUDES_CREATION' => 'Include Crearea De',
  'LBL_ACTION_FOR_WORKFLOW' => 'Acțiuni de flux de lucru',
  'LBL_WORKFLOW_SEARCH' => 'Căutare după numele de familie',

);
$jsLanguageStrings = array(
	'JS_STATUS_CHANGED_SUCCESSFULLY' => 'Status changed Successfully' , // TODO: Review
	'JS_TASK_DELETED_SUCCESSFULLY' => 'Acțiunea eliminat cu succes',
	'JS_SAME_FIELDS_SELECTED_MORE_THAN_ONCE' => 'Same fields selected more than once', // TODO: Review
	'JS_WORKFLOW_SAVED_SUCCESSFULLY' => 'Workflow saved successfully' , // TODO: Review
    'JS_CHECK_START_AND_END_DATE'=>'End Data și ora trebuie să fie mai mare sau egală cu data și ora începerii',

  'JS_TASK_STATUS_CHANGED' => 'Sarcina statutul a fost schimbat cu succes.',
  'JS_WORKFLOWS_STATUS_CHANGED' => '"Starea de flux" a fost schimbat cu succes.',
  'VTEmailTask' => 'Trimite Mail',
  'VTEntityMethodTask' => 'Provoca O Funcție Definită De Utilizator',
  'VTCreateTodoTask' => 'Crearea De Sarcini',
  'VTCreateEventTask' => 'Creați Un Eveniment',
  'VTUpdateFieldsTask' => 'Actualizare Câmpuri',
  'VTSMSTask' => 'SMS-uri de sarcini',
  'VTPushNotificationTask' => 'Mobile De Notificare În Push',
  'VTCreateEntityTask' => 'Creați O Intrare De',
  'LBL_EXPRESSION_INVALID' => 'Expresia Void',

);