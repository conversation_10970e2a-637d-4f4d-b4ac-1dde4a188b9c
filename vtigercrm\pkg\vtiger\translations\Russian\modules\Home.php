<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'ALVT'                         => 'Последние Контрагенты', // KEY 5.x: Top Accounts
	'PLVT'                         => 'Последние Сделки', // KEY 5.x: Top Potentials
	'QLTQ'                         => 'Последние Предложения', // KEY 5.x: Top Quotes
	'CVLVT'                        => 'Ключевые Показатели', // KEY 5.x: Key Metrics
	'HLT'                          => 'Топ Билеты Поддержка'         , 
	'GRT'                          => 'Положение Моей Группы', // KEY 5.x: My Group Allocation
	'OLTSO'                        => 'Последние Заказы на Продажу', // KEY 5.x: Top Sales Orders
	'ILTI'                         => 'Последние Счета', // KEY 5.x: Top Invoices
	'HDB'                          => 'Панель Главной Страницы', // KEY 5.x: Home Page Dashboard
	'OLTPO'                        => 'Последние Заказы на Закупку', // KEY 5.x: Top Purchase Orders
	'LTFAQ'                        => 'Мои Частые ЧаВо', // KEY 5.x: My Recent FAQs
	'UA'                           => 'Ближайшие События', // KEY 5.x: LBL_UPCOMING_EVENTS
	'PA'                           => 'События в процессе', // KEY 5.x: LBL_PENDING_EVENTS
        'Home'                         => 'Приборная панель',

  'LBL_SAVE_ORDER' => 'Сохранить Порядок',
  'LBL_ADD_NEW_DASHBOARD' => 'Добавить новую приборную панель',
	'LBL_MAX_CHARACTERS_ALLOWED_DASHBOARD' => 'Максимум 30 символов разрешено для имени панели.',
);

$jsLanguageStrings = array(
	'JS_TAB_NAME_SHOULD_NOT_BE_EMPTY' => 'Имя Dashboard не может не быть пустым',
	'JS_NO_DATA_AVAILABLE' => 'Данные недоступны',
);