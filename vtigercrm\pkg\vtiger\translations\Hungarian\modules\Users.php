<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD'               => 'Add User'                    , // TODO: Review
	'LBL_MY_PREFERENCES'           => 'Beállításaim'             , 
	'LBL_MORE_INFORMATION'         => 'További adatok'             , 
	'LBL_USERLOGIN_ROLE'           => 'Felhasználó Belépés & Munkakör', 
	'LBL_USER_IMAGE_INFORMATION'   => 'Felhasználó fényképe'    , 
	'LBL_CURRENCY_CONFIGURATION'   => 'Pénznem beállításai'     , 
	'LBL_ADDRESS_INFORMATION'      => 'Felhasználó címe'         , 
	'LBL_USER_ADV_OPTIONS'         => 'Haladó Felhasználói Opciók', 
	'Asterisk Configuration'       => 'Asterisk beállítások (Open Source VOIP telefon)', 
	'LBL_HOME_PAGE_COMPONENTS'     => 'Kezdőlap összetevők'      , 
	'LBL_TAG_CLOUD_DISPLAY'        => 'Tag Cloud Display'           , // TODO: Review
	'Role'                         => 'Munkakör'                   , 
	'Admin'                        => 'Admin'                       , 
	'User Name'                    => 'Felhasználó neve'          , 
	'Default Activity View'		   => 'Alapértelmezett MyCalendar megtekintése',
	'Default Calendar View'        => 'Default Calendar View'            ,
	'Default Lead View'            => 'Alapértelmezett Vevőjelölt nézet', 
	'Title'                        => 'Beosztás'                   , 
	'Office Phone'                 => 'Telefon, irodai'             , 
	'Department'                   => 'Részleg'                    , 
	'Reports To'                   => 'Felettese'                   , 
	'Yahoo id'                     => 'Yahoo azonosító'           , 
	'Home Phone'                   => 'Telefon, otthoni'            , 
	'User Image'                   => 'Fotó feltöltés'           , 
	'Date Format'                  => 'Dátum formátum'            , 
	'Tag Cloud'                    => 'Címke Felhő'               , 
	'Signature'                    => 'Aláírás'                  , 
	'Street Address'               => 'Utca/házszám'              , 
	'Password'                     => 'Jelszó'                     , 
	'Confirm Password'             => 'Jelszó megerősítése'     , 
	'LBL_SHOWN'                    => 'Megjelenített'              , 
	'LBL_HIDDEN'                   => 'Rejtett'                     , 
	'LBL_SHOW'                     => 'Mutat'                       , 
	'LBL_HIDE'                     => 'Rejt'                        , 
	'LBL_HOME_PAGE_COMPO'          => 'Kezdőlap összetevők'      , 
	'LBL_LOGIN_HISTORY'            => 'Belépési adatok'           , 
	'LBL_USERDETAIL_INFO'          => 'Részletes adatok megjelenítése a felhasználóról', 
	'LBL_DELETE_GROUP'             => 'Csoport Törlés'            , 
	'LBL_DELETE_GROUPNAME'         => 'Csoport Törlésre'          , 
	'LBL_TRANSFER_GROUP'           => 'Tulajdonosság átadás: '   , 
	'LBL_DELETE_USER'              => 'Felhasználó törlésre'    , 
	'LBL_TRANSFER_USER'            => 'Tulajdonosság átadás felhasználónak', 
	'LBL_DELETE_PROFILE'           => 'Profil törlés'             , 
	'LBL_TRANSFER_ROLES_TO_PROFILE' => 'Munkakör átadás Profilhoz', 
	'LBL_PROFILE_TO_BE_DELETED'    => 'Profil Törlésre'           , 
	'INTERNAL_MAIL_COMPOSER'       => 'Belső Levelezés Kezelő'   , 
	'Asterisk Extension'           => 'Asterisk bővítmény'       , 
	' Receive Incoming Calls'      => 'Receive Incoming Calls'      , // TODO: Review
	'Reminder Interval'            => 'Emlékeztető intervallum'   , 
	'Webservice Access Key'        => 'Hozzáférési kulcs'        , 
	'Language'                     => 'Nyelv'                       , 
	'Theme'                        => 'Megjelenés'                 , 
	'Time Zone'                    => 'Időzóna'                   , 
	'Decimal Separator'            => 'Tizedesjegy elválasztó'    , 
	'Digit Grouping Pattern'       => 'Számjegy csoportosítási minta', 
	'Digit Grouping Separator'     => 'Ezres elválasztó'          , 
	'Symbol Placement'             => 'Szimbólum elhelyezés'      , 
	'Number Of Currency Decimals'  => 'Tizedesek száma a Pénznemben', 
	'Truncate Trailing Zeros'      => 'Követő nullák elhagyása' , 
	'Default Call Duration'        => 'Default Call Duration (Mins)', // TODO: Review
	'Other Event Duration'         => 'Other Event Duration (Mins)' , // TODO: Review
	'Calendar Hour Format'         => 'Calendar Hour Format'        , // TODO: Review
	'Kwajalein'                    => '(UTC-12:00) International Date Line West', 
	'Pacific/Midway'               => '(UTC-11:00) Coordinated Universal Time-11', 
	'Pacific/Samoa'                => '(UTC-11:00) Samoa'           , 
	'Pacific/Honolulu'             => '(UTC-10:00) Hawaii'          , 
	'America/Anchorage'            => '(UTC-09:00) Alaska'          , 
	'America/Los_Angeles'          => '(UTC-08:00) Pacific Time (US & Canada)', 
	'America/Tijuana'              => '(UTC-08:00) Tijuana, Baja California', 
	'America/Denver'               => '(UTC-07:00) Mountain Time (US & Canada)', 
	'America/Chihuahua'            => '(UTC-07:00) Chihuahua, La Paz, Mazatlan', 
	'America/Mazatlan'             => '(UTC-07:00) Mazatlan'        , 
	'America/Phoenix'              => '(UTC-07:00) Arizona'         , 
	'America/Regina'               => '(UTC-06:00) Saskatchewan'    , 
	'America/Tegucigalpa'          => '(UTC-06:00) Central America' , 
	'America/Chicago'              => '(UTC-06:00) Central Time (US & Canada)', 
	'America/Mexico_City'          => '(UTC-06:00) Mexico City'     , 
	'America/Monterrey'            => '(UTC-06:00) Monterrey'       , 
	'America/New_York'             => '(UTC-05:00) Eastern Time (US & Canada)', 
	'America/Bogota'               => '(UTC-05:00) Bogota, Lima, Quito', 
	'America/Lima'                 => '(UTC-05:00) Lima'            , 
	'America/Rio_Branco'           => '(UTC-05:00) Rio Branco'      , 
	'America/Indiana/Indianapolis' => '(UTC-05:00) Indiana (East)'  , 
	'America/Caracas'              => '(UTC-04:30) Caracas'         , 
	'America/Halifax'              => '(UTC-04:00) Atlantic Time (Canada)', 
	'America/Manaus'               => '(UTC-04:00) Manaus'          , 
	'America/Santiago'             => '(UTC-04:00) Santiago'        , 
	'America/La_Paz'               => '(UTC-04:00) La Paz'          , 
	'America/Cuiaba'               => '(UTC-04:00) Cuiaba'          , 
	'America/Asuncion'             => '(UTC-04:00) Asuncion'        , 
	'America/St_Johns'             => '(UTC-03:30) Newfoundland'    , 
	'America/Argentina/Buenos_Aires' => '(UTC-03:00) Buenos Aires'    , 
	'America/Sao_Paulo'            => '(UTC-03:00) Brasilia'        , 
	'America/Godthab'              => '(UTC-03:00) Greenland'       , 
	'America/Montevideo'           => '(UTC-03:00) Montevideo'      , 
	'Atlantic/South_Georgia'       => '(UTC-02:00) Mid-Atlantic'    , 
	'Atlantic/Azores'              => '(UTC-01:00) Azores'          , 
	'Atlantic/Cape_Verde'          => '(UTC-01:00) Cape Verde Is.'  , 
	'Europe/London'                => '(UTC) London, Edinburgh, Dublin, Lisbon', 
	'UTC'                          => '(UTC) Coordinated Universal Time, Greenwich Mean Time', 
	'Africa/Monrovia'              => '(UTC) Monrovia, Reykjavik'   , 
	'Africa/Casablanca'            => '(UTC) Casablanca'            , 
	'Europe/Belgrade'              => '(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague', 
	'Europe/Sarajevo'              => '(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb', 
	'Europe/Brussels'              => '(UTC+01:00) Brussels, Copenhagen, Madrid, Paris', 
	'Africa/Algiers'               => '(UTC+01:00) West Central Africa', 
	'Europe/Amsterdam'             => '(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna', 
	'Europe/Minsk'                 => '(UTC+02:00) Minsk'           , 
	'Africa/Cairo'                 => '(UTC+02:00) Cairo'           , 
	'Europe/Helsinki'              => '(UTC+02:00) Helsinki, Riga, Sofia, Tallinn, Vilnius', 
	'Europe/Athens'                => '(UTC+02:00) Athens, Bucharest', 
	'Europe/Istanbul'              => '(UTC+02:00) Istanbul'        , 
	'Asia/Jerusalem'               => '(UTC+02:00) Jerusalem'       , 
	'Asia/Amman'                   => '(UTC+02:00) Amman'           , 
	'Asia/Beirut'                  => '(UTC+02:00) Beirut'          , 
	'Africa/Windhoek'              => '(UTC+02:00) Windhoek'        , 
	'Africa/Harare'                => '(UTC+02:00) Harare'          , 
	'Asia/Kuwait'                  => '(UTC+03:00) Kuwait, Riyadh'  , 
	'Asia/Baghdad'                 => '(UTC+03:00) Baghdad'         , 
	'Africa/Nairobi'               => '(UTC+03:00) Nairobi'         , 
	'Asia/Tehran'                  => '(UTC+03:30) Tehran'          , 
	'Asia/Tbilisi'                 => '(UTC+04:00) Tbilisi'         , 
	'Europe/Moscow'                => '(UTC+03:00) Moscow, Volgograd', 
	'Asia/Muscat'                  => '(UTC+04:00) Abu Dhabi, Muscat', 
	'Asia/Baku'                    => '(UTC+04:00) Baku'            , 
	'Asia/Yerevan'                 => '(UTC+04:00) Yerevan'         , 
	'Asia/Karachi'                 => '(UTC+05:00) Islamabad, Karachi', 
	'Asia/Tashkent'                => '(UTC+05:00) Tashkent'        , 
	'Asia/Kolkata'                 => '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi', 
	'Asia/Colombo'                 => '(UTC+05:30) Sri Jayawardenepura', 
	'Asia/Katmandu'                => '(UTC+05:45) Kathmandu'       , 
	'Asia/Dhaka'                   => '(UTC+06:00) Dhaka'           , 
	'Asia/Almaty'                  => '(UTC+06:00) Almaty'          , 
	'Asia/Yekaterinburg'           => '(UTC+06:00) Ekaterinburg'    , 
	'Asia/Rangoon'                 => '(UTC+06:30) Yangon (Rangoon)', 
	'Asia/Novosibirsk'             => '(UTC+07:00) Novosibirsk'     , 
	'Asia/Bangkok'                 => '(UTC+07:00) Bangkok, Jakarta', 
	'Asia/Brunei'                  => '(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi', 
	'Asia/Krasnoyarsk'             => '(UTC+08:00) Krasnoyarsk'     , 
	'Asia/Ulaanbaatar'             => '(UTC+08:00) Ulaan Bataar'    , 
	'Asia/Kuala_Lumpur'            => '(UTC+08:00) Kuala Lumpur, Singapore', 
	'Asia/Taipei'                  => '(UTC+08:00) Taipei'          , 
	'Australia/Perth'              => '(UTC+08:00) Perth'           , 
	'Asia/Irkutsk'                 => '(UTC+09:00) Irkutsk'         , 
	'Asia/Seoul'                   => '(UTC+09:00) Seoul'           , 
	'Asia/Tokyo'                   => '(UTC+09:00) Tokyo'           , 
	'Australia/Darwin'             => '(UTC+09:30) Darwin'          , 
	'Australia/Adelaide'           => '(UTC+09:30) Adelaide'        , 
	'Australia/Canberra'           => '(UTC+10:00) Canberra, Melbourne, Sydney', 
	'Australia/Brisbane'           => '(UTC+10:00) Brisbane'        , 
	'Australia/Hobart'             => '(UTC+10:00) Hobart'          , 
	'Asia/Vladivostok'             => '(UTC+10:00) Vladivostok'     , 
	'Pacific/Guam'                 => '(UTC+10:00) Guam, Port Moresby', 
	'Asia/Yakutsk'                 => '(UTC+10:00) Yakutsk'         , 
	'Etc/GMT-11'				   => '(UTC+11:00) Solomon Is., New Caledonia',
	'Pacific/Fiji'                 => '(UTC+12:00) Fiji'            , 
	'Asia/Kamchatka'               => '(UTC+12:00) Kamchatka'       , 
	'Pacific/Auckland'             => '(UTC+12:00) Auckland'        , 
	'Asia/Magadan'                 => '(UTC+12:00) Magadan'         , 
	'Pacific/Tongatapu'            => '(UTC+13:00) Nukualofa'       , 
	'Summary'                      => 'Summary'                     , // TODO: Review
	'Detail'                       => 'Detail'                      , // TODO: Review
	'LBL_USER_LIST_DETAILS'        => 'Details'                     , // TODO: Review
	'LBL_USER_DELETED_SUCCESSFULLY' => 'Felhasználó sikeresen törölve',
    'LBL_ACTIVE_USERS' => 'Aktív felhasználók',
    'LBL_INACTIVE_USERS' => 'Inaktív felhasználók',
    'LBL_DELETE_USER_PERMANENTLY' => 'Felhasználó törlése Véglegesen',
    'LBL_RESTORE' => 'Visszaad',
    'LBL_USER_RESTORED_SUCCESSFULLY' => 'Felhasználó restaurált sikeresen',
		'LBL_ALMOST_THERE'	=>	'Már majdnem ott vagyunk!',
	'LBL_ABOUT_ME'		=>	'About Me',
	'LBL_WE_PROMISE_TO_KEEP_THIS_PRIVATE'	=>	'(Megígérjük, hogy ez magán)',
	'LBL_ALL_FIELDS_BELOW_ARE_REQUIRED'		=>	'(Minden mező kitöltése kötelező)',
	'LBL_GET_STARTED'	=> 'Az első lépések',
	'LBL_YOUR_CONTACT_NUMBER' => 'Az Telefonszám',
	'LBL_WHERE_ARE_YOU_FROM' =>	'Honnan jöttél?',
	'LBL_SELECT_COUNTRY'	=> 'Ország kiválasztása',
	'LBL_COMPANY_SIZE'		=> 'Vállalati méret',
	'LBL_JOB_TITLE'			=> 'Job Title',
	'LBL_DEPARTMENT'		=> 'Osztály',
	'LBL_BASE_CURRENCY'		=> 'Az alap pénzneme',
	'LBL_CHOOSE_BASE_CURRENCY'	=> 'Választás alap pénzneme',
	'LBL_OPERATING_CURRENCY'	=> 'Bázisdeviza nem módosítható később. Válassza ki az operációs valuta',
	'LBL_LANGUAGE' => 'Nyelv',
	'LBL_CHOOSE_LANGUAGE'	=> 'Válassza ki a nyelvet',
	'LBL_CHOOSE_TIMEZONE'	=> 'Válasszon időzónát',
	'LBL_DATE_FORMAT'		=> 'Dátum formátum',
	'LBL_CHOOSE_DATE_FORMAT'=> 'Válassza a Date Format',
	'LBL_PHONE'	=> 'Telefon',
    'Space' => 'Hely',
	//picklist values for Default Calendar View field in MyPreference Page
	'ListView' => 'Listanézet',
	'MyCalendar' => 'Saját naptár',
	'SharedCalendar' => 'Megosztott naptár',
    
    'LBL_CHANGE_OWNER' => 'Tulajdonos módosítása',
    'LBL_TRANSFER_OWNERSHIP' => 'Átruházza a tulajdonjogot',
    'LBL_TRANSFER_OWNERSHIP_TO_USER' => 'Tulajdonjogát másra átruházni a felhasználói',
    'LBL_OWNERSHIP_TRANSFERRED_SUCCESSFULLY' => 'CRM Tulajdonos megváltozott sikeresen',
    'LBL_OWNERSHIP_TRANSFERRED_FAILED' => 'Nem sikerült a változó CRM tulajdonos',
    'Account Owner' => 'Számla tulajdonosa',
    'Starting Day of the week' => 'Kezdve A hét napja',
    'Day starts at' => 'Nap kezdődik',
    'Default Event Status' => 'Default Event Status',
    'Default Activity Type' => 'Alapértelmezett tevékenység típusa',
    'Default Record View' => 'Alapértelmezett Record megtekintése',
    'Left Panel Hide' => 'Bal oldali panel elrejtése',
    'Row Height' => 'Sormagasság',
	'LBL_RESTORE_USER_FAILED' => 'Nem sikerült helyreállítani a felhasználó. Már van egy CRM felhasználói a felhasználó név.',
    
    'LBL_DUPLICATE_USER_EXISTS' => 'Felhasználó már létezik',


	'LBL_CHANGE_USERNAME'          => 'Változás név'             ,
	'LBL_USERNAME_CHANGED'         => 'Felhasználónév módosítása sikeres',
	'ERROR_CHANGE_USERNAME'        => 'Hiba a változás felhasználónév. Kérjük, próbálkozzon később',

  'LBL_REMOVE_USER' => 'Törlés',
  'LBL_MORE_OPTIONS' => 'A További Lehetőségek',
  'LBL_RESTORE_USER' => 'Visszaállítás Felhasználó',
  'LBL_OLD_PASSWORD' => 'Régi Jelszó',
  'LBL_CHANGE_PASSWORD' => 'Jelszó Módosítása',
  'LBL_NEW_PASSWORD' => 'Új Jelszó',
  'LBL_CONFIRM_PASSWORD' => 'Jelszó Megerősítése',
	'LBL_CHANGE_ACCESS_KEY' => 'Hozzáférés módosítása Key',
	'LBL_ACCESS_KEY_UPDATED_SUCCESSFULLY' => 'Hozzáférési kulcs sikeresen frissítve',
	'LBL_FAILED_TO_UPDATE_ACCESS_KEY' => 'Nem sikerült frissíteni a hozzáférési kulcs',
  'LBL_LOGIN_AS' => 'Bejelentkezési mint ',
  'LBL_CREATE_USER' => 'Felhasználó Létrehozása',
  'LBL_DELETE_USER_PERMANENTLY_INFO' => 'Felhasználó törlése véglegesen át minden adatot, beleértve a megjegyzéseket, s a történelem új felhasználó.',
  'LBL_TO_CRM' => 'A bejelentkezés Vtiger CRM',
  'LBL_INVALID_USER_OR_PASSWORD' => 'Érvénytelen felhasználónevet vagy jelszót.',
  'LBL_INVALID_USER_OR_EMAIL' => 'Érvénytelen Felhasználónév vagy Email cím.',
  'LBL_EMAIL_SEND' => 'Az általunk küldött e-mail-hoz orrgazdaság jelszó.',
  'ForgotPassword' => 'Elfelejtettem A Jelszavam',
  'LBL_CONNECT_WITH_US' => 'Csatlakozzon HOZZÁNK',
  'LBL_GET_MORE' => 'Tudjon többet a Vtiger',
  'LBL_TRANSFER_RECORDS_TO_USER' => 'Transzfer rekordok felhasználó',
  'LBL_USER_TO_BE_DELETED' => 'Felhasználó Törölni kell az',
  'LBL_USERS_SETTINGS' => 'A FELHASZNÁLÓK BEÁLLÍTÁSAI',
  'LBL_TEMPLATES' => 'Sablonok',

);
$jsLanguageStrings = array(
		
	//Curency seperator validation messages
	'JS_ENTER_OLD_PASSWORD'=>'Please enter your old password.',
	'JS_ENTER_NEW_PASSWORD'=>'Please enter your new password.',
	'JS_ENTER_CONFIRMATION_PASSWORD'=>'Please enter your password confirmation.',
	'JS_REENTER_PASSWORDS'=>'Please re-enter passwords.  The \"new password\" and \"confirm password\" values do not match.',
	'JS_INVALID_PASSWORD'=>'You must specify a valid username and password.',
	'JS_PASSWORD_CHANGE_FAILED_1'=>'User password change failed for ',
	'JS_PASSWORD_CHANGE_FAILED_2'=>' failed.  The new password must be set.',
	'JS_PASSWORD_INCORRECT_OLD'=>'Incorrect old password specified. Re-enter password information.',
	'JS_ENTERED_CURRENT_USERNAME_MSG' => 'Ön belépett a jelenlegi felhasználónévvel. Kérjük, adja meg az új felhasználónevet.',
	'JS_NEW_ACCESS_KEY_REQUESTED' => 'Új hozzáférési kulcsot kért',
	'JS_CHANGE_ACCESS_KEY_CONFIRMATION' => 'Azt kérte, egy új hozzáférési kulcsot. &lt;br&gt;&lt;br&gt;Az új hozzáférési kulcsot rendelkezés, ki kell cserélni a régi hozzáférési kulcsot az újat minden telepített bővítmények. &lt;br&gt;&lt;br&gt;Do szeretné folytatni?',
);