<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 * ********************************************************************************
 *  Language     : Español es_es
 *  Version      : 6.0.0
 *  Created Date : 2013-05-10
 *  Author       : JPL TSolucio, S. L. Joe <PERSON>rdes
 *  Last change  : 2013-05-10
 *  Author       : JPL TSolucio, S. L<PERSON>
 *************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD' => 'Añadir Moneda',
	'LBL_EDIT_CURRENCY' => 'Editar Moneda',
	'LBL_ADD_NEW_CURRENCY' => 'Nueva Moneda',
	'LBL_CURRENCY_NAME' => 'Nombre de Moneda',
	'LBL_CURRENCY_CODE' => 'Código de Moneda',
	'LBL_CURRENCY_SYMBOL' => 'Símbolo',
	'LBL_CONVERSION_RATE' => 'Ratio de Conversión',
	'LBL_ENTER_CONVERSION_RATE' => 'Introduce Ratio de Conversión',
	'LBL_CURRENCY_STATUS_DESC' => 'Habilitar casilla para activar la moneda',
	'LBL_TRANSFER_CURRENCY' => 'Moneda de Cambio',
	'LBL_CURRENT_CURRENCY' => 'Moneda Actual',
	
	//currency names
	'Albania, Leke' 		=> 'Albania, Leke',
	'Argentina, Pesos' 		=> 'Argentina, Pesos',
	'Aruba, Guilders' 		=> 'Aruba, Guilders',
	'Australia, Dollars' 	=> 'Australia, Dollars',
	'Azerbaijan, New Manats'=> 'Azerbaijan, New Manats',
	'Bahamas, Dollars' 		=> 'Bahamas, Dollars',
	'Bahrain, Dinar' 		=> 'Bahrain, Dinar',
	'Barbados, Dollars' 	=> 'Barbados, Dollars',
	'Belarus, Rubles' 		=> 'Belarus, Rubles',
	'Belize, Dollars' 		=> 'Belize, Dollars',
	'Bermuda, Dollars' 		=> 'Bermuda, Dollars',
	'Bolivia, Bolivianos' 	=> 'Bolivia, Bolivianos',
	'Convertible Marka'		=> 'Convertible Marka',
	'Botswana, Pulas' 		=> 'Botswana, Pulas',
	'Bulgaria, Leva' 		=> 'Bulgaria, Leva',
	'Brazil, Reais' 		=> 'Brazil, Reais',
	'Great Britain Pounds' 	=> 'Great Britain Pounds',
	'Brunei Darussalam, Dollars' => 'Brunei Darussalam, Dollars',
	'Canada, Dollars' 		=> 'Canada, Dollars',
	'Cayman Islands, Dollars' => 'Cayman Islands, Dollars',
	'Chile, Pesos' 			=> 'Chile, Pesos',
	'Colombia, Pesos' 		=> 'Colombia, Pesos',
	'Costa Rica, Colón' 	=> 'Costa Rica, Colón',
	'Croatia, Kuna' 		=> 'Croatia, Kuna',
	'Cuba, Pesos' 			=> 'Cuba, Pesos',
	'Cyprus, Pounds'		=> 'Cyprus, Pounds',
	'Czech Republic, Koruny' => 'Czech Republic, Koruny',
	'Denmark, Kroner' 		=> 'Denmark, Kroner',
	'Dominican Republic, Pesos' => 'Dominican Republic, Pesos',
	'East Caribbean, Dollars' => 'East Caribbean, Dollars',
	'Egypt, Pounds' 		=> 'Egypt, Pounds',
	'El Salvador, Colón' 	=> 'El Salvador, Colón',
	'England, Pounds' 		=> 'England, Pounds',
	'Estonia, Krooni' 		=> 'Estonia, Krooni',
	'Euro' 					=> 'Euro',
	'Falkland Islands, Pounds' => 'Falkland Islands, Pounds',
	'Fiji, Dollars' => 'Fiji, Dollars',
	'Ghana, Cedis' 	=> 'Ghana, Cedis',
	'Gibraltar, Pounds' 	=> 'Gibraltar, Pounds',
	'Guatemala, Quetzales' 	=> 'Guatemala, Quetzales',
	'Guernsey, Pounds' 		=> 'Guernsey, Pounds',
	'Guyana, Dollars' 		=> 'Guyana, Dollars',
	'Honduras, Lempiras' 	=> 'Honduras, Lempiras',
	'LvHong Kong, Dollars ' => 'LvHong Kong, Dollars ',
	'Hungary, Forint' 		=> 'Hungary, Forint',
	'Iceland, Krona' 		=> 'Iceland, Krona',
	'India, Rupees' 		=> 'India, Rupees',
	'Indonesia, Rupiahs' 	=> 'Indonesia, Rupiahs',
	'Iran, Rials' 			=> 'Iran, Rials',
	'Isle of Man, Pounds' 	=> 'Isle of Man, Pounds',
	'Israel, New Shekels' 	=> 'Israel, New Shekels',
	'Jamaica, Dollars' 		=> 'Jamaica, Dollars',
	'Japan, Yen' 			=> 'Japan, Yen',
	'Jersey, Pounds' 		=> 'Jersey, Pounds',
	'Kazakhstan, Tenge' 	=> 'Kazakhstan, Tenge',
	'Korea (North), Won' 	=> 'Korea (North), Won',
	'Korea (South), Won' 	=> 'Korea (South), Won',
	'Kyrgyzstan, Soms' 		=> 'Kyrgyzstan, Soms',
	'Laos, Kips' 			=> 'Laos, Kips',
	'Latvia, Lati' 			=> 'Latvia, Lati',
	'Lebanon, Pounds' 		=> 'Lebanon, Pounds',
	'Liberia, Dollars' 		=> 'Liberia, Dollars',
	'Switzerland Francs' 	=> 'Switzerland Francs',
	'Lithuania, Litai' 		=> 'Lithuania, Litai',
	'Macedonia, Denars' 	=> 'Macedonia, Denars',
	'Malaysia, Ringgits' 	=> 'Malaysia, Ringgits',
	'Malta, Liri'			=> 'Malta, Liri',
	'Mauritius, Rupees' 	=> 'Mauritius, Rupees',
	'Mexico, Pesos' 		=> 'Mexico, Pesos',
	'Mongolia, Tugriks' 	=> 'Mongolia, Tugriks',
	'Mozambique, Meticais' 	=> 'Mozambique, Meticais',
	'Namibia, Dollars' 		=> 'Namibia, Dollars',
	'Nepal, Rupees' 		=> 'Nepal, Rupees',
	'Netherlands Antilles, Guilders' => 'Netherlands Antilles, Guilders',
	'New Zealand, Dollars' 	=> 'New Zealand, Dollars',
	'Nicaragua, Cordobas' 	=> 'Nicaragua, Cordobas',
	'Nigeria, Nairas' 		=> 'Nigeria, Nairas',
	'North Korea, Won' 		=> 'North Korea, Won',
	'Norway, Krone' 		=> 'Norway, Krone',
	'Oman, Rials' 			=> 'Oman, Rials',
	'Pakistan, Rupees' 		=> 'Pakistan, Rupees',
	'Panama, Balboa' 		=> 'Panama, Balboa',
	'Paraguay, Guarani' 	=> 'Paraguay, Guarani',
	'Peru, Nuevos Soles' 	=> 'Peru, Nuevos Soles',
	'Philippines, Pesos' 	=> 'Philippines, Pesos',
	'Poland, Zlotych' 		=> 'Poland, Zlotych',
	'Qatar, Rials'			=> 'Qatar, Rials',
	'Romania, New Lei' 		=> 'Romania, New Lei',
	'Russia, Rubles'		=> 'Russia, Rubles',
	'Saint Helena, Pounds' => 'Saint Helena, Pounds',
	'Saudi Arabia, Riyals' => 'Saudi Arabia, Riyals',
	'Serbia, Dinars' 		=> 'Serbia, Dinars',
	'Seychelles, Rupees' 	=> 'Seychelles, Rupees',
	'Singapore, Dollars' 	=> 'Singapore, Dollars',
	'Solomon Islands, Dollars' => 'Solomon Islands, Dollars',
	'Somalia, Shillings' => 'Somalia, Shillings',
	'South Africa, Rand' => 'South Africa, Rand',
	'South Korea, Won' 	=> 'South Korea, Won',
	'Sri Lanka, Rupees' => 'Sri Lanka, Rupees',
	'Sweden, Kronor' 	=> 'Sweden, Kronor',
	'Switzerland, Francs' => 'Switzerland, Francs',
	'Suriname, Dollars' 	=> 'Suriname, Dollars',
	'Syria, Pounds' 		=> 'Syria, Pounds',
	'Taiwan, New Dollars' 	=> 'Taiwan, New Dollars',
	'Thailand, Baht' 		=> 'Thailand, Baht',
	'Trinidad and Tobago, Dollars' => 'Trinidad and Tobago, Dollars',
	'Turkey, New Lira' 		=> 'Turkey, New Lira',
	'Turkey, Liras' 		=> 'Turkey, Liras',
	'Tuvalu, Dollars' 		=> 'Tuvalu, Dollars',
	'Ukraine, Hryvnia' 		=> 'Ukraine, Hryvnia',
	'United Kingdom, Pounds' => 'United Kingdom, Pounds',
	'USA, Dollars' 			=> 'USA, Dollars',
	'Uruguay, Pesos' 		=> 'Uruguay, Pesos',
	'Uzbekistan, Sums' 		=> 'Uzbekistan, Sums',
	'Venezuela, Bolivares Fuertes' => 'Venezuela, Bolivares Fuertes',
	'Vietnam, Dong' 		=> 'Vietnam, Dong',
	'Zimbabwe Dollars' 		=> 'Zimbabwe Dollars',
	'China, Yuan Renminbi' 	=> 'China, Yuan Renminbi',
	'Afghanistan, Afghanis' => 'Afghanistan, Afghanis',
	'Cambodia, Riels' 		=> 'Cambodia, Riels',
	'China, Yuan Renminbi'	=> 'China, Yuan Renminbi',
	'Jordan, Dinar'			=> 'Jordan, Dinar',
	'Kenya, Shilling'		=> 'Kenya, Shilling',
	'MADAGASCAR, Malagasy Ariary' => 'MADAGASCAR, Malagasy Ariary',
	'United Arab Emirates, Dirham' => 'United Arab Emirates, Dirham',
	'United Republic of Tanzania, Shilling' => 'United Republic OF Tanzania, Shilling',
	'Yemen, Rials'			=> 'Yemen, Rials',
	'Zambia, Kwacha'		=> 'Zambia, Kwacha',
	'Malawi, kwacha'		=> 'Malawi, kwacha',
	'Tunisian, Dinar'		=> 'Tunisian, Dinar',
	'Moroccan, Dirham'		=> 'Moroccan, Dirham',
);

$jsLanguageStrings = array(
	'JS_CURRENCY_DETAILS_SAVED' => 'Se han guardado los detalles de la moneda',
	'JS_CURRENCY_DELETED_SUEESSFULLY' => 'Se ha eliminado la moneda correctamente',
);
