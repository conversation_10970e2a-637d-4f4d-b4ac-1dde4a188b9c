<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
    'Vendors'                      => 'Fournisseurs'                , 
    'SINGLE_Vendors'               => 'Fournisseur'                 , 
    'LBL_ADD_RECORD'               => 'Ajouter un Fournisseur'      , 
    'LBL_RECORDS_LIST'             => 'Liste des Fournisseurs'       , 
    'LBL_VENDOR_INFORMATION'       => 'Détail du Fournisseur'       , 
    'LBL_VENDOR_ADDRESS_INFORMATION' => 'Adresse'                   , 
    'Vendor Name'                  => 'Nom du Fournisseur'          , 
    'Vendor No'                    => 'Fournisseur N°'              , 
    'Website'                      => 'Site web'                    , 
    'GL Account'                   => 'Code comptable'              , 
    '300-Sales-Software'           => '300-Sales-Software'          , 
    '301-Sales-Hardware'           => '301-Sales-Hardware'          , 
    '302-Rental-Income'            => '302-Rental-Income'           , 
    '303-Interest-Income'          => '303-Interest-Income'         , 
    '304-Sales-Software-Support'   => '304-Sales-Software-Support'  , 
    '305-Sales Other'              => '305-Sales Other'             , 
    '306-Internet Sales'           => '306-Internet Sales'          , 
    '307-Service-Hardware Labor'   => '307-Service-Hardware Labor'  , 
    '308-Sales-Books'              => '308-Sales-Books'             , 

    'Phone'                        => 'Téléphone Principal',
    'Email'                        => 'E-Mail Principale',

);
$jsLanguageStrings = array(
    'LBL_RELATED_RECORD_DELETE_CONFIRMATION' => 'Vous êtes sur de vouloir le supprimer ?', 
    'LBL_DELETE_CONFIRMATION'      => 'En supprimant ce founisseur, cela va supprimé les Commandes Fournisseur qui lui sont associées. Etes vous sur de vouloir le supprimer ?', 
    'LBL_MASS_DELETE_CONFIRMATION' => 'En supprimant ces founisseurs, cela va supprimé les Commandes Fournisseur qui leurs sont associées. Etes vous sur de vouloir les supprimer ?', 
);