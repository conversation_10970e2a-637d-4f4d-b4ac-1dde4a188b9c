<?php
/*********************************************************************************
 ** The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *
 ********************************************************************************/

require_once('include/utils/utils.php');
require_once('include/logging.php');
require_once('include/database/PearDatabase.php');

require_once('modules/Settings/MailConverter/handlers/MailScannerInfo.php');
require_once('modules/Settings/MailConverter/handlers/MailBox.php');
require_once('modules/Settings/MailConverter/handlers/MailScanner.php');

//Added as sometimes the php.ini file used for command line php and
//for Apache php is different.
if(!function_exists('imap_open')) {
	echo $installationStrings['LBL_NO'].' '.$installationStrings['LBL_IMAP_SUPPORT'];
} elseif(!function_exists('openssl_encrypt')) {
	echo $installationStrings['LBL_NO'].' '.$installationStrings['LBL_OPENSSL_SUPPORT'];
}

/**
 * Helper function for triggering the scan.
 */
function service_MailScanner_performScanNow($scannerinfo, $debug) {
	/** If the scanner is not enabled, stop. */
	if($scannerinfo->isvalid) { 
		echo "Scanning " . $scannerinfo->server . " in progress\n";

		/** Start the scanning. */
		$scanner = new Vtiger_MailScanner($scannerinfo);
		$scanner->debug = $debug;
		$status = $scanner->performScanNow();
		
                if($status && is_bool($status))
                    echo "\nScanning " . $scannerinfo->server . " completed\n";
                else
                    echo "\nScanning Failed. Error ".$status."\n";
		
	} else {
		echo "Failed! [{$scannerinfo->scannername}] is not enabled for scanning!";
	}
}

/** 
 * Execution of this is based on number of emails and connection to mailserver.
 * So setting infinite timeout.
 */
@set_time_limit(0);

/** Turn-off this if not required. */
$debug = true;

/** Pick up the mail scanner for scanning. */
if(isset($_REQUEST['scannername'])) {
	
	// Target scannername specified?	
	$scannername = vtlib_purify($_REQUEST['scannername']);
	$scannerinfo = new Vtiger_MailScannerInfo($scannername);
	
	service_MailScanner_performScanNow($scannerinfo, $debug);
	
} else {
	
	// Scan all the configured mailscanners?
	
	$scannerinfos = Vtiger_MailScannerInfo::listAll();
	if(empty($scannerinfos)) {
		
		echo "No mailbox configured for scanning!";
		
	} else {
		foreach($scannerinfos as $scannerinfo) {
			service_MailScanner_performScanNow($scannerinfo, $debug);
		}
	}	
}

?>
