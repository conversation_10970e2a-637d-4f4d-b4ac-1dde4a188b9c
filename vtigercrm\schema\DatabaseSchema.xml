<?xml version="1.0"?>
<schema version="0.2">

	<table name="vtiger_users">
		<field name="id" type="I" >
			<key />
			<autoincrement />
		</field>
		<field name="user_name" type="C" size="255" />
		<field name="user_password" type="C" size="200" />
		<field name="cal_color" type="C" size="25">
			<default value="#E6FAD8" />
		</field>
		<field name="first_name" type="C" size="30" />
		<field name="last_name" type="C" size="30" />
		<field name="reports_to_id" type="C" size="36" />
		<field name="is_admin" type="C" size="3">
			<default value="0" />
		</field>
		<field name="currency_id" type="I" size="19">
			<default value="1" />
			<notnull />
		</field>
		<field name="description" type="X" />
		<field name="date_entered" type="T">
			<notnull />
			<deftimestamp />
		</field>
		<field name="date_modified" type="T">
			<!-- Troublesome with STRICT_TRANS_TABLE mode depends on MySQL 5.5 or 5.6+ -->
			<!--
			<notnull />
			<deftimestamp />
			-->
		</field>
		<field name="modified_user_id" type="C" size="36" />
		<field name="title" type="C" size="50" />
		<field name="department" type="C" size="50" />
		<field name="phone_home" type="C" size="50" />
		<field name="phone_mobile" type="C" size="50" />
		<field name="phone_work" type="C" size="50" />
		<field name="phone_other" type="C" size="50" />
		<field name="phone_fax" type="C" size="50" />
		<field name="email1" type="C" size="100" />
		<field name="email2" type="C" size="100" />
		<field name="secondaryemail" type="C" size="100" />
		<field name="status" type="C" size="25" />
		<field name="signature" type="C" size="1000" />
		<field name="address_street" type="C" size="150" />
		<field name="address_city" type="C" size="100" />
		<field name="address_state" type="C" size="100" />
		<field name="address_country" type="C" size="25" />
		<field name="address_postalcode" type="C" size="9" />
		<field name="user_preferences" type="X" />
		<field name="tz" type="C" size="30" />
		<field name="holidays" type="C" size="60" />
		<field name="namedays" type="C" size="60" />
		<field name="workdays" type="C" size="30" />
		<field name="weekstart" type="I" size="11" />
		<field name="date_format" type="C" size="200" />
		<field name="hour_format" type="C" size="30">
			<default value="am/pm" />
		</field>
		<field name="start_hour" type="C" size="30">
			<default value="10:00" />
		</field>
		<field name="end_hour" type="C" size="30">
			<default value="23:00" />
		</field>
		<field name="is_owner" type="C" size="100">
			<default value="0" />
		</field>
		<field name="activity_view" type="C" size="200">
			<default value="Today" />
		</field>
		<field name="lead_view" type="C" size="200">
			<default value="Today" />
		</field>
		<field name="imagename" type="C" size="250" />
		<field name="deleted" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="confirm_password" type="C" size="300" />

		<field name="internal_mailer" type="C" size="3">
			<notnull />
			<default value="1" />
		</field>
		<field name="reminder_interval" type="C" size="100" />
		<field name="reminder_next_time" type="C" size="100" />
		<field name="crypt_type" type="C" size="20">
			<notnull />
			<default value="MD5" />
		</field>
		<field name="accesskey" type="C" size="36" />
		<field name="theme" type="C" size="100" />
		<field name="language" type="C" size="36" />
		<field name="time_zone" type="C" size="200" />
		<field name="currency_grouping_pattern" type="C" size="100" />
		<field name="currency_decimal_separator" type="C" size="2" />
		<field name="currency_grouping_separator" type="C" size="2" />
		<field name="currency_symbol_placement" type="C" size="20" />
                <field name="userlabel" type="C" size="255" />
		<field name="defaultlandingpage" type="C" size="200" />
		<index name="user_user_name_idx">
			<col>user_name</col>
		</index>
		<index name="user_user_password_idx">
			<col>user_password</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_crmentity">
		<field name="crmid" type="I" size="19">
			<key />
		</field>
		<field name="smcreatorid" type="I" size="19">
			<notnull/>
			<default value="0" />
		</field>
		<field name="smownerid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="modifiedby" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="setype" type="C" size="30">
			<notnull />
		</field>
		<field name="description" type="X" />
		<field name="createdtime" type="T">
			<notnull />
		</field>
		<field name="modifiedtime" type="T">
			<notnull />
		</field>
		<field name="viewedtime" type="T" />
		<field name="status" type="C" size="50" />
		<field name="version" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<default value="1" />
		</field>

		<field name="deleted" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="smgroupid" type="I" size="19" />

		<index name="crmentity_smcreatorid_idx">
			<col>smcreatorid</col>
		</index>
		<index name="crmentity_modifiedby_idx">
			<col>modifiedby</col>
		</index>
		<index name="crmentity_deleted_idx">
			<col>deleted</col>
		</index>
		<index name="crm_ownerid_del_setype_idx">
			<col>smownerid</col>
			<col>deleted</col>
			<col>setype</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_sharedcalendar">
		<field name="userid" type="I" size="19">
			<key />
		</field>
		<field name="sharedid" type="I" size="19">
			<key />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tab">
		<field name="tabid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="name" type="C" size="25">
			<notnull />
		</field>
		<field name="presence" type="I" size="19">
			<notnull />
			<default value="1" />
		</field>
		<field name="tabsequence" type="I" size="10" />
		<field name="tablabel" type="C" size="25">
			<notnull />
		</field>
		<field name="modifiedby" type="I" size="19" />
		<field name="modifiedtime" type="I" size="19" />
		<field name="customized" type="I" size="19" />
		<field name="ownedby" type="I" size="19" />
		<field name="isentitytype" type="I">
			<notnull />
			<default value="1" />
		</field>
		<field name="trial" type="I"  size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="version" type="C" size="10" />
		<field name="parent" type="C" size="30"/>
		<field name="source" type="C" size="255">
			<default value="custom" />
		</field>

		<index name="tab_name_idx">
			<col>name</col>
			<unique />
		</index>
		<index name="tab_modifiedby_idx">
			<col>modifiedby</col>
		</index>
		<index name="tab_tabid_idx">
			<col>tabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_blocks">
		<field name="blockid" type="I" size="19">
			<key />
			<notnull />
		</field>
		<field name="tabid" type="I" size="19">
			<notnull />
		</field>
		<field name="blocklabel" type="C" size="100">
			<notnull />
		</field>
		<field name="sequence" type="I" size="10" />
		<field name="show_title" type="I" size="2" />
		<field name="visible" type="I" size="2">
			<notnull />
			<default value="0" />
		</field>
		<field name="create_view" type="I" size="2">
			<notnull />
			<default value="0" />
		</field>
		<field name="edit_view" type="I" size="2">
			<notnull />
			<default value="0" />
		</field>
		<field name="detail_view" type="I" size="2">
			<notnull />
			<default value="0" />
		</field>
		<field name="display_status" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="iscustom" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<index name="block_tabid_idx">
			<col>tabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_blocks" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_blocks FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_field">
		<field name="tabid" type="I" size="19">
			<notnull />
		</field>
		<field name="fieldid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="columnname" type="C" size="30">
			<notnull />
		</field>
		<field name="tablename" type="C" size="50">
			<notnull />
		</field>
		<field name="generatedtype" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="uitype" type="C" size="30">
			<notnull />
		</field>
		<field name="fieldname" type="C" size="50">
			<notnull />
		</field>
		<field name="fieldlabel" type="C" size="50">
			<notnull />
		</field>
		<field name="readonly" type="I" size="1">
			<notnull />
		</field>
		<field name="presence" type="I" size="19">
			<notnull />
			<default value="1" />
		</field>
		<field name="defaultvalue" type="X">
		</field>
		<field name="maximumlength" type="I" size="19" />
		<field name="sequence" type="I" size="19" />
		<field name="block" type="I" size="19" />
		<field name="displaytype" type="I" size="19" />
		<field name="typeofdata" type="C" size="100" />
		<field name="quickcreate" type="I" size="10">
			<notnull />
			<default value="1" />
		</field>
		<field name="quickcreatesequence" type="I" size="19" />
		<field name="info_type" type="C" size="20" />
		<field name="masseditable" type="I" size="10">
			<notnull />
			<default value="1" />
		</field>

		<index name="field_tabid_idx">
			<col>tabid</col>
		</index>
		<index name="field_fieldname_idx">
			<col>fieldname</col>
		</index>
		<index name="field_block_idx">
			<col>block</col>
		</index>
		<index name="field_displaytype_idx">
			<col>displaytype</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_field" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_field FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_account">
		<field name="accountid" type="I" size="19">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="account_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="accountname" type="C" size="100">
			<unique />
			<notnull />
		</field>
		<field name="parentid" type="I" size="19">
			<default value="0" />
		</field>
		<field name="account_type" type="C" size="200" />
		<field name="industry" type="C" size="200" />
		<field name="annualrevenue" type="I" size="19">
			<default value="0" />
		</field>
		<field name="rating" type="C" size="200" />
		<field name="ownership" type="C" size="50" />
		<field name="siccode" type="C" size="50" />
		<field name="tickersymbol" type="C" size="30" />
		<field name="phone" type="C" size="30" />
		<field name="otherphone" type="C" size="30" />
		<field name="email1" type="C" size="100" />
		<field name="email2" type="C" size="100" />
		<field name="website" type="C" size="100" />
		<field name="fax" type="C" size="30" />
		<field name="employees" type="I" size="10">
			<default value="0" />
		</field>
		<field name="emailoptout" type="C" size="3">
			<default value="0" />
		</field>
		<field name="notify_owner" type="C" size="3">
			<default value="0" />
		</field>

		<index name="account_account_type_idx">
			<col>account_type</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_account" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_account FOREIGN KEY (accountid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_accountbillads">
		<field name="accountaddressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="bill_city" type="C" size="30" />
		<field name="bill_code" type="C" size="30" />
		<field name="bill_country" type="C" size="30" />
		<field name="bill_state" type="C" size="30" />
		<field name="bill_street" type="C" size="250" />
		<field name="bill_pobox" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_accountbillads" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_accountbillads FOREIGN KEY (accountaddressid) REFERENCES vtiger_account(accountid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_accountshipads">
		<field name="accountaddressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="ship_city" type="C" size="30" />
		<field name="ship_code" type="C" size="30" />
		<field name="ship_country" type="C" size="30" />
		<field name="ship_state" type="C" size="30" />
		<field name="ship_pobox" type="C" size="30" />
		<field name="ship_street" type="C" size="250" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_accountshipads" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_accountshipads FOREIGN KEY (accountaddressid) REFERENCES vtiger_account(accountid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_accountscf">
		<field name="accountid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_accountscf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_accountscf FOREIGN KEY (accountid) REFERENCES vtiger_account(accountid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_contactdetails">
		<field name="contactid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="contact_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="accountid" type="I" size="19" />
		<field name="salutation" type="C" size="200">
		</field>
		<field name="firstname" type="C" size="40" />
		<field name="lastname" type="C" size="80">
			<notnull />
		</field>
		<field name="email" type="C" size="100" />
		<field name="phone" type="C" size="50" />
		<field name="mobile" type="C" size="50" />
		<field name="title" type="C" size="50" />
		<field name="department" type="C" size="30" />
		<field name="fax" type="C" size="50" />
		<field name="reportsto" type="C" size="30" />
		<field name="training" type="C" size="50" />
		<field name="usertype" type="C" size="50" />
		<field name="contacttype" type="C" size="50" />
		<field name="otheremail" type="C" size="100" />
		<field name="secondaryemail" type="C" size="100" />
		<field name="donotcall" type="C" size="3" />
		<field name="emailoptout" type="C" size="3">
			<default value="0" />
		</field>
		<field name="imagename" type="C" size="150" />
		<field name="reference" type="C" size="3" />
		<field name="notify_owner" type="C" size="3">
			<default value="0"/>
		</field>

		<index name="contactdetails_accountid_idx">
			<col>accountid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_contactdetails" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_contactdetails FOREIGN KEY (contactid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_contactsubdetails">
		<field name="contactsubscriptionid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="homephone" type="C" size="50" />
		<field name="otherphone" type="C" size="50" />
		<field name="assistant" type="C" size="30" />
		<field name="assistantphone" type="C" size="50" />
		<field name="birthday" type="D" />
		<field name="laststayintouchrequest" type="I" size="30">
			<default value="0" />
		</field>
		<field name="laststayintouchsavedate" type="I" size="19">
			<default value="0" />
		</field>
		<field name="leadsource" type="C" size="200" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_contactsubdetails" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_contactsubdetails FOREIGN KEY (contactsubscriptionid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_contactaddress">
		<field name="contactaddressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="mailingcity" type="C" size="40" />
		<field name="mailingstreet" type="C" size="250" />
		<field name="mailingcountry" type="C" size="40" />
		<field name="othercountry" type="C" size="30" />
		<field name="mailingstate" type="C" size="30" />
		<field name="mailingpobox" type="C" size="30" />
		<field name="othercity" type="C" size="40" />
		<field name="otherstate" type="C" size="50" />
		<field name="mailingzip" type="C" size="30" />
		<field name="otherzip" type="C" size="30" />
		<field name="otherstreet" type="C" size="250" />
		<field name="otherpobox" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_contactaddress" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_contactaddress FOREIGN KEY (contactaddressid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_contactscf">
		<field name="contactid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_contactscf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_contactscf FOREIGN KEY (contactid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_portalinfo">
		<field name="id" type="I" size="11">
			<key />
		</field>
		<field name="user_name" type="C" size="50" />
		<field name="user_password" type="C" size="255" />
		<field name="type" type="C" size="5" />
		<field name="cryptmode" type="C" size="20" />
		<field name="last_login_time" type="T" />
		<field name="login_time" type="T" />
		<field name="logout_time" type="T" />
		<field name="isactive" type="I" size="1" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_portalinfo" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_portalinfo FOREIGN KEY (id) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_customerdetails">
		<field name="customerid" type="I" size="19">
			<key />
		</field>
		<field name="portal" type="C" size="3" />
		<field name="support_start_date" type="D" />
		<field name="support_end_date" type="D" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_customerdetails" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_customerdetails FOREIGN KEY (customerid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_leaddetails">
		<field name="leadid" type="I" size="19">
			<key />
		</field>
		<field name="lead_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="email" type="C" size="100" />
		<field name="interest" type="C" size="50" />
		<field name="firstname" type="C" size="40" />
		<field name="salutation" type="C" size="200" />
		<field name="lastname" type="C" size="80">
			<notnull />
		</field>
		<field name="company" type="C" size="100">
			<notnull />
		</field>
		<field name="annualrevenue" type="I" size="19">
			<default value="0" />
		</field>
		<field name="industry" type="C" size="200" />
		<field name="campaign" type="C" size="30" />
		<field name="rating" type="C" size="200" />
		<field name="leadstatus" type="C" size="50" />
		<field name="leadsource" type="C" size="200" />
		<field name="converted" type="I" size="1">
			<default value="0" />
		</field>
		<field name="designation" type="C" size="50">
			<default value="SalesMan" />
		</field>
		<field name="licencekeystatus" type="C" size="50" />
		<field name="space" type="C" size="250" />
		<field name="comments" type="X" />
		<field name="priority" type="C" size="50" />
		<field name="demorequest" type="C" size="50" />
		<field name="partnercontact" type="C" size="50" />
		<field name="productversion" type="C" size="20" />
		<field name="product" type="C" size="50" />
		<field name="maildate" type="D" />
		<field name="nextstepdate" type="D" />
		<field name="fundingsituation" type="C" size="50" />
		<field name="purpose" type="C" size="50" />
		<field name="evaluationstatus" type="C" size="50" />
		<field name="transferdate" type="D" />
		<field name="revenuetype" type="C" size="50" />
		<field name="noofemployees" type="I" size="50" />
		<field name="secondaryemail" type="C" size="100" />
		<field name="assignleadchk" type="I" size="1">
			<default value="0" />
		</field>

		<index name="leaddetails_converted_leadstatus_idx">
			<col>converted</col>
			<col>leadstatus</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_leaddetails" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_leaddetails FOREIGN KEY (leadid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_leadsubdetails">
		<field name="leadsubscriptionid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="website" type="C" size="255" />
		<field name="callornot" type="I" size="1">
			<default value="0" />
		</field>
		<field name="readornot" type="I" size="1">
			<default value="0" />
		</field>
		<field name="empct" type="I" size="10">
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_leadsubdetails" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_leadsubdetails FOREIGN KEY (leadsubscriptionid) REFERENCES vtiger_leaddetails(leadid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_leadaddress">
		<field name="leadaddressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="city" type="C" size="30" />
		<field name="code" type="C" size="30" />
		<field name="state" type="C" size="30" />
		<field name="pobox" type="C" size="30" />
		<field name="country" type="C" size="30" />
		<field name="phone" type="C" size="50" />
		<field name="mobile" type="C" size="50" />
		<field name="fax" type="C" size="50" />
		<field name="lane" type="C" size="250" />
		<field name="leadaddresstype" type="C" size="30">
			<default value="Billing" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_leadaddress" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_leadaddress FOREIGN KEY (leadaddressid) REFERENCES vtiger_leaddetails(leadid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_leadscf">
		<field name="leadid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_leadscf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_leadscf FOREIGN KEY (leadid) REFERENCES vtiger_leaddetails(leadid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_notes">
		<field name="notesid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="note_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="title" type="C" size="50">
			<notnull />
		</field>
		<field name="filename" type="C" size="200" />
		<field name="notecontent" type="X" />
		<field name="folderid" type="I" size="19">
			<default value="1" />
			<notnull />
		</field>
		<field name="filetype" type="C" size="50">
			<default value="NULL" />
		</field>
		<field name="filelocationtype" type="C" size="5">
			<default value="NULL" />
		</field>
		<field name="filedownloadcount" type="I" size="19">
			<default value="NULL" />
		</field>
		<field name="filestatus" type="I" size="19">
			<default value="NULL" />
		</field>
		<field name="filesize" type="I" size="19">
			<default value="0" />
			<notnull />
		</field>
		<field name="fileversion" type="C" size="50">
			<default value="NULL" />
		</field>
		<index name="notes_title_idx">
			<col>title</col>
		</index>
		<index name="notes_notesid_idx">
			<col>notesid</col>
		</index>
		<index name="notes_crmid_idx">
			<col>crmid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_notes" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_notes FOREIGN KEY (notesid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_potential">
		<field name="potentialid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="potential_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="related_to" type="I" size="19" />
		<field name="potentialname" type="C" size="120">
			<notnull />
		</field>
		<field name="amount" type="N" size="14.2">
			<default value="0" />
		</field>
		<field name="currency" type="C" size="20" />
		<field name="closingdate" type="D" />
		<field name="typeofrevenue" type="C" size="50" />
		<field name="nextstep" type="C" size="100" />
		<field name="private" type="I" size="1">
			<default value="0" />
		</field>
		<field name="probability" type="N" size="7.3">
			<default value="0" />
		</field>
		<field name="campaignid" type="I" size="19" />
		<field name="sales_stage" type="C" size="200" />
		<field name="potentialtype" type="C" size="200" />
		<field name="leadsource" type="C" size="200" />
		<field name="productid" type="I" size="50" />
		<field name="productversion" type="C" size="50" />
		<field name="quotationref" type="C" size="50" />
		<field name="partnercontact" type="C" size="50" />
		<field name="remarks" type="C" size="50" />
		<field name="runtimefee" type="I" size="19">
			<default value="0" />
		</field>
		<field name="followupdate" type="D" />
		<field name="evaluationstatus" type="C" size="50" />
		<field name="description" type="X" />
		<field name="forecastcategory" type="I" size="19">
			<default value="0" />
		</field>
		<field name="outcomeanalysis" type="I" size="19">
			<default value="0" />
		</field>

		<index name="potential_relatedto_idx">
			<col>related_to</col>
		</index>
		<index name="potentail_sales_stage_idx">
			<col>sales_stage</col>
		</index>
		<index name="potentail_sales_stage_amount_idx">
			<col>amount</col>
			<col>sales_stage</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_potential" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_potential FOREIGN KEY (potentialid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_potstagehistory">
		<field name="historyid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="potentialid" type="I" size="19">
			<notnull />
		</field>
		<field name="amount" type="N" />
		<field name="stage" type="C" size="100" />
		<field name="probability" type="N" size="7.3" />
		<field name="expectedrevenue" type="N" />
		<field name="closedate" type="D" />
		<field name="lastmodified" type="T" />

		<index name="potstagehistory_potentialid_idx">
			<col>potentialid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_potstagehistory" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_potstagehistory FOREIGN KEY (potentialid) REFERENCES vtiger_potential(potentialid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_potentialscf">
		<field name="potentialid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_potentialscf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_potentialscf FOREIGN KEY (potentialid) REFERENCES vtiger_potential(potentialid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_activity">
		<field name="activityid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="subject" type="C" size="100">
			<notnull />
		</field>
		<field name="semodule" type="C" size="20" />
		<field name="activitytype" type="C" size="200">
			<notnull />
		</field>

		<field name="date_start" type="D">
			<notnull />
		</field>
		<field name="due_date" type="D" />
		<field name="time_start" type="C" size="50" />
		<field name="time_end" type="C" size="50" />
		<field name="sendnotification" type="C" size="3">
			<notnull />
			<default value="0"/>
		</field>
		<field name="duration_hours" type="C" size="200" />
		<field name="duration_minutes" type="C" size="200" />
		<field name="status" type="C" size="200" />
		<field name="eventstatus" type="C" size="200" />
		<field name="priority" type="C" size="200" />
		<field name="location" type="C" size="150" />
		<field name="notime" type="C" size="3">
			<notnull />
			<default value="0" />
		</field>
		<field name="visibility" type="C" size="50">
			<notnull />
			<default value="all" />
		</field>
		<field name="recurringtype" type="C" size="200" />

		<index name="activity_activityid_subject_idx">
			<col>activityid</col>
			<col>subject</col>
		</index>
		<index name="activity_activitytype_date_start_idx">
			<col>activitytype</col>
			<col>date_start</col>
		</index>
		<index name="activity_date_start_due_date_idx">
			<col>date_start</col>
			<col>due_date</col>
		</index>
		<index name="activity_date_start_time_start_idx">
			<col>date_start</col>
			<col>time_start</col>
		</index>
		<index name="activity_eventstatus_idx">
			<col>eventstatus</col>
		</index>
		<index name="activity_status_idx">
			<col>status</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_activity" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_activity FOREIGN KEY (activityid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_attachments">
		<field name="attachmentsid" type="I" size="19">
			<key />
		</field>
		<field name="name" type="C" size="255">
			<notnull />
		</field>
		<field name="description" type="X" />
		<field name="type" type="C" size="100" />
		<field name="path" type="X" />
                <field name="storedname" type="C" size="255" />
		<field name="subject" type="C" size="255" />
		<index name="attachments_attachmentsid_idx">
			<col>attachmentsid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_attachments" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_attachments FOREIGN KEY (attachmentsid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_seattachmentsrel">
		<field name="crmid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="attachmentsid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<index name="seattachmentsrel_attachmentsid_idx">
			<col>attachmentsid</col>
		</index>
		<index name="seattachmentsrel_crmid_idx">
			<col>crmid</col>
		</index>
		<index name="seattachmentsrel_attachmentsid_crmid_idx">
			<col>attachmentsid</col>
			<col>crmid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_seattachmentsrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_seattachmentsrel FOREIGN KEY (attachmentsid) REFERENCES vtiger_attachments(attachmentsid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_seattachmentsrel FOREIGN KEY (crmid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_tracker">
		<field name="id" type="I" size="11">
			<key />
			<autoincrement />
		</field>
		<field name="user_id" type="C" size="36" />
		<field name="module_name" type="C" size="25" />
		<field name="item_id" type="C" size="36" />
		<field name="item_summary" type="C" size="255" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_vendor">
		<field name="vendorid" type="I" size="19">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="vendor_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="vendorname" type="C" size="100" />
		<field name="phone" type="C" size="100" />
		<field name="email" type="C" size="100" />
		<field name="website" type="C" size="100" />
		<field name="glacct" type="C" size="200" />
		<field name="category" type="C" size="50" />
		<field name="street" type="X" />
		<field name="city" type="C" size="30" />
		<field name="state" type="C" size="30" />
		<field name="pobox" type="C" size="30" />
		<field name="postalcode" type="C" size="100" />
		<field name="country" type="C" size="100" />
		<field name="description" type="X" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_vendor" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_vendor FOREIGN KEY (vendorid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_products">
		<field name="productid" type="I" size="11">
			<key />
		</field>
		<field name="product_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="productname" type="C" size="255">
			<notnull />
		</field>
		<field name="productcode" type="C" size="40" />
		<field name="productcategory" type="C" size="200" />
		<field name="manufacturer" type="C" size="200" />
		<field name="qty_per_unit" type="N" size="11.2">
			<default value="0" />
		</field>
		<field name="unit_price" type="N" size="25.2" />
		<field name="weight" type="N" size="11.3" />
		<field name="pack_size" type="I" size="11" />
		<field name="sales_start_date" type="D" />
		<field name="sales_end_date" type="D" />
		<field name="start_date" type="D" />
		<field name="expiry_date" type="D" />
		<field name="cost_factor" type="I" size="11" />
		<field name="commissionrate" type="N" size="7.3" />
		<field name="commissionmethod" type="C" size="50" />
		<field name="discontinued" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="usageunit" type="C" size="200" />
		<field name="reorderlevel" type="I" size="11" />
		<field name="website" type="C" size="100" />
		<field name="taxclass" type="C" size="200" />
		<field name="mfr_part_no" type="C" size="200" />
		<field name="vendor_part_no" type="C" size="200" />
		<field name="serialno" type="C" size="200" />
		<field name="qtyinstock" type="N" size="25.3" />
		<field name="productsheet" type="C" size="200" />
		<field name="qtyindemand" type="I" size="11" />
		<field name="glacct" type="C" size="200" />
		<field name="vendor_id" type="I" size="11" />
		<field name="imagename" type="X" />
		<field name="currency_id" type="I" size="19">
			<notnull />
			<default value="1" />
		</field>
		<field name="is_subproducts_viewable" type="I" size="1">
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_products" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_products FOREIGN KEY (productid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_currency">
		<field name="currencyid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="currency" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>

		<index name="currency_currency_idx">
			<unique />
			<col>currency</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_visibility">
		<field name="visibilityid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="visibility" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0"/>
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1"/>
		</field>

		<index name="visibility_visibility_idx">
			<col>visibility</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_manufacturer">
		<field name="manufacturerid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="manufacturer" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="manufacturer_manufacturer_idx">
			<unique />
			<col>manufacturer</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_role">
		<field name="roleid" type="C" size="255">
			<key />
		</field>
		<field name="rolename" type="C" size="200" />
		<field name="parentrole" type="C" size="255" />
		<field name="depth" type="I" size="19" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_user2role">
		<field name="userid" type="I" size="11">
			<key />
		</field>
		<field name="roleid" type="C" size="255">
			<notnull />
		</field>

		<index name="user2role_roleid_idx">
			<col>roleid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_user2role" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_user2role FOREIGN KEY (roleid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_user2role FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_groups">
		<field name="groupid" type="I" size="19">
			<key />
		</field>
		<field name="groupname" type="C" size="100" />
		<field name="description" type="X" />

		<index name="groups_groupname_idx">
			<col>groupname</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_users2group">
		<field name="groupid" type="I" size="19">
			<key />
		</field>
		<field name="userid" type="I" size="19">
			<key />
		</field>

		<index name="users2group_groupname_uerid_idx">
			<col>groupid</col>
			<col>userid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_users2group" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_users2group FOREIGN KEY (groupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_users2group FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_group2grouprel">
		<field name="groupid" type="I" size="19">
			<key />
		</field>
		<field name="containsgroupid" type="I" size="19">
			<key />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_group2grouprel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_group2grouprel FOREIGN KEY (containsgroupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE ON UPDATE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_group2grouprel FOREIGN KEY (groupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE ON UPDATE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_group2role">
		<field name="groupid" type="I" size="19">
			<key />
		</field>
		<field name="roleid" type="C" size="255">
			<key />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_group2role" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_group2role FOREIGN KEY (groupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_group2role FOREIGN KEY (roleid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_group2rs">
		<field name="groupid" type="I" size="19">
			<key />
		</field>
		<field name="roleandsubid" type="C" size="255">
			<key />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_group2rs" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_group2rs FOREIGN KEY (groupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_group2rs FOREIGN KEY (roleandsubid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_campaign">
		<field name="campaign_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="campaignname" type="C" size="255" />
		<field name="campaigntype" type="C" size="200" />
		<field name="campaignstatus" type="C" size="200" />
		<field name="expectedrevenue" type="N" size="25.3" />
		<field name="budgetcost" type="N" size="25.3" />
		<field name="actualcost" type="N" size="25.3" />
		<field name="expectedresponse" type="C" size="200" />
		<field name="numsent" type="N" size="11.0" />
		<field name="product_id" type="I" size="19" />
		<field name="sponsor" type="C" size="255" />
		<field name="targetaudience" type="C" size="255" />
		<field name="targetsize" type="I" size="19" />
		<field name="expectedresponsecount" type="I" size="19" />
		<field name="expectedsalescount" type="I" size="19" />
		<field name="expectedroi" type="N" size="25.3" />
		<field name="actualresponsecount" type="I" size="19" />
		<field name="actualsalescount" type="I" size="19" />
		<field name="actualroi" type="N" size="25.3" />
		<field name="campaignid" type="I" size="19">
			<key />
		</field>
		<field name="closingdate" type="D" />

		<index name="campaign_campaignstatus_idx">
			<col>campaignstatus</col>
		</index>
		<index name="campaign_campaignname_idx">
			<col>campaignname</col>
		</index>
		<index name="campaign_campaignid_idx">
			<col>campaignid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_campaignscf">
		<field name="campaignid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_campaignscf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_campaignscf FOREIGN KEY (campaignid) REFERENCES vtiger_campaign(campaignid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_campaigncontrel">
		<field name="campaignid" type="I" size="19">
			<key />
			<default value="0"/>
		</field>
		<field name="contactid" type="I" size="19">
			<key />
			<notnull />
			<default value="0"/>
		</field>
		<field name="campaignrelstatusid" type="I" size="19">
			<key />
			<notnull />
			<default value="0"/>
		</field>

		<index name="campaigncontrel_contractid_idx">
			<col>contactid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_campaigncontrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_campaigncontrel FOREIGN KEY (campaignid) REFERENCES vtiger_campaign(campaignid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_campaigncontrel FOREIGN KEY (contactid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_campaignleadrel">
		<field name="campaignid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="leadid" type="I" size="19">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="campaignrelstatusid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<index name="campaignleadrel_leadid_campaignid_idx">
			<col>leadid</col>
			<col>campaignid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_campaignleadrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_campaignleadrel FOREIGN KEY (campaignid) REFERENCES vtiger_campaign(campaignid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_campaignleadrel FOREIGN KEY (leadid) REFERENCES vtiger_leaddetails(leadid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_contpotentialrel">
		<field name="contactid" type="I" size="19">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="potentialid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<index name="contpotentialrel_potentialid_idx">
			<col>potentialid</col>
		</index>
		<index name="contpotentialrel_contactid_idx">
			<col>contactid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_contpotentialrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_contpotentialrel FOREIGN KEY (contactid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_contpotentialrel FOREIGN KEY (potentialid) REFERENCES vtiger_potential(potentialid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_cntactivityrel">
		<field name="contactid" type="I" size="19">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="activityid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<index name="cntactivityrel_contactid_idx">
			<col>contactid</col>
		</index>
		<index name="cntactivityrel_activityid_idx">
			<col>activityid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_cntactivityrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_cntactivityrel FOREIGN KEY (activityid) REFERENCES vtiger_activity(activityid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_cntactivityrel FOREIGN KEY (contactid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_troubletickets">
		<field name="ticketid" type="I" size="19">
			<key />
		</field>
		<field name="ticket_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="groupname" type="C" size="100" />
		<field name="parent_id" type="C" size="100" />
		<field name="product_id" type="C" size="100" />
		<field name="priority" type="C" size="200" />
		<field name="severity" type="C" size="200" />
		<field name="status" type="C" size="200" />
		<field name="category" type="C" size="200" />
		<field name="title" type="C" size="255">
			<notnull />
		</field>
		<field name="solution" type="X" />
		<field name="update_log" type="X" />
		<field name="version_id" type="I" size="11" />
		<field name="hours" type="C" size="200" />
		<field name="days" type="C" size="200" />
		<index name="troubletickets_ticketid_idx">
			<col>ticketid</col>
		</index>
		<index name="troubletickets_status_idx">
			<col>status</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_troubletickets" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_troubletickets FOREIGN KEY (ticketid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_ticketcomments">
		<field name="commentid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="ticketid" type="I" size="19">
		</field>
		<field name="comments" type="X" />
		<field name="ownerid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="ownertype" type="C" size="10" />
		<field name="createdtime" type="T" >
			<notnull />
		</field>

		<index name="ticketcomments_ticketid_idx">
			<col>ticketid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ticketcomments" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_ticketcomments FOREIGN KEY (ticketid) REFERENCES vtiger_troubletickets(ticketid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_salesmanactivityrel">
		<field name="smid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="activityid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<index name="salesmanactivityrel_activityid_idx">
			<col>activityid</col>
		</index>
		<index name="salesmanactivityrel_smid_idx">
			<col>smid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_salesmanactivityrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_salesmanactivityrel FOREIGN KEY (activityid) REFERENCES vtiger_activity(activityid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_salesmanactivityrel FOREIGN KEY (smid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_vendorcontactrel">
		<field name="vendorid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="contactid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<index name="vendorcontactrel_vendorid_idx">
			<col>vendorid</col>
		</index>
		<index name="vendorcontactrel_contact_idx">
			<col>contactid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_vendorcontactrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_vendorcontactrel FOREIGN KEY (contactid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_vendorcontactrel FOREIGN KEY (vendorid) REFERENCES vtiger_vendor(vendorid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_salesmanticketrel">
		<field name="smid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="id" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<index name="salesmanticketrel_smid_idx">
			<col>smid</col>
		</index>
		<index name="salesmanticketrel_id_idx">
			<col>id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_salesmanticketrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_salesmanticketrel FOREIGN KEY (id) REFERENCES vtiger_troubletickets(ticketid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_salesmanticketrel FOREIGN KEY (smid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_seactivityrel">
		<field name="crmid" type="I" size="19">
			<key />
		</field>
		<field name="activityid" type="I" size="19">
			<key />
		</field>

		<index name="seactivityrel_activityid_idx">
			<col>activityid</col>
		</index>
		<index name="seactivityrel_crmid_idx">
			<col>crmid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_seactivityrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_seactivityrel FOREIGN KEY (activityid) REFERENCES vtiger_activity(activityid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_seactivityrel FOREIGN KEY (crmid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_seproductsrel">
		<field name="crmid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="productid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="setype" type="C" size="30">
			<notnull />
		</field>
		<field name="quantity" type="I" size="19">
			<default value="1" />
		</field>

		<index name="seproductsrel_productid_idx">
			<col>productid</col>
		</index>
		<index name="seproductrel_crmid_idx">
			<col>crmid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_seproductsrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_seproductsrel FOREIGN KEY (crmid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_seproductsrel FOREIGN KEY (productid) REFERENCES vtiger_products(productid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_seticketsrel">
		<field name="crmid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="ticketid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<index name="seticketsrel_crmid_idx">
			<col>crmid</col>
		</index>
		<index name="seticketsrel_ticketid_idx">
			<col>ticketid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_seticketsrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_seticketsrel FOREIGN KEY (crmid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_seticketsrel FOREIGN KEY (ticketid) REFERENCES vtiger_troubletickets(ticketid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_import_maps">
		<field name="id" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="name" type="C" size="36">
			<notnull />
		</field>
		<field name="module" type="C" size="36">
			<notnull />
		</field>
		<field name="content" type="B" />
		<field name="has_header" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="deleted" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="date_entered" type="T">
			<notnull />
			<deftimestamp />
		</field>
		<field name="date_modified" type="T">
			<!-- Troublesome with STRICT_TRANS_TABLE mode depends on MySQL 5.5 or 5.6+ -->
			<!--
			<notnull />
			<deftimestamp />
			-->
		</field>
		<field name="assigned_user_id" type="C" size="36" />
		<field name="is_published" type="C" size="3">
			<notnull />
			<default value="no" />
		</field>
		<index name="import_maps_assigned_user_id_module_name_deleted_idx">
			<col>assigned_user_id</col>
			<col>module</col>
			<col>name</col>
			<col>deleted</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_systems">
		<field name="id" type="I" size="19">
			<key />
		</field>
		<field name="server" type="C" size="100" />
		<field name="server_port" type="I" size="19" />
		<field name="server_username" type="C" size="100" />
		<field name="server_password" type="C" size="255" />
		<field name="server_type" type="C" size="20" />
		<field name="smtp_auth" type="C" size="5" />
		<field name="server_path" type="C" size="256" />
		<field name="from_email_field" type="C" size="50" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_currency_info">
		<field name="id" type="I" >
			<key />
			<autoincrement />

		</field>
		<field name="currency_name" type="C" size="100" />
		<field name="currency_code" type="C" size="100" />
		<field name="currency_symbol" type="C" size="30" />
		<field name="conversion_rate" type="N" size="10.3" />
		<field name="currency_status" type="C" size="25" />
		<field name="defaultid" type="C" size="10">
			<notnull />
			<default value="0" />
		</field>
		<field name="deleted" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_defaultcv">
		<field name="tabid" type="I" size="19">
			<key />
		</field>
		<field name="defaultviewname" type="C" size="50">
			<notnull />
		</field>
		<field name="query" type="X" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_defaultcv" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_defaultcv FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_emailtemplates">
		<field name="foldername" type="C" size="100" />
		<field name="templatename" type="C" size="100" />
		<field name="templatepath" type="C" size="100" />
		<field name="subject" type="C" size="100" />
		<field name="description" type="X" />
		<field name="body" type="X" />
		<field name="deleted" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="templateid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="systemtemplate" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="module" type="C" size="100" />

		<index name="emailtemplates_foldernamd_templatename_subject_idx">
			<col>foldername</col>
			<col>templatename</col>
			<col>subject</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_faq">
		<field name="id" type="I" size="11">
			<key />
			<autoincrement />
		</field>
		<field name="faq_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="product_id" type="C" size="100" />
		<field name="question" type="X">
			<notnull />
		</field>
		<field name="answer" type="X" />
		<field name="category" type="C" size="200">
			<notnull />
		</field>
		<field name="status" type="C" size="200">
			<notnull />
		</field>

		<index name="faq_id_idx">
			<col>id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_faq" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_faq FOREIGN KEY (id) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_faqcomments">
		<field name="commentid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="faqid" type="I" size="19">
		</field>
		<field name="comments" type="X" />
		<field name="createdtime" type="T">
			<notnull />
		</field>

		<index name="faqcomments_faqid_idx">
			<col>faqid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_faqcomments" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_faqcomments FOREIGN KEY (faqid) REFERENCES vtiger_faq(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_loginhistory">
		<field name="login_id" type="I" size="11">
			<key />
			<autoincrement />
		</field>
		<field name="user_name" type="C" size="25">
			<notnull />
		</field>
		<field name="user_ip" type="C" size="25">
			<notnull />
		</field>
		<field name="logout_time" type="T">
			<deftimestamp />
		</field>
		<field name="login_time" type="T">
			<!-- Troublesome with STRICT_TRANS_TABLE mode depends on MySQL 5.5 or 5.6+ -->
			<!--
				<deftimestamp />
			-->
		</field>
		<field name="status" type="C" size="25" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_senotesrel">
		<field name="crmid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="notesid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<index name="senotesrel_notesid_idx">
			<col>notesid</col>
		</index>
		<index name="senotesrel_crmid_idx">
			<col>crmid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_senotesrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_senotesrel FOREIGN KEY (crmid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_senotesrel FOREIGN KEY (notesid) REFERENCES vtiger_notes(notesid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_ticketcf">
		<field name="ticketid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="from_portal" type="C" size="3" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ticketcf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_ticketcf FOREIGN KEY (ticketid) REFERENCES vtiger_troubletickets(ticketid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_productcf">
		<field name="productid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_productcf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_productcf FOREIGN KEY (productid) REFERENCES vtiger_products(productid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_users_last_import">
		<field name="id" type="I" size="36">
			<autoincrement />
			<key />
		</field>
		<field name="assigned_user_id" type="C" size="36" />
		<field name="bean_type" type="C" size="36" />
		<field name="bean_id" type="C" size="36" />
		<field name="deleted" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>

		<index name="idx_user_id">
			<col>assigned_user_id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_wordtemplates">
		<field name="templateid" type="I" size="19">
			<key />
		</field>
		<field name="filename" type="C" size="100">
			<notnull />
		</field>
		<field name="module" type="C" size="30">
			<notnull />
		</field>
		<field name="date_entered" type="T">
			<notnull />
			<deftimestamp />
		</field>
		<field name="parent_type" type="C" size="50">
			<notnull />
		</field>
		<field name="data" type="B">
			<notnull />
		</field>
		<field name="description" type="X" />
		<field name="filesize" type="C" size="50">
			<notnull />
		</field>
		<field name="filetype" type="C" size="20">
			<notnull />
		</field>
		<field name="deleted" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_accountrating">
		<field name="accountratingid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="rating" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>

		<index name="accountrating_rating_idx">
			<col>rating</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_accounttype">
		<field name="accounttypeid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="accounttype" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="accounttype_accounttype_idx">
			<col>accounttype</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_leadsource">
		<field name="leadsourceid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="leadsource" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="leadsource_source_idx">
			<col>source</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_opportunity_type">
		<field name="opptypeid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="opportunity_type" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="opportunity_type_opportunity_type_idx">
			<col>opportunity_type</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_leadstage">
		<field name="leadstageid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="stage" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>

		<index name="leadstage_stage_idx">
			<col>stage</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_leadstatus">
		<field name="leadstatusid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="leadstatus" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="leadstatus_status_idx">
			<col>status</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_eventstatus">
		<field name="eventstatusid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="eventstatus" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="eventstatus_status_idx">
			<col>status</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_duration_minutes">
		<field name="minutesid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="duration_minutes" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>

		<index name="duration_minutes_status_idx">
			<col>status</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_opportunitystage">
		<field name="potstageid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="stage" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="probability" type="N" size="3.2">
			<default value="0" />
		</field>

		<index name="opportunitystage_stage_idx">
			<col>stage</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_priority">
		<field name="priorityid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="priority" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>

		<index name="priority_priority_idx">
			<col>priority</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_industry">
		<field name="industryid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="industry" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="industry_industry_idx">
			<col>industry</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_salutationtype">
		<field name="salutationid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="salutationtype" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_taskpriority">
		<field name="taskpriorityid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="taskpriority" type="C" size="200" />
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_taskstatus">
		<field name="taskstatusid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="taskstatus" type="C" size="200" />
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_activitytype">
		<field name="activitytypeid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="activitytype" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="activitytype_activitytype_idx">
			<col>activitytype</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_sales_stage">
		<field name="sales_stage_id" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="sales_stage" type="C" size="200">
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_faqcategories">
		<field name="faqcategories_id" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="faqcategories" type="C" size="200" />
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_faqstatus">
		<field name="faqstatus_id" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="faqstatus" type="C" size="200" />
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_faqcf"> 
		<field name="faqid" type="I" size="19"> 
			<key /> 
			<default value="0" /> 
		</field> 

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt> 
	</table> 

	<table name="vtiger_faqcf" alter="true"> 
		<constraint>ADD CONSTRAINT fk_1_vtiger_faqcf FOREIGN KEY (faqid) REFERENCES vtiger_faq(id) ON DELETE CASCADE</constraint> 
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt> 
		<data> 
		</data> 
	</table> 

	<table name="vtiger_rating">
		<field name="rating_id" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="rating" type="C" size="200" />
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ticketcategories">
		<field name="ticketcategories_id" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="ticketcategories" type="C" size="200" />
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ticketpriorities">
		<field name="ticketpriorities_id" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="ticketpriorities" type="C" size="200" />
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ticketseverities">
		<field name="ticketseverities_id" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="ticketseverities" type="C" size="200" />
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ticketstatus">
		<field name="ticketstatus_id" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="ticketstatus" type="C" size="200" />
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="0" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_durationhrs">
		<field name="hrsid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="hrs" type="C" size="50" />
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_durationmins">
		<field name="minsid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="mins" type="C" size="50" />
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_profile">
		<field name="profileid" type="I" size="10">
			<key />
			<autoincrement />
		</field>
		<field name="profilename" type="C" size="50">
			<notnull />
		</field>
		<field name="description" type="X" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_profile2globalpermissions">
		<field name="profileid" type="I" size="19">
			<key />
		</field>
		<field name="globalactionid" type="I" size="19">
			<key />
		</field>
		<field name="globalactionpermission" type="I" size="19" />

		<index name="idx_profile2globalpermissions">
			<col>profileid</col>
			<col>globalactionid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_profile2globalpermissions" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_profile2globalpermissions FOREIGN KEY (profileid) REFERENCES vtiger_profile(profileid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_profile2tab">
		<field name="profileid" type="I" size="11" />
		<field name="tabid" type="I" size="10" />
		<field name="permissions" type="I" size="10">
			<notnull />
			<default value="0" />
		</field>

		<index name="profile2tab_profileid_tabid_idx">
			<col>profileid</col>
			<col>tabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_profile2standardpermissions">
		<field name="profileid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="10">
			<key />
		</field>
		<field name="operation" type="I" size="10">
			<key />
		</field>
		<field name="permissions" type="I" size="1" />

		<index name="profile2standardpermissions_profileid_tabid_operation_idx">
			<col>profileid</col>
			<col>tabid</col>
			<col>operation</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_profile2field">
		<field name="profileid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="10" />
		<field name="fieldid" type="I" size="19">
			<key />
		</field>
		<field name="visible" type="I" size="19" />
		<field name="readonly" type="I" size="19" />

		<index name="profile2field_profileid_tabid_fieldname_idx">
			<col>profileid</col>
			<col>tabid</col>
			<col>fieldname</col>
		</index>
		<index name="profile2field_tabid_profileid_idx">
			<col>tabid</col>
			<col>profileid</col>
		</index>
		<index name="profile2field_visible_profileid_idx">
			<col>visible</col>
			<col>profileid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_role2profile">
		<field name="roleid" type="C" size="255">
			<key />
		</field>
		<field name="profileid" type="I" size="11">
			<key />
		</field>
		<index name="role2profile_roleid_profileid_idx">
			<col>roleid</col>
			<col>profileid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_org_share_action_mapping">
		<field name="share_action_id" type="I" size="19">
			<key />
		</field>
		<field name="share_action_name" type="C" size="200" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_def_org_share">
		<field name="ruleid" type="I" size="11">
			<key />
			<autoincrement />
		</field>
		<field name="tabid" type="I" size="11">
			<notnull />
		</field>
		<field name="permission" type="I" size="19">
		</field>
		<field name="editstatus" type="I" size="19" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_def_org_share" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_def_org_share FOREIGN KEY (permission) REFERENCES vtiger_org_share_action_mapping(share_action_id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_def_org_field">
		<field name="tabid" type="I" size="10" />
		<field name="fieldid" type="I" size="19">
			<key />
		</field>
		<field name="visible" type="I" size="19" />
		<field name="readonly" type="I" size="19" />

		<index name="def_org_field_tabid_fieldid_idx">
			<col>tabid</col>
			<col>fieldid</col>
		</index>
		<index name="def_org_field_tabid_idx">
			<col>tabid</col>
		</index>
		<index name="def_org_field_visible_fieldid_idx">
			<col>visible</col>
			<col>fieldid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_profile2utility">
		<field name="profileid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="11">
			<key />
		</field>
		<field name="activityid" type="I" size="11">
			<key />
		</field>
		<field name="permission" type="I" size="1" />

		<index name="profile2utility_profileid_tabid_activityid_idx">
			<col>profileid</col>
			<col>tabid</col>
			<col>activityid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_productcategory">
		<field name="productcategoryid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="productcategory" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="productcategory_productcategory_idx">
			<col>productcategory</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_salesorder">
		<field name="salesorderid" type="I" size="19">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="subject" type="C" size="100" />
		<field name="potentialid" type="I" size="19" />
		<field name="customerno" type="C" size="100" />
		<field name="salesorder_no" type="C" size="100" />
		<field name="quoteid" type="I" size="19" />
		<field name="vendorterms" type="C" size="100" />
		<field name="contactid" type="I" size="19">
		</field>
		<field name="vendorid" type="I" size="19">
		</field>
		<field name="duedate" type="D" />
		<field name="carrier" type="C" size='200' />
		<field name="pending" type="C" size='200' />
		<field name="type" type="C" size="100" />
		<field name="adjustment" type="N" size="25.3" />
		<field name="salescommission" type="N" size="25.3" />
		<field name="exciseduty" type="N" size="25.3" />
		<field name="total" type="N" size="25.3" />
		<field name="subtotal" type="N" size="25.3" />
		<field name="taxtype" type="C" size="25" />
		<field name="discount_percent" type="N" size="25.3" />
		<field name="discount_amount" type="N" size="25.3" />
		<field name="s_h_amount" type="N" size="25.3" />
		<field name="accountid" type="I" size="19" />
		<field name="terms_conditions" type="X" />
		<field name="purchaseorder" type="C" size='200' />
		<field name="sostatus" type="C" size='200' />
		<field name="currency_id" type="I" size='19' >
			<notnull />
			<default value="1" />
		</field>
		<field name="conversion_rate" type="N" size='10.3' >
			<notnull />
			<default value="1.000" />
		</field>
		<field name="enable_recurring" type="I">
			<default value="0" />
		</field>
		<field name="compound_taxes_info" type="X" />
		<index name="salesorder_vendorid_idx">
			<col>vendorid</col>
		</index>
		<index name="salesorder_contactid_idx">
			<col>contactid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_salesorder" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_salesorder FOREIGN KEY (contactid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_salesorder FOREIGN KEY (salesorderid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_salesorder FOREIGN KEY (vendorid) REFERENCES vtiger_vendor(vendorid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_sobillads">
		<field name="sobilladdressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="bill_city" type="C" size="30" />
		<field name="bill_code" type="C" size="30" />
		<field name="bill_country" type="C" size="30" />
		<field name="bill_state" type="C" size="30" />
		<field name="bill_street" type="C" size="250" />
		<field name="bill_pobox" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_sobillads" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_sobillads FOREIGN KEY (sobilladdressid) REFERENCES vtiger_salesorder(salesorderid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_soshipads">
		<field name="soshipaddressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="ship_city" type="C" size="30" />
		<field name="ship_code" type="C" size="30" />
		<field name="ship_country" type="C" size="30" />
		<field name="ship_state" type="C" size="30" />
		<field name="ship_street" type="C" size="250" />
		<field name="ship_pobox" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_soshipads" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_soshipads FOREIGN KEY (soshipaddressid) REFERENCES vtiger_salesorder(salesorderid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_salesordercf">
		<field name="salesorderid" type="I" size="19">
			<key />
			<default value="0"/>
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_salesordercf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_salesordercf FOREIGN KEY (salesorderid) REFERENCES vtiger_salesorder(salesorderid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>


	<table name="vtiger_notificationscheduler">
		<field name="schedulednotificationid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="schedulednotificationname" type="C" size="200" />
		<field name="active" type="I" size="1" />
		<field name="notificationsubject" type="C" size="200" />
		<field name="notificationbody" type="X" />
		<field name="label" type="C" size="50" />
		<field name="type" type="C" size="10" />
		<index name="notificationscheduler_schedulednotificationname_idx">
			<col>schedulednotificationname</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_activityproductrel">
		<field name="activityid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="productid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<index name="activityproductrel_activityid_idx">
			<col>activityid</col>
		</index>
		<index name="activityproductrel_productid_idx">
			<col>productid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_activityproductrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_activityproductrel FOREIGN KEY (activityid) REFERENCES vtiger_activity(activityid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_activityproductrel FOREIGN KEY (productid) REFERENCES vtiger_products(productid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_relatedlists">
		<field name="relation_id" type="I" size="19">
			<key />
		</field>
		<field name="tabid" type="I" size="10" />
		<field name="related_tabid" type="I" size="10" />
		<field name="name" type="C" size="100" />
		<field name="sequence" type="I" size="10" />
		<field name="label" type="C" size="100" />
		<field name="presence" type="I" size="10">
			<notnull />
			<default value="0" />
		</field>
		<field name="actions" type="C" size="50">
			<notnull />
			<default value="" />
		</field>
		<field name="relationfieldid" type="I" size="19" />
		<field name="source" type="C" size="25" />
		<field name="relationtype" type="C" size="10" />

		<index name="relatedlists_relation_id_idx">
			<col>relation_id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_rss">
		<field name="rssid" type="I" size="19">
			<key />
		</field>
		<field name="rssurl" type="C" size="200">
			<notnull />
			<default value="" />
		</field>
		<field name="rsstitle" type="C" size="200" />
		<field name="rsstype" type="I" size="10">
			<default value="0" />
		</field>
		<field name="starred" type="I" size="1">
			<default value="0" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_vendorcf">
		<field name="vendorid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_vendorcf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_vendorcf FOREIGN KEY (vendorid) REFERENCES vtiger_vendor(vendorid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_pricebook">
		<field name="pricebookid" type="I" size="19">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="pricebook_no" type="C" size="100">
			<notnull />
			<unique />
		</field>
		<field name="bookname" type="C" size="100" />
		<field name="active" type="I" size="1" />
		<field name="currency_id" type="I" size="19">
			<notnull />
			<default value="1" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_pricebook" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_pricebook FOREIGN KEY (pricebookid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_pricebookcf">
		<field name="pricebookid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_pricebookcf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_pricebookcf FOREIGN KEY (pricebookid) REFERENCES vtiger_pricebook(pricebookid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_pricebookproductrel">
		<field name="pricebookid" type="I" size="19">
			<key />
		</field>
		<field name="productid" type="I" size="19">
			<key />
		</field>
		<field name="listprice" type="N" size="25.3" />
		<field name="usedcurrency" type="I" size="11">
			<notnull />
			<default value="1" />
		</field>

		<index name="pricebookproductrel_pricebookid_idx">
			<col>pricebookid</col>
		</index>
		<index name="pricebookproductrel_productid_idx">
			<col>productid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_pricebookproductrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_pricebookproductrel FOREIGN KEY (pricebookid) REFERENCES vtiger_pricebook(pricebookid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_mail_accounts">
		<field name="account_id" type="I" size="11">
			<key />
		</field>
		<field name="user_id" type="I" size="11">
			<notnull />
		</field>
		<field name="display_name" type="C" size="50" />
		<field name="mail_id" type="C" size="50" />
		<field name="account_name" type="C" size="50" />
		<field name="mail_protocol" type="C" size="20" />
		<field name="mail_username" type="C" size="50">
			<notnull />
		</field>
		<field name="mail_password" type="C" size="250">
			<notnull />
		</field>
		<field name="mail_servername" type="C" size="50" />
		<field name="box_refresh" type="I" size="10" />
		<field name="mails_per_page" type="I" size="10" />
		<field name="ssltype" type="C" size="50" />
		<field name="sslmeth" type="C" size="50" />
		<field name="int_mailer" type="I" size="1">
			<default value="0" />
		</field>
		<field name="status" type="C" size="10" />
		<field name="set_default" type="I" size="2" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_quotes">
		<field name="quoteid" type="I" size="19">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="subject" type="C" size="100" />
		<field name="potentialid" type="I" size="19">
		</field>
		<field name="quotestage" type="C" size="200" />
		<field name="validtill" type="D" />
		<field name="contactid" type="I" size="19">
		</field>
		<field name="quote_no" type="C" size="100" />
		<field name="subtotal" type="N" size="25.3" />
		<field name="carrier" type="C" size='200' />
		<field name="shipping" type="C" size="100" />
		<field name="inventorymanager" type="I" size="19" />
		<field name="type" type="C" size="100" />
		<field name="adjustment" type="N" size="25.3" />
		<field name="total" type="N" size="25.3" />
		<field name="taxtype" type="C" size="25" />
		<field name="discount_percent" type="N" size="25.3" />
		<field name="discount_amount" type="N" size="25.3" />
		<field name="s_h_amount" type="N" size="25.3" />
		<field name="accountid" type="I" size="19" />
		<field name="terms_conditions" type="X" />
		<field name="currency_id" type="I" size='19' >
			<notnull />
			<default value="1" />
		</field>
		<field name="conversion_rate" type="N" size='10.3' >
			<notnull />
			<default value="1.000" />
		</field>
		<field name="compound_taxes_info" type="X" />
		<index name="quote_quotestage_idx">
			<col>quotestage</col>
		</index>
		<index name="quotes_potentialid_idx">
			<col>potentialid</col>
		</index>
		<index name="quotes_contactid_idx">
			<col>contactid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_quotes" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_quotes FOREIGN KEY (contactid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_quotes FOREIGN KEY (quoteid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_quotes FOREIGN KEY (potentialid) REFERENCES vtiger_potential(potentialid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_quotesbillads">
		<field name="quotebilladdressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="bill_city" type="C" size="30" />
		<field name="bill_code" type="C" size="30" />
		<field name="bill_country" type="C" size="30" />
		<field name="bill_state" type="C" size="30" />
		<field name="bill_street" type="C" size="250" />
		<field name="bill_pobox" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_quotesbillads" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_quotesbillads FOREIGN KEY (quotebilladdressid) REFERENCES vtiger_quotes(quoteid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_quotesshipads">
		<field name="quoteshipaddressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="ship_city" type="C" size="30" />
		<field name="ship_code" type="C" size="30" />
		<field name="ship_country" type="C" size="30" />
		<field name="ship_state" type="C" size="30" />
		<field name="ship_street" type="C" size="250" />
		<field name="ship_pobox" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_quotesshipads" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_quotesshipads FOREIGN KEY (quoteshipaddressid) REFERENCES vtiger_quotes(quoteid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_quotescf">
		<field name="quoteid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_quotescf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_quotescf FOREIGN KEY (quoteid) REFERENCES vtiger_quotes(quoteid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_purchaseorder">
		<field name="purchaseorderid" type="I" size="19">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="subject" type="C" size="100" />
		<field name="quoteid" type="I" size="19">
		</field>
		<field name="vendorid" type="I" size="19">
		</field>
		<field name="requisition_no" type="C" size="100" />
		<field name="purchaseorder_no" type="C" size="100" />
		<field name="tracking_no" type="C" size="100" />
		<field name="contactid" type="I" size="19">
		</field>
		<field name="duedate" type="D" />
		<field name="carrier" type="C" size='200' />
		<field name="type" type="C" size="100" />
		<field name="adjustment" type="N" size="25.3" />
		<field name="salescommission" type="N" size="25.3" />
		<field name="exciseduty" type="N" size="25.3" />
		<field name="total" type="N" size="25.3" />
		<field name="subtotal" type="N" size="25.3" />
		<field name="taxtype" type="C" size="25" />
		<field name="discount_percent" type="N" size="25.3" />
		<field name="discount_amount" type="N" size="25.3" />
		<field name="s_h_amount" type="N" size="25.3" />
		<field name="terms_conditions" type="X" />
		<field name="postatus" type="C" size="200" />
		<field name="currency_id" type="I" size='19' >
			<notnull />
			<default value="1" />
		</field>
		<field name="conversion_rate" type="N" size='10.3' >
			<notnull />
			<default value="1.000" />
		</field>
		<field name="compound_taxes_info" type="X" />

		<index name="purchaseorder_vendorid_idx">
			<col>vendorid</col>
		</index>
		<index name="purchaseorder_quoteid_idx">
			<col>quoteid</col>
		</index>
		<index name="purchaseorder_contactid_idx">
			<col>contactid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_purchaseorder" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_purchaseorder FOREIGN KEY (contactid) REFERENCES vtiger_contactdetails(contactid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_purchaseorder FOREIGN KEY (purchaseorderid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_purchaseorder FOREIGN KEY (quoteid) REFERENCES vtiger_quotes(quoteid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_4_vtiger_purchaseorder FOREIGN KEY (vendorid) REFERENCES vtiger_vendor(vendorid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_pobillads">
		<field name="pobilladdressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="bill_city" type="C" size="30" />
		<field name="bill_code" type="C" size="30" />
		<field name="bill_country" type="C" size="30" />
		<field name="bill_state" type="C" size="30" />
		<field name="bill_street" type="C" size="250" />
		<field name="bill_pobox" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_pobillads" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_pobillads FOREIGN KEY (pobilladdressid) REFERENCES vtiger_purchaseorder(purchaseorderid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_poshipads">
		<field name="poshipaddressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="ship_city" type="C" size="30" />
		<field name="ship_code" type="C" size="30" />
		<field name="ship_country" type="C" size="30" />
		<field name="ship_state" type="C" size="30" />
		<field name="ship_street" type="C" size="250" />
		<field name="ship_pobox" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_poshipads" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_poshipads FOREIGN KEY (poshipaddressid) REFERENCES vtiger_purchaseorder(purchaseorderid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_purchaseordercf">
		<field name="purchaseorderid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_purchaseordercf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_purchaseordercf FOREIGN KEY (purchaseorderid) REFERENCES vtiger_purchaseorder(purchaseorderid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>


	<table name="vtiger_invoice">
		<field name="invoiceid" type="I" size="19">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="subject" type="C" size="100" />
		<field name="salesorderid" type="I" size="19">
		</field>
		<field name="customerno" type="C" size="100" />
		<field name="contactid" type="I" size="19" />
		<field name="notes" type="C" size="100" />
		<field name="invoicedate" type="D" />
		<field name="duedate" type="D" />
		<field name="invoiceterms" type="C" size="100" />
		<field name="type" type="C" size="100" />
		<field name="adjustment" type="N" size="25.3" />
		<field name="salescommission" type="N" size="25.3" />
		<field name="exciseduty" type="N" size="25.3" />
		<field name="subtotal" type="N" size="25.3" />
		<field name="total" type="N" size="25.3" />
		<field name="taxtype" type="C" size="25" />
		<field name="discount_percent" type="N" size="25.3" />
		<field name="discount_amount" type="N" size="25.3" />
		<field name="s_h_amount" type="N" size="25.3" />
		<field name="shipping" type="C" size="100" />
		<field name="accountid" type="I" size="19" />
		<field name="terms_conditions" type="X" />
		<field name="purchaseorder" type="C" size='200' />
		<field name="invoicestatus" type="C" size='200' />
		<field name="invoice_no" type="C" size='100' >
			<default value="NULL" />
			<unique />
		</field>
		<field name="currency_id" type="I" size='19' >
			<notnull />
			<default value="1" />
		</field>
		<field name="conversion_rate" type="N" size='10.3' >
			<notnull />
			<default value="1.000" />
		</field>
		<field name="compound_taxes_info" type="X" />
		<index name="invoice_purchaseorderid_idx">
			<col>invoiceid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_invoice" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_invoice FOREIGN KEY (invoiceid) REFERENCES vtiger_crmentity(crmid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_invoice FOREIGN KEY (salesorderid) REFERENCES vtiger_salesorder(salesorderid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_invoicebillads">
		<field name="invoicebilladdressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="bill_city" type="C" size="30" />
		<field name="bill_code" type="C" size="30" />
		<field name="bill_country" type="C" size="30" />
		<field name="bill_state" type="C" size="30" />
		<field name="bill_street" type="C" size="250" />
		<field name="bill_pobox" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_invoicebillads" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_invoicebillads FOREIGN KEY (invoicebilladdressid) REFERENCES vtiger_invoice(invoiceid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_invoiceshipads">
		<field name="invoiceshipaddressid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="ship_city" type="C" size="30" />
		<field name="ship_code" type="C" size="30" />
		<field name="ship_country" type="C" size="30" />
		<field name="ship_state" type="C" size="30" />
		<field name="ship_street" type="C" size="250" />
		<field name="ship_pobox" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_invoiceshipads" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_invoiceshipads FOREIGN KEY (invoiceshipaddressid) REFERENCES vtiger_invoice(invoiceid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_invoicecf">
		<field name="invoiceid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_invoicecf" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_invoicecf FOREIGN KEY (invoiceid) REFERENCES vtiger_invoice(invoiceid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>


	<table name="vtiger_activity_reminder">
		<field name="activity_id" type="I" size="11">
			<key />
			<notnull />
		</field>
		<field name="reminder_time" type="I" size="11">
			<notnull />
		</field>
		<field name="reminder_sent" type="I" size="2">
			<notnull />
		</field>
		<field name="recurringid" type="I" size="19">
			<key />
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_customview">
		<field name="cvid" type="I" size="19">
			<key />
		</field>
		<field name="viewname" type="C" size="100">
			<notnull />
		</field>
		<field name="setdefault" type="I" size="1">
			<default value="0" />
		</field>
		<field name="setmetrics" type="I" size="1">
			<default value="0" />
		</field>
		<field name="entitytype" type="C" size="25">
			<notnull />
		</field>
		<field name="status" type="I" size="1">
			<default value="1" />
		</field>
		<field name="userid" type="I" size="19">
			<default value="1" />
		</field>
		<index name="customview_entitytype_idx">
			<col>entitytype</col>
		</index>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_customview" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_customview FOREIGN KEY (entitytype) REFERENCES vtiger_tab (name) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_cvcolumnlist">
		<field name="cvid" type="I" size="19">
			<key />
			<notnull />
		</field>
		<field name="columnindex" type="I" size="11">
			<key />
			<notnull />
		</field>
		<field name="columnname" type="C" size="250">
			<default value="" />
		</field>

		<index name="cvcolumnlist_columnindex_idx">
			<col>columnindex</col>
		</index>
		<index name="cvcolumnlist_cvid_idx">
			<col>cvid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_cvcolumnlist" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_cvcolumnlist FOREIGN KEY (cvid) REFERENCES vtiger_customview (cvid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_cvstdfilter">
		<field name="cvid" type="I" size="19">
			<key />
		</field>
		<field name="columnname" type="C" size="250">
			<default value="" />
		</field>
		<field name="stdfilter" type="C" size="250">
			<default value="" />
		</field>
		<field name="startdate" type="D">
			<default value="NULL" />
		</field>
		<field name="enddate" type="D">
			<default value="NULL" />
		</field>

		<index name="cvstdfilter_cvid_idx">
			<col>cvid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_cvstdfilter" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_cvstdfilter FOREIGN KEY (cvid) REFERENCES vtiger_customview (cvid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_cvadvfilter">
		<field name="cvid" type="I" size="19">
			<key />
		</field>
		<field name="columnindex" type="I" size="11">
			<key />
			<notnull />
		</field>
		<field name="columnname" type="C" size="250">
			<default value="" />
		</field>
		<field name="comparator" type="C" size="10">
			<default value="" />
		</field>
		<field name="value" type="C" size="200">
			<default value="" />
		</field>
		<field name="groupid" type="I">
			<default value="1" />
		</field>
		<field name="column_condition" type="C" size="255">
			<default value="and" />
		</field>

		<index name="cvadvfilter_cvid_idx">
			<col>cvid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_cvadvfilter" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_cvadvfilter FOREIGN KEY (cvid) REFERENCES vtiger_customview (cvid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_customaction">
		<field name="cvid" type="I" size="19">
			<notnull />
		</field>
		<field name="subject" type="C" size="250">
			<notnull />
		</field>
		<field name="module" type="C" size="50">
			<notnull />
		</field>
		<field name="content" type="X">
			<default value="" />
		</field>

		<index name="customaction_cvid_idx">
			<col>cvid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_customaction" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_customaction FOREIGN KEY (cvid) REFERENCES vtiger_customview (cvid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_selectquery">
		<field name="queryid" type="I" size="19">
			<key />
		</field>
		<field name="startindex" type="I" size="19">
			<default value="0" />
		</field>
		<field name="numofobjects" type="I" size="19">
			<default value="0" />
		</field>

		<index name="selectquery_queryid_idx">
			<col>queryid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_selectcolumn">
		<field name="queryid" type="I" size="19">
			<key />
		</field>
		<field name="columnindex" type="I" size="11">
			<key />
			<notnull />
			<default value="0" />
		</field>
		<field name="columnname" type="C" size="250">
			<default value="" />
		</field>

		<index name="selectcolumn_queryid_idx">
			<col>queryid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_selectcolumn" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_selectcolumn FOREIGN KEY (queryid) REFERENCES vtiger_selectquery (queryid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_relcriteria">
		<field name="queryid" type="I" size="19">
			<key />
		</field>
		<field name="columnindex" type="I" size="11">
			<key />
			<notnull />
		</field>
		<field name="columnname" type="C" size="250">
			<default value="" />
		</field>
		<field name="comparator" type="C" size="10">
			<default value="" />
		</field>
		<field name="value" type="C" size="200">
			<default value="" />
		</field>
		<field name="groupid" type="I" size="11">
			<default value="1" />
		</field>
		<field name="column_condition" type="C" size="256">
			<default value="and" />
		</field>

		<index name="relcriteria_queryid_idx">
			<col>queryid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_relcriteria" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_relcriteria FOREIGN KEY (queryid) REFERENCES vtiger_selectquery (queryid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_reportfolder">
		<field name="folderid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="foldername" type="C" size="100">
			<notnull />
			<default value="" />
		</field>
		<field name="description" type="C" size="250">
			<default value="" />
		</field>
		<field name="state" type="C" size="50">
			<default value="SAVED" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_report">
		<field name="reportid" type="I" size="19">
			<key />
		</field>
		<field name="folderid" type="I" size="19">
			<notnull />
		</field>
		<field name="reportname" type="C" size="100">
			<default value="" />
		</field>
		<field name="description" type="C" size="250">
			<default value="" />
		</field>
		<field name="reporttype" type="C" size="50">
			<default value="" />
		</field>
		<field name="queryid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="state" type="C" size="50">
			<default value="SAVED" />
		</field>
		<field name="customizable" type="I" size="1">
			<default value="1" />
		</field>
		<field name="category" type="I" size="11">
			<default value="1" />
		</field>
		<field name="owner" type="I" size="11">
			<default value="1" />
		</field>
		<field name="sharingtype" type="C" size="200">
			<default value="Private" />
		</field>

		<index name="report_queryid_idx">
			<col>queryid</col>
		</index>
		<index name="report_folderid_idx">
			<col>folderid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_report" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_report FOREIGN KEY (folderid) REFERENCES vtiger_reportfolder (folderid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_report FOREIGN KEY (queryid) REFERENCES vtiger_selectquery (queryid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_reportmodules">
		<field name="reportmodulesid" type="I" size="19">
			<key />
		</field>
		<field name="primarymodule" type="C" size="50">
			<notnull />
			<default value="" />
		</field>
		<field name="secondarymodules" type="C" size="250">
			<default value="" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_reportmodules" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_reportmodules FOREIGN KEY (reportmodulesid) REFERENCES vtiger_report (reportid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_reportsortcol">
		<field name="sortcolid" type="I" size="19">
			<key />
		</field>
		<field name="reportid" type="I" size="19">
			<key />
		</field>
		<field name="columnname" type="C" size="250">
			<default value="" />
		</field>
		<field name="sortorder" type="C" size="250">
			<default value="Asc" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_reportsortcol" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_reportsortcol FOREIGN KEY (reportid) REFERENCES vtiger_report (reportid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_reportdatefilter">
		<field name="datefilterid" type="I" size="19">
			<key />
		</field>
		<field name="datecolumnname" type="C" size="250">
			<default value="" />
		</field>
		<field name="datefilter" type="C" size="250">
			<default value="" />
		</field>
		<field name="startdate" type="D">
			<default value="NULL" />
		</field>
		<field name="enddate" type="D">
			<default value="NULL" />
		</field>

		<index name="reportdatefilter_datefilterid_idx">
			<col>datefilterid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_reportdatefilter" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_reportdatefilter FOREIGN KEY (datefilterid) REFERENCES vtiger_report (reportid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_reportsummary">
		<field name="reportsummaryid" type="I" size="19">
			<key />
		</field>
		<field name="summarytype" type="I" size="19">
			<key />
		</field>
		<field name="columnname" type="C" size="250">
			<key />
			<default value="" />
		</field>

		<index name="reportsummary_reportsummaryid_idx">
			<col>reportsummaryid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_reportsummary" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_reportsummary FOREIGN KEY (reportsummaryid) REFERENCES vtiger_report (reportid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_reporttype">
		<field name="reportid" type="I" size="10">
			<key />
		</field>
		<field name="data" type="X" />
	</table>
	<table name="vtiger_reporttype" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_reporttype FOREIGN KEY (reportid) REFERENCES vtiger_report (reportid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_usageunit">
		<field name="usageunitid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="usageunit" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="usageunit_usageunit_idx">
			<col>usageunit</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_glacct">
		<field name="glacctid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="glacct" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="glacct_glacct_idx">
			<col>glacct</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_quotestage">
		<field name="quotestageid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="quotestage" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="quotestage_quotestage_idx">
			<col>quotestage</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_invoicestatus">
		<field name="invoicestatusid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="invoicestatus" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="invoicestatus_invoiestatus_idx">
			<col>invoicestatus</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_postatus">
		<field name="postatusid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="postatus" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="postatus_postatus_idx">
			<col>postatus</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_sostatus">
		<field name="sostatusid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="sostatus" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="sostatus_sostatus_idx">
			<col>sostatus</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>

	</table>

	<table name="vtiger_carrier">
		<field name="carrierid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="carrier" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="carrier_carrier_idx">
			<col>carrier</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_taxclass">
		<field name="taxclassid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="taxclass" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>

		<index name="taxclass_carrier_idx">
			<col>taxclass</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_organizationdetails">
		<field name="organization_id" type="I" size="11">
			<key />
		</field>
		<field name="organizationname" type="C" size="60">
			<unique />
		</field>
		<field name="address" type="C" size="150" />
		<field name="city" type="C" size="100" />
		<field name="state" type="C" size="100" />
		<field name="country" type="C" size="100" />
		<field name="code" type="C" size="30" />
		<field name="phone" type="C" size="30" />
		<field name="fax" type="C" size="30" />
		<field name="website" type="C" size="100" />
		<field name="logoname" type="C" size="50" />
		<field name="logo" type="X" />
		<field name="vatid" type="C" size="100" /> 

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_recurringtype">
		<field name="recurringeventid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="recurringtype" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>

		<index name="recurringtype_status_idx">
			<col>recurringtype</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_recurringevents">
		<field name="recurringid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="activityid" type="I" size="19">
			<notnull />
		</field>
		<field name="recurringdate" type="D" />
		<field name="recurringtype" type="C" size="30" />
		<field name="recurringfreq" type="I" size="19" />
		<field name="recurringinfo" type="C" size="50" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_recurringevents" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_recurringevents FOREIGN KEY (activityid) REFERENCES vtiger_activity(activityid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_inventorynotification">
		<field name="notificationid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="notificationname" type="C" size="200" />
		<field name="notificationsubject" type="C" size="200" />
		<field name="notificationbody" type="X" />
		<field name="label" type="C" size="50" />
		<field name="status" type="C" size="30" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_inventory_tandc">
		<field name="id" type="I" size="19">
			<key />
		</field>
		<field name="type" type="C" size="30">
			<notnull />
		</field>
		<field name="tandc" type="X" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_convertleadmapping">
		<field name="cfmid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="leadfid" type="I" size="19">
			<notnull />
		</field>
		<field name="accountfid" type="I" size="19" />
		<field name="contactfid" type="I" size="19" />
		<field name="potentialfid" type="I" size="19" />
		<field name="editable" type="I" size="19">
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_actionmapping">
		<field name="actionid" type="I" size="19">
			<key />
		</field>
		<field name="actionname" type="C" size="200">
			<key />
		</field>
		<field name="securitycheck" type="I" size="19" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_org_share_action2tab">
		<field name="share_action_id" type="I" size="19">
			<key />
		</field>
		<field name="tabid" type="I" size="19">
			<key />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_org_share_action2tab" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_org_share_action2tab FOREIGN KEY (share_action_id) REFERENCES vtiger_org_share_action_mapping(share_action_id) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_org_share_action2tab FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_module_rel">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="tabid" type="I" size="19">
			<notnull />
		</field>
		<field name="relationtype" type="C" size="200">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<index name="idx_datashare_module_rel_tabid">
			<col>tabid</col>
		</index>
	</table>

	<table name="vtiger_datashare_module_rel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_module_rel FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_grp2grp">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="share_groupid" type="I" size="19">
		</field>
		<field name="to_groupid" type="I" size="19">
		</field>
		<field name="permission" type="I" size="19" />

		<index name="datashare_grp2grp_share_groupid_idx">
			<col>share_groupid</col>
		</index>
		<index name="datashare_grp2grp_to_groupid_idx">
			<col>to_groupid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_datashare_grp2grp" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_grp2grp FOREIGN KEY (shareid) REFERENCES vtiger_datashare_module_rel(shareid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_datashare_grp2grp FOREIGN KEY (share_groupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_datashare_grp2grp FOREIGN KEY (to_groupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_grp2role">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="share_groupid" type="I" size="19">
		</field>
		<field name="to_roleid" type="C" size="255">
		</field>
		<field name="permission" type="I" size="19" />

		<index name="idx_datashare_grp2role_share_groupid">
			<col>share_groupid</col>
		</index>
		<index name="idx_datashare_grp2role_to_roleid">
			<col>to_roleid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_datashare_grp2role" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_grp2role FOREIGN KEY (shareid) REFERENCES vtiger_datashare_module_rel(shareid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_datashare_grp2role FOREIGN KEY (share_groupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_datashare_grp2role FOREIGN KEY (to_roleid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_grp2rs">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="share_groupid" type="I" size="19">
		</field>
		<field name="to_roleandsubid" type="C" size="255">
		</field>
		<field name="permission" type="I" size="19" />

		<index name="datashare_grp2rs_share_groupid_idx">
			<col>share_groupid</col>
		</index>
		<index name="datashare_grp2rs_to_roleandsubid_idx">
			<col>to_roleandsubid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_datashare_grp2rs" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_grp2rs FOREIGN KEY (shareid) REFERENCES vtiger_datashare_module_rel(shareid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_datashare_grp2rs FOREIGN KEY (share_groupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_datashare_grp2rs FOREIGN KEY (to_roleandsubid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_role2group">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="share_roleid" type="C" size="255">
		</field>
		<field name="to_groupid" type="I" size="19">
		</field>
		<field name="permission" type="I" size="19" />

		<index name="idx_datashare_role2group_share_roleid">
			<col>share_roleid</col>
		</index>
		<index name="idx_datashare_role2group_to_groupid">
			<col>to_groupid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_datashare_role2group" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_role2group FOREIGN KEY (shareid) REFERENCES vtiger_datashare_module_rel(shareid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_datashare_role2group FOREIGN KEY (to_groupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_datashare_role2group FOREIGN KEY (share_roleid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_role2role">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="share_roleid" type="C" size="255">
		</field>
		<field name="to_roleid" type="C" size="255">
		</field>
		<field name="permission" type="I" size="19" />

		<index name="datashare_role2role_share_roleid_idx">
			<col>share_roleid</col>
		</index>
		<index name="datashare_role2role_to_roleid_idx">
			<col>to_roleid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_datashare_role2role" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_role2role FOREIGN KEY (shareid) REFERENCES vtiger_datashare_module_rel(shareid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_datashare_role2role FOREIGN KEY (share_roleid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_datashare_role2role FOREIGN KEY (to_roleid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_role2rs">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="share_roleid" type="C" size="255">
		</field>
		<field name="to_roleandsubid" type="C" size="255">
		</field>
		<field name="permission" type="I" size="19" />

		<index name="datashare_role2s_share_roleid_idx">
			<col>share_roleid</col>
		</index>
		<index name="datashare_role2s_to_roleandsubid_idx">
			<col>to_roleandsubid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_datashare_role2rs" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_role2rs FOREIGN KEY (shareid) REFERENCES vtiger_datashare_module_rel(shareid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_datashare_role2rs FOREIGN KEY (share_roleid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_datashare_role2rs FOREIGN KEY (to_roleandsubid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_rs2grp">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="share_roleandsubid" type="C" size="255">
		</field>
		<field name="to_groupid" type="I" size="19">
		</field>
		<field name="permission" type="I" size="19" />

		<index name="datashare_rs2grp_share_roleandsubid_idx">
			<col>share_roleandsubid</col>
		</index>
		<index name="datashare_rs2grp_to_groupid_idx">
			<col>to_groupid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_datashare_rs2grp" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_rs2grp FOREIGN KEY (shareid) REFERENCES vtiger_datashare_module_rel(shareid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_datashare_rs2grp FOREIGN KEY (to_groupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_datashare_rs2grp FOREIGN KEY (share_roleandsubid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_rs2role">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="share_roleandsubid" type="C" size="255">
		</field>
		<field name="to_roleid" type="C" size="255">
		</field>
		<field name="permission" type="I" size="19" />

		<index name="datashare_rs2role_share_roleandsubid_idx">
			<col>share_roleandsubid</col>
		</index>
		<index name="datashare_rs2role_to_roleid_idx">
			<col>to_roleid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_datashare_rs2role" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_rs2role FOREIGN KEY (shareid) REFERENCES vtiger_datashare_module_rel(shareid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_datashare_rs2role FOREIGN KEY (share_roleandsubid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_datashare_rs2role FOREIGN KEY (to_roleid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_rs2rs">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="share_roleandsubid" type="C" size="255">
		</field>
		<field name="to_roleandsubid" type="C" size="255">
		</field>
		<field name="permission" type="I" size="19" />

		<index name="datashare_rs2rs_share_roleandsubid_idx">
			<col>share_roleandsubid</col>
		</index>
		<index name="idx_datashare_rs2rs_to_roleandsubid_idx">
			<col>to_roleandsubid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_datashare_rs2rs" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_rs2rs FOREIGN KEY (shareid) REFERENCES vtiger_datashare_module_rel(shareid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_datashare_rs2rs FOREIGN KEY (share_roleandsubid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_datashare_rs2rs FOREIGN KEY (to_roleandsubid) REFERENCES vtiger_role(roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_relatedmodules">
		<field name="datashare_relatedmodule_id" type="I" size="19">
			<key />
		</field>
		<field name="tabid" type="I" size="19">
		</field>
		<field name="relatedto_tabid" type="I" size="19">
		</field>
		<index name="datashare_relatedmodules_tabid_idx">
			<col>tabid</col>
		</index>
		<index name="datashare_relatedmodules_relatedto_tabid_idx">
			<col>relatedto_tabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_datashare_relatedmodules" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_datashare_relatedmodules FOREIGN KEY (relatedto_tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_datashare_relatedmodules FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_datashare_relatedmodule_permission">
		<field name="shareid" type="I" size="19">
			<key />
		</field>
		<field name="datashare_relatedmodule_id" type="I" size="19">
			<key />
		</field>
		<field name="permission" type="I" size="19" />

		<index name="datashare_relatedmodule_permission_shareid_permissions_idx">
			<col>shareid</col>
			<col>permission</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_parenttab">
		<field name="parenttabid" type="I" size="19">
			<notnull />
			<key />
		</field>
		<field name="parenttab_label" type="C" size="100">
			<notnull />
		</field>
		<field name="sequence" type="I" size="10">
			<notnull />
		</field>
		<field name="visible" type="I" size="2">
			<notnull />
			<default value="0" />
		</field>
		<index name="parenttab_parenttabid_parenttabl_label_visible_idx">
			<col>parenttabid</col>
			<col>parenttab_label</col>
			<col>visible</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tmp_read_user_sharing_per">
		<field name="userid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="11">
			<key />
		</field>
		<field name="shareduserid" type="I" size="11">
			<key />
		</field>

		<index name="tmp_read_user_sharing_per_userid_shareduserid_idx">
			<col>userid</col>
			<col>shareduserid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tmp_read_user_sharing_per" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_tmp_read_user_sharing_per FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_tmp_read_user_sharing_per FOREIGN KEY (shareduserid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_tmp_read_user_sharing_per FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_tmp_read_group_sharing_per">
		<field name="userid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="11">
			<key />
		</field>
		<field name="sharedgroupid" type="I" size="11">
			<key />
		</field>

		<index name="tmp_read_group_sharing_per_userid_sharedgroupid_idx">
			<col>userid</col>
			<col>sharedgroupid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tmp_read_group_sharing_per" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_tmp_read_group_sharing_per FOREIGN KEY (sharedgroupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_tmp_read_group_sharing_per FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_tmp_read_group_sharing_per FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_tmp_write_user_sharing_per">
		<field name="userid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="11">
			<key />
		</field>
		<field name="shareduserid" type="I" size="11">
			<key />
		</field>

		<index name="tmp_write_user_sharing_per_userid_shareduserid_idx">
			<col>userid</col>
			<col>shareduserid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tmp_write_user_sharing_per" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_tmp_write_user_sharing_per FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_tmp_write_user_sharing_per FOREIGN KEY (shareduserid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_tmp_write_user_sharing_per FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_tmp_write_group_sharing_per">
		<field name="userid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="11">
			<key />
		</field>
		<field name="sharedgroupid" type="I" size="11">
			<key />
		</field>

		<index name="tmp_write_group_sharing_per_UK1">
			<col>userid</col>
			<col>sharedgroupid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tmp_write_group_sharing_per" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_tmp_write_group_sharing_per FOREIGN KEY (sharedgroupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_tmp_write_group_sharing_per FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_tmp_write_group_sharing_per FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_tmp_read_user_rel_sharing_per">
		<field name="userid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="11">
			<key />
		</field>
		<field name="relatedtabid" type="I" size="11">
			<key />
		</field>
		<field name="shareduserid" type="I" size="11">
			<key />
		</field>

		<index name="tmp_read_user_rel_sharing_per_userid_shared_reltabid_idx">
			<col>userid</col>
			<col>shareduserid</col>
			<col>relatedtabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tmp_read_user_rel_sharing_per" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_tmp_read_user_rel_sharing_per FOREIGN KEY (relatedtabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_tmp_read_user_rel_sharing_per FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_tmp_read_user_rel_sharing_per FOREIGN KEY (shareduserid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_4_vtiger_tmp_read_user_rel_sharing_per FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_tmp_read_group_rel_sharing_per">
		<field name="userid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="11">
			<key />
		</field>
		<field name="relatedtabid" type="I" size="11">
			<key />
		</field>
		<field name="sharedgroupid" type="I" size="11">
			<key />
		</field>

		<index name="tmp_read_group_rel_sharing_per_userid_sharedgroupid_tabid">
			<col>userid</col>
			<col>sharedgroupid</col>
			<col>tabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tmp_read_group_rel_sharing_per" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_tmp_read_group_rel_sharing_per FOREIGN KEY (sharedgroupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_tmp_read_group_rel_sharing_per FOREIGN KEY (relatedtabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_tmp_read_group_rel_sharing_per FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_4_vtiger_tmp_read_group_rel_sharing_per FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_tmp_write_user_rel_sharing_per">
		<field name="userid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="11">
			<key />
		</field>
		<field name="relatedtabid" type="I" size="11">
			<key />
		</field>
		<field name="shareduserid" type="I" size="11">
			<key />
		</field>

		<index name="tmp_write_user_rel_sharing_per_userid_sharduserid_tabid_idx">
			<col>userid</col>
			<col>shareduserid</col>
			<col>tabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tmp_write_user_rel_sharing_per" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_tmp_write_user_rel_sharing_per FOREIGN KEY (relatedtabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_tmp_write_user_rel_sharing_per FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_tmp_write_user_rel_sharing_per FOREIGN KEY (shareduserid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_4_vtiger_tmp_write_user_rel_sharing_per FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_tmp_write_group_rel_sharing_per">
		<field name="userid" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="11">
			<key />
		</field>
		<field name="relatedtabid" type="I" size="11">
			<key />
		</field>
		<field name="sharedgroupid" type="I" size="11">
			<key />
		</field>

		<index name="tmp_write_group_rel_sharing_per_userid_sharedgroupid_tabid_idx">
			<col>userid</col>
			<col>sharedgroupid</col>
			<col>tabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tmp_write_group_rel_sharing_per" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_tmp_write_group_rel_sharing_per FOREIGN KEY (sharedgroupid) REFERENCES vtiger_groups(groupid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_tmp_write_group_rel_sharing_per FOREIGN KEY (relatedtabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_3_vtiger_tmp_write_group_rel_sharing_per FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_4_vtiger_tmp_write_group_rel_sharing_per FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_parenttabrel">
		<field name="parenttabid" type="I" size="19">
			<notnull />
		</field>
		<field name="tabid" type="I" size="19">
			<notnull />
		</field>
		<field name="sequence" type="I" size="3">
			<notnull />
		</field>

		<index name="parenttabrel_tabid_parenttabid_idx">
			<col>tabid</col>
			<col>parenttabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_parenttabrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_parenttabrel FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_parenttabrel" alter="true">
		<constraint>ADD CONSTRAINT fk_2_vtiger_parenttabrel FOREIGN KEY (parenttabid) REFERENCES vtiger_parenttab(parenttabid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_campaigntype">
		<field name="campaigntypeid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="campaigntype" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="campaigntype_campaigntype_idx">
			<col>campaigntype</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_campaignstatus">
		<field name="campaignstatusid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="campaignstatus" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="campaignstatus_campaignstatus_idx">
			<col>campaignstatus</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_expectedresponse">
		<field name="expectedresponseid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="expectedresponse" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="sortorderid" type="I" size="11"></field>
		<index name="CampaignExpRes_UK01">
			<col>expectedresponse</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
	<table name="vtiger_picklist">
		<field name="picklistid" type="I" size="11">
			<key />
			<autoincrement />
		</field>
		<field name="name" type="C" size="200">
			<notnull />
		</field>
		<index name="picklist_name_idx">
			<col>name</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
	<table name="vtiger_role2picklist">
		<field name="roleid" type="C" size="255">
			<key />
		</field>
		<field name="picklistvalueid" type="I" size="11">
			<key />
		</field>
		<field name="picklistid" type="I" size="11">
			<key />
		</field>
		<field name="sortid" type="I" size="11" />

		<index name="role2picklist_roleid_picklistid_idx">
			<col>roleid</col>
			<col>picklistid</col>
			<col>picklistvalueid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
	<table name="vtiger_role2picklist" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_role2picklist FOREIGN KEY (roleid) REFERENCES vtiger_role (roleid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>
	<table name="vtiger_role2picklist" alter="true">
		<constraint>ADD CONSTRAINT fk_2_vtiger_role2picklist FOREIGN KEY (picklistid) REFERENCES vtiger_picklist (picklistid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>
	<table name="vtiger_portal">
		<field name="portalid" type="I" size="19">
			<key />
		</field>
		<field name="portalname" type="C" size="200">
			<notnull />
		</field>
		<field name="portalurl" type="C" size="255">
			<notnull />
		</field>
		<field name="sequence" type="I" size="3">
			<notnull />
		</field>
		<field name="setdefault" type="I" size="3">
			<notnull />
			<default value="0" />
		</field>
		<index name="portal_portalname_idx">
			<col>portalname</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_soapservice">
		<field name="id" type="I" size="19">
			<default value="NULL" />
		</field>
		<field name="type" type="C" size="25">
			<default value="NULL" />
		</field>
		<field name="sessionid" type="C" size="100">
			<default value="NULL" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_announcement">
		<field name="creatorid" type="I" size="19">
			<key />
		</field>
		<field name="announcement" type="X" />
		<field name="title" type="C" size="255" />
		<field name="time" type="T">
			<deftimestamp />
		</field>

		<index name="announcement_creatorid_idx">
			<col>creatorid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_freetags">
		<field name="id" type="I" size="19">
			<notnull />
			<key />
		</field>
		<field name="tag" type="C" size="50">
			<notnull />
			<default value="" />
		</field>
		<field name="raw_tag" type="C" size="50">
			<notnull />
			<default value="" />
		</field>
		<field name="visibility" type="C" size="100">
			<notnull />
			<default value="PRIVATE" />
		</field>
		<field name="owner" type="I" size="19">
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_freetagged_objects">
		<field name="tag_id" type="I" size="20">
			<notnull />
			<default value="0" />
			<key />
		</field>
		<field name="tagger_id" type="I" size="20">
			<notnull />
			<default value="0" />
			<key />
		</field>
		<field name="object_id" type="I" size="20">
			<notnull />
			<default value="0" />
			<key />
		</field>
		<field name="tagged_on" type="T">
			<deftimestamp />
			<notnull />
		</field>
		<field name="module" type="C" size="50">
			<default value="" />
			<notnull />
		</field>

		<index name="freetagged_objects_tag_id_tagger_id_object_id_idx">
			<col>tag_id</col>
			<col>tagger_id</col>
			<col>object_id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_emaildetails">
		<field name="emailid" type="I" size="19">
			<notnull />
			<key />
		</field>
		<field name="from_email" type="C" size="50">
			<notnull />
			<default value="" />
		</field>
		<field name="to_email" type="X">
			<notnull />
		</field>
		<field name="cc_email" type="X">
			<notnull />
		</field>
		<field name="bcc_email" type="X">
			<notnull />
		</field>
		<field name="assigned_user_email" type="C" size="50">
			<notnull />
			<default value="" />
		</field>
		<field name="idlists" type="X">
			<notnull />
			<default value="" />
		</field>
		<field name="email_flag" type="C" size="50">
			<notnull />
			<default value="" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_invitees">
		<field name="activityid" type="I" size="19">
			<KEY/>
		</field>
		<field name="inviteeid" type="I" size="19">
			<KEY/>
		</field>
		<field name="status" type="C" size="50" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_inventorytaxinfo">
		<field name="taxid" type="I" size="3">
			<KEY/>
			<notnull />
		</field>
		<field name="taxname" type="C" size="50">
		</field>
		<field name="taxlabel" type="C" size="50">
		</field>
		<field name="percentage" type="N" size="7.3">
		</field>
		<field name="deleted" type="I" size="1" />

		<index name="inventorytaxinfo_taxname_idx">
			<col>taxname</col>
		</index>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_producttaxrel">
		<field name="productid" type="I" size="11">
			<notnull />
		</field>
		<field name="taxid" type="I" size="3">
			<notnull />
		</field>
		<field name="taxpercentage" type="N" size="7.3">
		</field>
		<field name="regions" type="X" />

		<index name="producttaxrel_productid_idx">
			<col>productid</col>
		</index>
		<index name="producttaxrel_taxid_idx">
			<col>taxid</col>
		</index>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_status">
		<field name="statusid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="status" type="C" size="200">
			<notnull />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<field name="picklist_valueid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<index name="vtiger_status_status_idx">
			<col>source</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_activity_view">
		<field name="activity_viewid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="activity_view" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>

		<index name="vtiger_activity_view_status_idx">
			<col>source</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_lead_view">
		<field name="lead_viewid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="lead_view" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>

		<index name="vtiger_lead_viewstatus_idx">
			<col>source</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_date_format">
		<field name="date_formatid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="date_format" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>

		<index name="vtiger_date_format_status_idx">
			<col>source</col>
			<unique />
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_postatushistory">
		<field name="historyid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="purchaseorderid" type="I" size="19">
			<notnull />
		</field>
		<field name="vendorname" type="C" size="100" />
		<field name="total" type="N" />
		<field name="postatus" type="C" size="200" />
		<field name="lastmodified" type="T" />

		<index name="postatushistory_purchaseorderid_idx">
			<col>purchaseorderid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_postatushistory" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_postatushistory FOREIGN KEY (purchaseorderid) REFERENCES vtiger_purchaseorder(purchaseorderid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_sostatushistory">
		<field name="historyid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="salesorderid" type="I" size="19">
			<notnull />
		</field>
		<field name="accountname" type="C" size="100" />
		<field name="total" type="N" />
		<field name="sostatus" type="C" size="200" />
		<field name="lastmodified" type="T" />

		<index name="sostatushistory_salesorderid_idx">
			<col>salesorderid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_sostatushistory" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_sostatushistory FOREIGN KEY (salesorderid) REFERENCES vtiger_salesorder(salesorderid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_quotestagehistory">
		<field name="historyid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="quoteid" type="I" size="19">
			<notnull />
		</field>
		<field name="accountname" type="C" size="100" />
		<field name="total" type="N" />
		<field name="quotestage" type="C" size="200" />
		<field name="lastmodified" type="T" />

		<index name="quotestagehistory_quoteid_idx">
			<col>quoteid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_quotestagehistory" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_quotestagehistory FOREIGN KEY (quoteid) REFERENCES vtiger_quotes(quoteid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_invoicestatushistory">
		<field name="historyid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="invoiceid" type="I" size="19">
			<notnull />
		</field>
		<field name="accountname" type="C" size="100" />
		<field name="total" type="N" />
		<field name="invoicestatus" type="C" size="200" />
		<field name="lastmodified" type="T" />

		<index name="invoicestatushistory_invoiceid_idx">
			<col>invoiceid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_invoicestatushistory" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_invoicestatushistory FOREIGN KEY (invoiceid) REFERENCES vtiger_invoice(invoiceid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_audit_trial">
		<field name="auditid" type="I" size="19">
			<key />
		</field>
		<field name="userid" type="I" size="19" />
		<field name="module" type="C" size="255" />
		<field name="action" type="C" size="255" />
		<field name="recordid" type="C" size="20" />
		<field name="actiondate" type="T"/>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_inventoryproductrel">
		<field name="id" type="I" size="19" />
		<field name="productid" type="I" size="19" />
		<field name="sequence_no" type="I" size="4" />
		<field name="quantity" type="N" size="25.3" />
		<field name="listprice" type="N" size="25.3" />
		<field name="discount_percent" type="N" size="7.3" />
		<field name="discount_amount" type="N" size="25.3" />
		<field name="comment" type="X" />
		<field name="description" type="X" />
		<field name="incrementondel" type="I" size="11">
			<notnull />
			<default value="0" />
		</field>
		<field name="lineitem_id" type="I" size="11">
			<key />
			<autoincrement />
		</field>

		<index name="inventoryproductrel_id_idx">
			<col>id</col>
		</index>
		<index name="inventoryproductrel_productid_idx">
			<col>productid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_shippingtaxinfo">
		<field name="taxid" type="I" size="3">
			<KEY/>
			<notnull />
		</field>
		<field name="taxname" type="C" size="50">
		</field>
		<field name="taxlabel" type="C" size="50">
		</field>
		<field name="percentage" type="N" size="7.3">
		</field>
		<field name="deleted" type="I" size="1" />

		<index name="shippingtaxinfo_taxname_idx">
			<col>taxname</col>
		</index>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_inventoryshippingrel">
		<field name="id" type="I" size="19" />

		<index name="inventoryishippingrel_id_idx">
			<col>id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_salesmanattachmentsrel">
		<field name="smid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="attachmentsid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<index name="salesmanattachmentsrel_smid_idx">
			<col>smid</col>
		</index>
		<index name="salesmanattachmentsrel_attachmentsid_idx">
			<col>attachmentsid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_salesmanattachmentsrel" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_salesmanattachmentsrel FOREIGN KEY (smid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_salesmanattachmentsrel FOREIGN KEY (attachmentsid) REFERENCES vtiger_attachments(attachmentsid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>
	<table name="vtiger_entityname">
		<field name="tabid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="modulename" type="C" size="50">
			<notnull />
		</field>
		<field name="tablename" type="C" size="100">
			<notnull />
		</field>
		<field name="fieldname" type="C" size="150">
			<notnull />
		</field>
		<field name="entityidfield" type="C" size="150">
			<notnull />
		</field>
		<field name="entityidcolumn" type="C" size="150">
			<notnull />
		</field>
		<index name="entityname_tabid_idx">
			<col>tabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_entityname" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_entityname FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_version">
		<field name="id" type="I" size="11">
			<key />
			<autoincrement />
		</field>
		<field name="old_version" type="C" size="30" />
		<field name="current_version" type="C" size="30" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<!-- Database changes after 5.0.4 GA -->

	<table name="vtiger_relatedlists_rb">
		<field name="entityid" type="I" size="19" />
		<field name="action" type="C" size="50" />
		<field name="rel_table" type="C" size="200" />
		<field name="rel_column" type="C" size="200" />
		<field name="ref_column" type="C" size="200" />
		<field name="related_crm_ids" type="X" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_activity_reminder_popup">
		<field name="reminderid" type="I" size="19" >
			<key/>
			<autoincrement />
		</field>
		<field name="semodule" type="C" size="100" >
			<notnull />
		</field>
		<field name="recordid" type="I" size="19" >
			<notnull />
		</field>
		<field name="date_start" type="D" >
			<notnull />
		</field>
		<field name="time_start" type="C" size="100" >
			<notnull />
		</field>
		<field name="status" type="I" size="2" >
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_reminder_interval">
		<field name="reminder_intervalid" type="I" size="19" >
			<key/>
			<autoincrement />
		</field>
		<field name="reminder_interval" type="C" size="200" >
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19" >
			<notnull />
		</field>
		<field name="presence" type="I" size="1" >
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_user2mergefields">
		<field name="userid" type="I" size="11" />
		<field name="tabid" type="I" size="19" />
		<field name="fieldid" type="I" size="19" />
		<field name="visible" type="I" size="2" />
		<index name="userid_tabid_idx">
			<col>userid</col>
			<col>tabid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_productcurrencyrel">
		<field name="productid" type="I" size="11" >
			<notnull />
		</field>
		<field name="currencyid" type="I" size="11" >
			<notnull />
		</field>
		<field name="converted_price"  type="N" size="25.2" />
		<field name="actual_price"  type="N" size="25.2" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_attachmentsfolder">
		<field name="folderid" type="I" size="19">
			<key />
			<notnull />
			<autoincrement />
		</field>
		<field name="foldername" type="C" size="200">
			<notnull />
		</field>
		<field name="description" type="C" size="250" />
		<field name="createdby" type="I" size="19">
			<notnull />
		</field>
		<field name="sequence" type="I" size="19">
			<default value="NULL" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_homestuff">
		<field name="stuffid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="stuffsequence" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="stufftype" type="C" size="100" />
		<field name="userid" type="I" size="19">
			<notnull />
		</field>
		<field name="visible" type="I" size="10">
			<notnull />
			<default value="0" />
		</field>
		<field name="stufftitle" type="C" size="100" />
		<index name="stuff_stuffid_idx">
			<col>stuffid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
	<table name="vtiger_homestuff" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_homestuff FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_homemodule">
		<field name="stuffid" type="I" size="19">
			<key />
		</field>
		<field name="modulename" type="C" size="100" />
		<field name="maxentries" type="I" size="19" >
			<notnull />
		</field>
		<field name="customviewid" type="I" size="19">
			<notnull />
		</field>
		<field name="setype" type="C" size="30">
			<notnull />
		</field>
		<index name="stuff_stuffid_idx">
			<col>stuffid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_homemodule" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_homemodule FOREIGN KEY (stuffid) REFERENCES vtiger_homestuff(stuffid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_homemoduleflds">
		<field name="stuffid" type="I" size="19">
		</field>
		<field name="fieldname" type="C" size="100" />
		<index name="stuff_stuffid_idx">
			<col>stuffid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_homemoduleflds" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_homemoduleflds FOREIGN KEY (stuffid) REFERENCES vtiger_homemodule(stuffid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_homerss">
		<field name="stuffid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="url" type="C" size="100" />
		<field name="maxentries" type="I" size="19" >
			<notnull />
		</field>
		<index name="stuff_stuffid_idx">
			<col>stuffid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_homerss" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_homerss FOREIGN KEY (stuffid) REFERENCES vtiger_homestuff(stuffid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_homedashbd">
		<field name="stuffid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="dashbdname" type="C" size="100" />
		<field name="dashbdtype" type="C" size="100" />
		<index name="stuff_stuffid_idx">
			<col>stuffid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_homedashbd" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_homedashbd FOREIGN KEY (stuffid) REFERENCES vtiger_homestuff(stuffid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_homedefault">
		<field name="stuffid" type="I" size="19">
			<key />
			<default value="0" />
		</field>
		<field name="hometype" type="C" size="30">
			<notnull />
		</field>
		<field name="maxentries" type="I" size="19">
		</field>
		<field name="setype" type="C" size="30">
		</field>
		<index name="stuff_stuffid_idx">
			<col>stuffid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_homedefault" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_homedefault FOREIGN KEY (stuffid) REFERENCES vtiger_homestuff(stuffid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>
	<!-- Webservices -->
	<table name="vtiger_ws_fieldtype">
		<field name="fieldtypeid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="uitype" type="C" size="30">
			<notnull />
		</field>
		<field name="fieldtype" type="C" size="200">
			<notnull />
		</field>
		<index name="uitype_idx">
			<unique />
			<col>uitype</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ws_referencetype">
		<field name="fieldtypeid" type="I" size="19">
			<key />
			<notnull />
		</field>
		<field name="type" type="C" size="25">
			<key />
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ws_referencetype" alter="true">
		<constraint>ADD CONSTRAINT `fk_1_vtiger_referencetype` FOREIGN KEY (`fieldtypeid`) REFERENCES `vtiger_ws_fieldtype` (`fieldtypeid`) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_ws_userauthtoken">
		<field name="userid" type="I" size="19">
			<key />
		</field>
		<field name="token" type="C" size="36">
			<notnull />
		</field>
		<field name="expiretime" type="I" size="19">
			<key />
			<notnull />
		</field>
		<index name="userid_idx">
			<unique />
			<col>userid</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ws_entity">
		<field name="id" type="I" size="11">
			<key />
			<notnull />
			<autoincrement />
		</field>
		<field name="name" type="C" size="25">
			<notnull />
			<unique />
		</field>
		<field name="handler_path" type="C" size="255">
			<notnull />
		</field>
		<field name="handler_class" type="C" size="64">
			<notnull />
		</field>
		<field name="ismodule" type="I" size="3">
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ws_entity_tables">
		<field name="webservice_entity_id" type="I" size="11">
			<key />
			<notnull />
		</field>
		<field name="table_name" type="C" size="50">
			<notnull />
			<key />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ws_entity_tables" alter="true">
		<constraint>ADD CONSTRAINT `fk_1_vtiger_ws_actor_tables` FOREIGN KEY (`webservice_entity_id`) REFERENCES `vtiger_ws_entity` (`id`) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_ws_entity_fieldtype">
		<field name="fieldtypeid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="table_name" type="C" size="50">
			<notnull />
		</field>
		<field name="field_name" type="C" size="50">
			<notnull />
		</field>
		<field name="fieldtype" type="C" size="200">
			<notnull />
		</field>
		<index name="vtiger_idx_1_tablename_fieldname">
			<unique />
			<col>table_name</col>
			<col>field_name</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ws_entity_referencetype">
		<field name="fieldtypeid" type="I" size="19">
			<key />
			<notnull />
		</field>
		<field name="type" type="C" size="25">
			<key />
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ws_entity_referencetype" alter="true">
		<constraint>ADD CONSTRAINT `vtiger_fk_1_actors_referencetype` FOREIGN KEY (`fieldtypeid`) REFERENCES `vtiger_ws_entity_fieldtype` (`fieldtypeid`) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>

	<table name="vtiger_ws_operation">
		<field name="operationid" type="I" size="11">
			<key />
			<notnull />
			<autoincrement />
		</field>
		<field name="name" type="C" size="128">
			<notnull />
			<unique />
		</field>
		<field name="handler_path" type="C" size="255">
			<notnull />
		</field>
		<field name="handler_method" type="C" size="64">
			<notnull />
		</field>
		<field name="type" type="C" size="8">
			<notnull />
		</field>
		<field name="prelogin" type="I" size="3">
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ws_operation_parameters">
		<field name="operationid" type="I" size="11">
			<key />
			<notnull />
			<autoincrement />
		</field>
		<field name="name" type="C" size="128">
			<key />
			<notnull />
		</field>
		<field name="type" type="C" size="64">
			<notnull />
		</field>
		<field name="sequence" type="I" size="11">
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ws_entity_name">
		<field name="entity_id" type="I" size="11">
			<key />
		</field>
		<field name="name_fields" type="C" size="50">
			<notnull />
		</field>
		<field name="index_field" type="C" size="50">
			<notnull />
		</field>
		<field name="table_name" type="C" size="50">
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
	<!-- END -->

	<table name="vtiger_eventhandlers">
		<field name="eventhandler_id" type="I" >
			<key />
			<autoincrement />
		</field>
		<field name="event_name" type="C" size="100">
			<key />
		</field>
		<field name="handler_path" type="C" size="400">
			<notnull />
		</field>
		<field name="handler_class" type="C" size="100">
			<key />
			<notnull />
		</field>
		<field name="cond" type="X">
			<notnull />
		</field>
		<field name="is_active" type="I" size="1">
			<notnull />
		</field>
		<field name="dependent_on" type="C" size="255">
			<default value="[]" />
		</field>
		<index name="eventhandler_idx">
			<unique />
			<col>eventhandler_id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_eventhandler_module">
		<field name="eventhandler_module_id" type="I">
			<key />
			<autoincrement />
		</field>
		<field name="module_name" type="C" size="100">
		</field>
		<field name="handler_class" type="C" size="100">
		</field>
		<index name="eventhandler_idx">
			<unique />
			<col>eventhandler_id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<!-- vtmailscanner -->
	<table name="vtiger_mailscanner">
		<field name="scannerid" type="I">
			<key />
			<autoincrement />
		</field>
		<field name="scannername" type="C" size="30"></field>
		<field name="server" type="C" size="100"></field>
		<field name="protocol" type="C" size="10"></field>
		<field name="username" type="C" size="255"></field>
		<field name="password" type="C" size="255"></field>
		<field name="ssltype" type="C" size="10"></field>
		<field name="sslmethod" type="C" size="30"></field>
		<field name="connecturl" type="C" size="255"></field>
		<field name="searchfor" type="C" size="10"></field>
		<field name="markas" type="C" size="10"></field>
		<field name="isvalid" type="I" size="1"></field>
		<field name="scanfrom" type="C" size="10">
			<default value="ALL" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_mailscanner_ids">
		<field name="scannerid" type="I"></field>
		<field name="messageid" type="X"></field>
		<field name="crmid" type="I"></field>
		<field name="refids" type="X"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_mailscanner_folders">
		<field name="folderid" type="I">
			<key />
			<autoincrement/>
		</field>
		<field name="scannerid" type="I"></field>
		<field name="foldername" type="C" size="255"></field>
		<field name="lastscan" type="C" size="30"></field>
		<field name="rescan" type="I" size="1"></field>
		<field name="enabled" type="I" size="1"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_mailscanner_rules">
		<field name="ruleid" type="I">
			<key />
			<autoincrement/>
		</field>
		<field name="scannerid" type="I"></field>
		<field name="fromaddress" type="C" size="255"></field>
		<field name="toaddress" type="C" size="255"></field>
		<field name="subjectop" type="C" size="20"></field>
		<field name="subject" type="C" size="255"></field>
		<field name="bodyop" type="C" size="20"></field>
		<field name="body" type="C" size="255"></field>
		<field name="matchusing" type="C" size="5"></field>
		<field name="sequence" type="I"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_mailscanner_actions">
		<field name="actionid" type="I">
			<key />
			<autoincrement/>
		</field>
		<field name="scannerid" type="I"></field>
		<field name="actiontype" type="C" size="10"></field>
		<field name="module" type="C" size="30"></field>
		<field name="lookup" type="C" size="30"></field>
		<field name="sequence" type="I"></field>
		<field name="actiontype" type="C" size="10"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_mailscanner_ruleactions">
		<field name="ruleid" type="I"></field>
		<field name="actionid" type="I"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
	<!-- END -->

	<!-- Recurring Invoice -->
	<table name="vtiger_invoice_recurring_info">
		<field name="salesorderid" type="I"></field>
		<field name="recurring_frequency" type="C" size="200"></field>
		<field name="start_period" type="D"></field>
		<field name="end_period" type="D"></field>
		<field name="last_recurring_date" type="D">
			<default value="NULL" />
		</field>
		<field name="payment_duration" type="C" size="200"></field>
		<field name="invoice_status" type="C" size="200"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_recurring_frequency">
		<field name="recurring_frequency_id" type="I"></field>
		<field name="recurring_frequency" type="C" size="200"></field>
		<field name="sortorderid" type="I"></field>
		<field name="presence" type="I"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_payment_duration">
		<field name="payment_duration_id" type="I"></field>
		<field name="payment_duration" type="C" size="200"></field>
		<field name="sortorderid" type="I"></field>
		<field name="presence" type="I"></field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
	<!-- END -->

	<!-- com_vtiger_workflow -->
	<table name="com_vtiger_workflows">
		<field name="workflow_id" type="I" >
			<key />
			<autoincrement />
		</field>
		<field name="module_name" type="C" size="100">
		</field>
		<field name="summary" type="C" size="400">
			<notnull />
		</field>
		<field name="test" type="X">
			<notnull />
		</field>
		<field name="execution_condition" type="I">
			<notnull />
		</field>
		<field name="defaultworkflow" type="I" size="1">
		</field>
		<field name="type" type="C" size="255">
		</field>
		<field name="filtersavedinnew" type="I" size="1">
		</field>
		<field name="schtypeid" type="I" size="10">
		</field>
		<field name="schdayofmonth" type="C" size="100">
		</field>
		<field name="schdayofweek" type="C" size="100">
		</field>
		<field name="schannualdates" type="C" size="500">
		</field>
		<field name="schtime" type="C" size="50">
		</field>
		<field name="nexttrigger_time" type="T">
		</field>
		<field name="status" type="I" size="1" />
		<field name="workflowname" type="C" size="100" />

		<index name="com_vtiger_workflows_idx">
			<unique />
			<col>workflow_id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="com_vtiger_workflow_activatedonce">
		<field name="workflow_id" type="I" >
			<key />
		</field>
		<field name="entity_id" type="I" >
			<key />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>


	<table name="com_vtiger_workflowtasks">
		<field name="task_id" type="I" >
			<key />
			<autoincrement />
		</field>
		<field name="workflow_id" type="I" >
		</field>
		<field name="summary" type="C" size="400">
			<notnull />
		</field>
		<field name="task" type="X">
			<notnull />
		</field>
		<index name="com_vtiger_workflowtasks_idx">
			<unique />
			<col>task_id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="com_vtiger_workflowtask_queue">
		<field name="task_id" type="I" >
		</field>
		<field name="entity_id" type="C" size="100" >
		</field>
		<field name="do_after" type="I" >
		</field>
		<field name="relatedinfo" type="C" size="255" />
		
		<index name="com_vtiger_workflowtask_queue_idx">
			<unique />
			<col>task_id</col>
			<col>entity_id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="com_vtiger_workflowtasks_entitymethod">
		<field name="workflowtasks_entitymethod_id" type="I" >
			<key />
		</field>
		<field name="module_name" type="C" size="100" >
		</field>
		<field name="method_name" type="C" size="100" >
		</field>
		<field name="function_path" type="C" size="400" >
		</field>
		<field name="function_name" type="C" size="100" >
		</field>
		<index name="com_vtiger_workflowtasks_entitymethod_idx">
			<unique />
			<col>workflowtasks_entitymethod_id</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="com_vtiger_workflowtemplates">
		<field name="template_id" type="I" >
			<key />
			<autoincrement />
		</field>
		<field name="module_name" type="C" size="100" >
		</field>
		<field name="title" type="C" size="400" >
		</field>
		<field name="template" type="X">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="com_vtiger_workflow_tasktypes">
		<field name="id" type="I" size="11">
			<notnull />
		</field>
		<field name="tasktypename" type="C" size="255" >
			<notnull />
		</field>
		<field name="label" type="C" size="255" >
		</field>
		<field name="classname" type="C" size="255">
		</field>
		<field name="classpath" type="C" size="255">
		</field>
		<field name="templatepath" type="C" size="255">
		</field>
		<field name="modules" type="C" size="500">
		</field>
		<field name="sourcemodule" type="C" size="255">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
	<!-- com_vtiger_workflow ends-->

	<!--  asterisk integration starts -->
	<table name="vtiger_asteriskextensions">
		<field name="userid" type="I" size="11">
		</field>
		<field name="asterisk_extension" type="C" size="50">
		</field>
		<field name="use_asterisk" type="C" size="3">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_asterisk">
		<field name="server" type="C" size="30">
		</field>
		<field name="port" type="C" size="30">
		</field>
		<field name="username" type="C" size="50">
		</field>
		<field name="password" type="C" size="50">
		</field>
		<field name="version" type="C" size="50">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_asteriskincomingcalls">
		<field name="from_number" type="C" size="50">
		</field>
		<field name="from_name" type="C" size="50">
		</field>
		<field name="to_number" type="C" size="50">
		</field>
		<field name="callertype" type="C" size="30">
		</field>
		<field name="flag" type="I" size="19">
		</field>
		<field name="timer" type="I" size="19">
		</field>
		<field name="refuid" type="C" size="255">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_asteriskincomingevents">
		<field name="uid" type="C" size="255">
			<key />
		</field>
		<field name="channel" type="C" size="100">
		</field>
		<field name="from_number" type="I8" size="20">
		</field>
		<field name="from_name" type="C" size="100">
		</field>
		<field name="to_number" type="I8" size="20">
		</field>
		<field name="callertype" type="C" size="100">
		</field>
		<field name="timer" type="I" size="20">
		</field>
		<field name="flag" type="C" size="3">
		</field>
		<field name="pbxrecordid" type="I" size="19">
		</field>
		<field name="relcrmid" type="I" size="19">
		</field>
	</table>
	<!--  asterisk integration ends -->

	<table name="vtiger_modentity_num">
		<field name="num_id" type="I" size="19">
			<key />
			<notnull />
		</field>
		<field name="semodule" type="C" size="50">
			<notnull />
		</field>
		<field name="prefix" type="C" size="50">
			<notnull />
			<default value="" />
		</field>
		<field name="start_id" type="C" size="50">
			<notnull />
		</field>
		<field name="cur_id" type="C" size="50">
			<notnull />
		</field>
		<field name="active" type="C" size="2">
			<notnull />
		</field>
		<index name="num_idx" type="C" size="50">
			<unique />
			<col>num_id</col>
		</index>
		<index name="semodule_active_idx">
			<col>semodule</col>
			<col>active</col>
		</index>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<!-- Table added to store languages -->
	<table name="vtiger_language">
		<field name="id" type="I">
			<key />
			<autoincrement />
			<notnull />
		</field>
		<field name="name" type="C" size="50">
		</field>
		<field name="prefix" type="C" size="10">
		</field>
		<field name="label" type="C" size="30">
		</field>
		<field name="lastupdated" type="T">
		</field>
		<field name="sequence" type="I">
		</field>
		<field name="isdefault" type="I" size="1">
		</field>
		<field name="active" type="I" size="1">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_crmentityrel">
		<field name="crmid" type="I">
			<notnull />
		</field>
		<field name="module" type="C" size="100">
			<notnull />
		</field>
		<field name="relcrmid" type="I">
			<notnull />
		</field>
		<field name="relmodule" type="C" size="100">
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_fieldmodulerel">
		<field name="fieldid" type="I">
			<notnull />
		</field>
		<field name="module" type="C" size="100">
			<notnull />
		</field>
		<field name="relmodule" type="C" size="100">
			<notnull />
		</field>
		<field name="status" type="C" size="10">
			<default value="NULL" />
		</field>
		<field name="sequence" type="I">
			<default value="NULL" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_links">
		<field name="linkid" type="I">
			<notnull />
			<key />
		</field>
		<field name="tabid" type="I" />
		<field name="linktype" type="C" size="50" />
		<field name="linklabel" type="C" size="50" />
		<field name="linkurl" type="C" size="255" />
		<field name="linkicon" type="C" size="100" />
		<field name="sequence" type="I" />
		<field name="handler_path" type="C" size="128">
			<default value="NULL" />
		</field>
		<field name="handler_class" type="C" size="50">
			<default value="NULL" />
		</field>
		<field name="handler" type="C" size="50">
			<default value="NULL" />
		</field>
		<field name="parent_link" type="I" size="19" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<index name="link_tabidtype_idx">
			<col>tabid</col>
			<col>linktype</col>
		</index>
	</table>

	<!-- move settings page to database starts -->
	<table name="vtiger_settings_blocks">
		<field name="blockid" type="I" size="19">
			<key />
			<notnull />
		</field>
		<field name="label" type="C" size="250">
		</field>
		<field name="sequence" type="I" size="19">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_settings_field">
		<field name="fieldid" type="I" size="19">
			<key />
			<notnull />
		</field>
		<field name="blockid" type="I" size="19">
		</field>
		<field name="name" type="C" size="250">
		</field>
		<field name="iconpath" type="C" size="300">
		</field>
		<field name="description" type="X">
		</field>
		<field name="linkto" type="X">
		</field>
		<field name="sequence" type="I" size="19">
		</field>
		<field name="active" type="I" size="19">
			<default value="0"/>
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_settings_field" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_settings_field FOREIGN KEY (blockid) REFERENCES vtiger_settings_blocks(blockid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>
	<!-- move settings page to database ends -->

	<!-- Added for Email Status Tracking -->
	<table name="vtiger_email_access">
		<field name="crmid" type="I" />
		<field name="mailid" type="I" />
		<field name="accessdate" type="D" />
		<field name="accesstime" type="T" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_email_track">
		<field name="crmid" type="I" />
		<field name="mailid" type="I" />
		<field name="access_count" type="I" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<index name="link_tabidtype_idx">
			<col>crmid</col>
			<col>mailid</col>
			<unique />
		</index>
	</table>

	<table name="vtiger_reportsharing">
		<field name="reportid" type="I" size="19">
			<notnull />
		</field>
		<field name="shareid" type="I" size="19">
			<notnull />
		</field>
		<field name="setype" type="C" size="200">
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_reportfilters">
		<field name="filterid" type="I" size="19">
			<notnull />
		</field>
		<field name="name" type="C" size="200">
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<!-- notebook changes start -->
	<table name="vtiger_notebook_contents">
		<field name="userid" type="I" size="19">
			<notnull />
		</field>
		<field name="notebookid" type="I" size="19">
			<notnull />
		</field>
		<field name="contents" type="X">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
	<!-- notebook changes end -->

	<table name="vtiger_inventorysubproductrel">
		<field name="id" type="I" size="19">
			<notnull />
		</field>
		<field name="sequence_no" type="I" size="10">
			<notnull />
		</field>
		<field name="productid" type="I" size="19">
			<notnull />
		</field>
		<field name="quantity" type="I" size="19">
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_activitycf">
		<field name="activityid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_user_module_preferences">
		<field name="userid" type="I" size="19">
			<key />
		</field>
		<field name="tabid" type="I" size="19">
			<key />
		</field>
		<field name="default_cvid" type="I" size="19">
			<notnull />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_user_module_preferences" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_user_module_preferences FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE ON UPDATE CASCADE</constraint>
		<constraint>ADD CONSTRAINT fk_2_vtiger_user_module_preferences FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE ON UPDATE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
		<data>
		</data>
	</table>
	<!-- home page changes start -->
	<table name="vtiger_home_layout">
		<field name="userid" type="I" size="19">
			<key />
		</field>
		<field name="layout" type="I" size="19">
			<notnull />
			<default value="4" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<!-- home page changes end -->
	<table name="vtiger_notescf">
		<field name="notesid" type="I" size="19">
			<key />
			<default value="0" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_currencies">
		<field name="currencyid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="currency_name" type="C" size="200" />
		<field name="currency_code" type="C" size="50" />
		<field name="currency_symbol" type="C" size="11" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tab_info">
		<field name="tabid" type="I" size="19" />
		<field name="prefname" type="C" size="256" />
		<field name="prefvalue" type="C" size="256" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_tab_info" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_tab_info FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE ON UPDATE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_campaignrelstatus">
		<field name="campaignrelstatusid" type="I" size="19" />
		<field name="campaignrelstatus" type="C" size="256" />
		<field name="sortorderid" type="I" size="19" />
		<field name="presence" type="I" size="19" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_campaignaccountrel">
		<field name="campaignid" type="I" size="19" />
		<field name="accountid" type="I" size="19" />
		<field name="campaignrelstatusid" type="I" size="19" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_customerportal_tabs">
		<field name="tabid" type="I" size="19">
			<notnull />
			<key />
		</field>
		<field name="visible" type="I" size="1">
			<default value="1" />
		</field>
		<field name="sequence" type="I" size="1">
			<default value="NULL" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_customerportal_prefs">
		<field name="tabid" type="I" size="19">
			<notnull />
			<key />
		</field>
		<field name="prefkey" type="C" size="100">
			<notnull />
			<key />
		</field>
		<field name="prefvalue" type="I" size="20">
			<default value="NULL" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_relcriteria_grouping">
		<field name="groupid" type="I" size="11">
			<key />
			<notnull />
		</field>
		<field name="queryid" type="I" size="19">
			<key />
		</field>
		<field name="group_condition" type="C" size="256">
		</field>
		<field name="condition_expression" type="X">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_scheduled_reports">
		<field name="reportid" type="I">
			<key />
		</field>
		<field name="recipients" type="X">
		</field>
		<field name="schedule" type="X">
		</field>
		<field name="format" type="C" size="10">
		</field>
		<field name="next_trigger_time" type="TS">
			<DEFTIMESTAMP/>
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_ws_fieldinfo">
		<field name="id" type="C" size="64">
			<notnull />
			<key />
		</field>
		<field name="property_name" type="C" size="32">
		</field>
		<field name="property_value" type="C" size="64">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_cvadvfilter_grouping">
		<field name="groupid" type="I" size="11">
			<key />
			<notnull />
		</field>
		<field name="cvid" type="I" size="19">
			<key />
		</field>
		<field name="group_condition" type="C" size="255">
		</field>
		<field name="condition_expression" type="X">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_picklist_dependency">
		<field name="id" type="I" size="11">
			<key />
		</field>
		<field name="tabid" type="I" size="19">
			<notnull />
		</field>
		<field name="sourcefield" type="C" size="255">
		</field>
		<field name="targetfield" type="C" size="255">
		</field>
		<field name="sourcevalue" type="C" size="100">
		</field>
		<field name="targetvalues" type="X">
		</field>
		<field name="criteria" type="X">
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_currency_grouping_pattern">
		<field name="currency_grouping_patternid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="currency_grouping_pattern" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_currency_decimal_separator">
		<field name="currency_decimal_separatorid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="currency_decimal_separator" type="C" size="2">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_currency_grouping_separator">
		<field name="currency_grouping_separatorid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="currency_grouping_separator" type="C" size="2">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_currency_symbol_placement">
		<field name="currency_symbol_placementid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="currency_symbol_placement" type="C" size="30">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_time_zone">
		<field name="time_zoneid" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="time_zone" type="C" size="200">
			<notnull />
		</field>
		<field name="sortorderid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="presence" type="I" size="1">
			<notnull />
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_homereportchart">
		<field name="stuffid" type="I" size="11">
			<key />
		</field>
		<field name="reportid" type="I" size="19" />
		<field name="reportcharttype" type="C" size="100" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_reportgroupbycolumn">
		<field name="reportid" type="I" size="19" />
		<field name="sortid" type="I" size="19" />
		<field name="sortcolname" type="C" size="250" />
		<field name="dategroupbycriteria" type="C" size="250" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_reportgroupbycolumn" alter="true">
		<constraint>ADD CONSTRAINT fk_1_vtiger_reportgroupbycolumn FOREIGN KEY (reportid) REFERENCES vtiger_report(reportid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_crmsetup">
		<field name="userid" type="I" size="11" />
		<field name="setup_status" type="I" size="2" />
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_feedback">
		<field name="userid" type="I" size="19">
		</field>
		<field name="dontshow" type="C" size="19" >
			<default value="false" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_shareduserinfo">
		<field name="userid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="shareduserid" type="I" size="19">
			<notnull />
			<default value="0" />
		</field>
		<field name="color" type="C" size="50" />
		<field name="visible" type="I" size="19" >
			<default value="1" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_calendar_default_activitytypes">
		<field name="id" type="I" size="19">
			<key />
		</field>
		<field name="module" type="C" size="50" />
		<field name="fieldname" type="C" size="50" />
		<field name="defaultcolor" type="C" size="50" />
		<field name="isdefault" type="I" size="11" >
			<default value="1" />
		</field>
		<field name="conditions" type="C" size="255" />

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_calendar_user_activitytypes">
		<field name="id" type="I" size="19">
			<key />
		</field>
		<field name="defaultid" type="I" size="19" />
		<field name="userid" type="I" size="19" />
		<field name="color" type="C" size="50" />
		<field name="visible" type="I" size="19" >
			<default value="1" />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_dashboard_tabs">
		<field name="id" type="I" size="19">
			<key />
			<autoincrement />
		</field>
		<field name="tabname" type="C" size="50" >
			<unique />
		</field>
		<field name="isdefault" type="I" size="1" >
			<default value="0" />
		</field>
		<field name="sequence" type="I" size="5" >
			<default value="2" />
		</field>
		<field name="appname" type="C" size="20" />
		<field name="modulename" type="C" size="50" />
		<field name="userid" type="I" size="11">
			<unique />
		</field>

		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_dashboard_tabs" alter="true">
		<constraint>ADD CONSTRAINT vtiger_dashboard_tabs_ibfk_1 FOREIGN KEY (userid) REFERENCES vtiger_users(id) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
	
	<table name="vtiger_dashboard_tabs" alter="true">
		<constraint>ADD CONSTRAINT tabname UNIQUE(tabname, userid)</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_app2tab">
		<field name="tabid" type="I" size="11" />
		<field name="appname" type="C" size="20" />
		<field name="sequence" type="I" size="19" />
		<field name="visible" type="I" size="3" >
			<default value="1" />
		</field>
		<opt platform="mysql">Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>

	<table name="vtiger_app2tab" alter="true">
		<constraint>ADD CONSTRAINT vtiger_app2tab_fk_tab FOREIGN KEY (tabid) REFERENCES vtiger_tab(tabid) ON DELETE CASCADE</constraint>
		<opt>Engine=InnoDB DEFAULT CHARSET utf8</opt>
	</table>
</schema>
