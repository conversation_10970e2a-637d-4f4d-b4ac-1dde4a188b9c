<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
    'Project'                      => 'Progetto'                    , 
	'SINGLE_Project'               => 'Progetto'                    , 
	'LBL_ADD_RECORD'               => 'Aggiungi progetto'                 , 
	'LBL_RECORDS_LIST'             => 'Lista Progetti'               , 
	'LBL_PROJECT_INFORMATION'      => 'Informationi Progetto'       , 
	'Project Name'                 => 'Nome Progetto'               , 
	'Start Date'                   => 'Data di Inizio'              , 
	'Target End Date'              => 'Data finale obiettivo'             , 
	'Actual End Date'              => 'Data finale effettiva'             , 
	'Project No'                   => 'N. Progetto'              , 
	'Target Budget'                => 'Budget obiettivo'               , 
	'Project Url'                  => 'Url Progetto'                 , 
	'Progress'                     => 'Progresso'                    , 
        'Type'                         => 'Tipo'                        , 
    'Related to'                   => 'Relativi a'                  ,
	'LBL_TASKS_OPEN'               => 'Attività aperte'                  , 
	'LBL_TASKS_DUE'                => 'Attività Scadute'                   , 
	'LBL_TASKS_COMPLETED'          => 'Attività Completate'             , 
	'LBL_PEOPLE'                   => 'Persone'                      , 
	'LBL_CHARTS'                   => 'Diagrammi'                      , 
	'LBL_TASKS_LIST'               => 'Lista Attività'                  ,
	'LBL_MILESTONES'               => 'Stadio'                  , 
	'LBL_TASKS'                    => 'Attività'                       ,  
	'LBL_STATUS_IS'                => 'Stato è'                   ,  
	'LBL_STATUS'                   => 'Stato'                      , 
	'LBL_TICKET_PRIORITY'          => 'Priorità'                    ,  
	'LBL_DOWNLOAD_FILE'            => 'Scarica File'               ,  
    'LBL_MILESTONES_LIST'          => 'Lista Stadi'            ,
    'LBL_TASKS_HIGH'               => 'Alta priorità'               ,
    'LBL_TASKS_NORMAL'             => 'Priorità normale'            ,
    'LBL_TASKS_LOW'                => 'Bassa priorità'              ,
    'LBL_TASKS_OTHER'              => 'Altre priorità'              ,
    'LBL_SELECT_PROGRESS'			=> 'Progresso - Tutti',
	'LBL_SELECT_STATUS'				=> 'Stato - Tutto',
	
	//picklist values
	'prospecting' => 'Prospetti',
    'in progress' => 'In corso',
    'initiated' => 'Avviato',
	'waiting for feedback' => 'In attesa di un commento',
    'on hold' => 'In attesa',
    'archived' => 'Archiviati',
    'completed' => 'Completato',
    'delivered' => 'Consegnato',
	'administrative' => 'Amministrativo',
    'operative' => 'Operativo',
    'other' => 'Altro',
    'low' => 'Basso',
    'normal' => 'Normale',
    'high' => 'Alto',
    
    //Gantt chart 
    'LBL_CHART' => 'Diagramma di Gantt',
    'LBL_PROGRESS_CHART' => 'progresso Diagramma di Gantt',
    'LBL_TASK_NAME' => 'nome del task',
    'LBL_START_DATE' => 'data di inizio',
    'LBL_END_DATE' => 'data di fine',
    'LBL_DURATION' => 'durata',
    'LBL_INFO' => 'Info',
    'LBL_GANTT_INFO1' => 'Far scorrere sul lato destro dell\'attività per ampliare la durata',
    'LBL_GANTT_INFO2' => 'Spostare la sinistra o il centro dell\'attività per cambiare la data iniziale o finale.',
    'LBL_EDIT_PROJECT_TASK_STATUS_COLOR' => 'Modifica del Colore dello stato dell\'attività del Progetto',
    'LBL_SELECT_PROJECT_TASK_STATUS_COLOR' => 'Seleziona il colore dello stato dell\'attività del Progetto',
    'LBL_SELECT_STATUS' => 'Selezionare Stato',
    'LBL_EDIT_COLOR' => 'Modifica colore',
);

$jsLanguageStrings = array(
    'JS_COLOR_SAVED_SUCESSFULLY' => 'Colore salvato con successo',
);