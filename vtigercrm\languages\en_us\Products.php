<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	// Basic Strings
	'Products' => 'Products',
	'SINGLE_Products' => 'Product',
	'LBL_ADD_RECORD' => 'Add Product',
	'LBL_RECORDS_LIST' => 'Products List',

	// Blocks
	'LBL_PRODUCT_INFORMATION' => 'Product Details',
	'LBL_IMAGE_INFORMATION' => 'Product Image Information',
	'LBL_STOCK_INFORMATION' => 'Stock Information',
	
	'LBL_MORE_CURRENCIES' => 'more currencies', 
	'LBL_PRICES' => 'Product Prices',
	'LBL_PRICE' => 'Price',
	'LBL_RESET_PRICE' => 'Reset Price',
	'LBL_RESET' => 'Reset',
	'LBL_ADD_TO_PRICEBOOKS' => 'Add to PriceBooks',

	//Field Labels
	'Product No' => 'Product Number',
	'Part Number' => 'Part Number',
	'Product Active' => 'Product Active',
	'Manufacturer' => 'Manufacturer',
	'Product Category' => 'Product Category',
	'Website' => 'Website',
	'Mfr PartNo' => 'Mfr PartNo',
	'Vendor PartNo' => 'Vendor PartNo',
	'Usage Unit'=>'Usage Unit',
	'Handler'=>'Handler',
	'Reorder Level'=>'Reorder Level',
	'Tax Class'=>'Tax Class',
	'Reorder Level'=>'Reorder Level',
	'Vendor PartNo'=>'Vendor Part No',
	'Serial No'=>'Serial No',
	'Qty In Stock'=>'Qty. in Stock',
	'Product Sheet'=>'Product Sheet',
	'Qty In Demand'=>'Qty. in Demand',
	'GL Account'=>'GL Account',
	'Product Image'=>'Product Image',
	'Unit Price'=>'Unit Price',
	'Commission Rate'=>'Commission Rate',
	'Qty/Unit'=>'Qty/Unit',
	
	//Added for existing picklist entries

	'--None--'=>'--None--',

	'Hardware'=>'Hardware',
	'Software'=>'Software',
	'CRM Applications'=>'CRM Applications',

	'300-Sales-Software'=>'300-Sales-Software',
	'301-Sales-Hardware'=>'301-Sales-Hardware',
	'302-Rental-Income'=>'302-Rental-Income',
	'303-Interest-Income'=>'303-Interest-Income',
	'304-Sales-Software-Support'=>'304-Sales-Software-Support',
	'305-Sales Other'=>'305-Sales Other',
	'306-Internet Sales'=>'306-Internet Sales',
	'307-Service-Hardware Labor'=>'307-Service-Hardware Labor',
	'308-Sales-Books'=>'308-Sales-Books',

	'Box'=>'Box',
	'Carton'=>'Carton',
	'Caton'=>'Caton',
	'Dozen'=>'Dozen',
	'Each'=>'Each',
	'Hours'=>'Hours',
	'Impressions'=>'Impressions',
	'Lb'=>'Lb',
	'M'=>'M',
	'Pack'=>'Pack',
	'Pages'=>'Pages',
	'Pieces'=>'Pieces',
	'Reams'=>'Reams',
	'Sheet'=>'Sheet',
	'Spiral Binder'=>'Spiral Binder',
	'Sq Ft'=>'Sq Ft',
	
	'LBL_ADD_TO_PRICEBOOKS' => 'Add to PriceBooks',
	'LBL_CONVERSION_RATE' => 'Conversion Rate',
    'LBL_PRODUCTSMOD_DISABLED' => 'Enable Products Module to view Products',
	'LBL_SHOW_BUNDLE_IN_INVENTORY' => 'Show bundle items in Invoice/Quotes/Sales Order/Purchase Order',
	'LBL_BUNDLE_TOTAL_COST' => 'Total cost of Bundle',
	'LBL_UPDATE_BUNDLE_PRICE' => 'Update Bundle Price',
	'LBL_PRODUCT_NAME' => 'Product Name',
	'LBL_PRICE_QUANTITY' => 'Price x Quantity',
);

$jsLanguageStrings = array(
	'JS_SUB_PRODUCTS_WILL_BE_SHOWN_IN_INVENTORY' => 'Sub products will be shown in the Inventory',
	'JS_SUB_PRODUCTS_WILL_NOT_BE_SHOWN_IN_INVENTORY' => 'Sub products will not be shown in the Inventory',
	'JS_SUCCESSFULLY_CHANGED_BUNDLE_COST' => 'Successfully changed total cost of bundle',
	'JS_DELETION_OR_IN_ACTIVATION_CHILD_PRODUCT_MESSAGE' => 'This product is part of a Product bundle. The price of the bundle remain unchanged when a bundle item is deleted or inactivated. Are you sure you want to proceed?',
);