<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'Potentials'                   => 'Fırsatlar'                  , 
	'SINGLE_Potentials'            => 'Fırsat'                     , 
	'LBL_ADD_RECORD'               => 'Add Opportunity'             , 
	'LBL_RECORDS_LIST'             => 'Opportunities List'          , 
	'LBL_OPPORTUNITY_INFORMATION'  => 'Fırsat Bilgisi:'            , 
	'Potential No'                 => 'Fırsat No'                  , 
	'Amount'                       => 'Tutar'                       , 
	'Next Step'                    => 'Sonraki Adım'               , 
	'Sales Stage'                  => 'Satış Aşaması'           , 
	'Probability'                  => 'Olabilirlik (%)'             , 
	'Campaign Source'              => 'Kaynak Kampanya'             , 
	'Forecast Amount'              => 'Ağırlıklı Gelir'             , 
	'Funnel'                       => 'Sales Funnel'                , 
	'Potentials by Stage'          => 'Opportunities by Stage'      , 
	'Total Revenue'                => 'Revenue by Salesperson'      , 
	'Top Potentials'               => 'Top Opportunities'           , 
	'Forecast'                     => 'Sales Forecast'              , 
	'Prospecting'                  => 'Aday Olabilirmi?'            , 
	'Qualification'                => 'Aday Olarak Uygun'           , 
	'Needs Analysis'               => 'Analiz Edelim'               , 
	'Value Proposition'            => 'Değerimizi Göster'         , 
	'Id. Decision Makers'          => 'Karar Mercii Bul'            , 
	'Perception Analysis'          => 'Algı Oluştur'              , 
	'Proposal/Price Quote'         => 'Teklif'                      , 
	'Negotiation/Review'           => 'Pazarlık'                   , 
	'Closed Won'                   => 'Kapalı - Kazanıldı'       , 
	'Closed Lost'                  => 'Kapalı - Kaybedildi'        , 
	'--None--'                     => '--Hiçbiri--'                , 
	'Existing Business'            => 'Eksi İş'                   , 
	'New Business'                 => 'Yeni İş'                   , 
	'LBL_EXPECTED_CLOSE_DATE_ON'   => 'Expected to close on'        , 
	'LBL_RELATED_CONTACTS'         => 'Related Contacts'            , // TODO: Review
	'LBL_RELATED_PRODUCTS'         => 'Related Products'            , // TODO: Review
    'Related To'                   => 'Kuruluş Adı',
    'Type'                         => 'Tip'                         , 
    
    //Convert Potentials
    'LBL_CONVERT_POTENTIAL' => 'Fırsatlar dönüştürmek',
    'LBL_POTENTIALS_FIELD_MAPPING' => 'Fırsatlar Alan Haritalama',
    'LBL_CONVERT_POTENTIALS_ERROR' => 'Sen Fırsat dönüştürmek Projesi etkinleştirmek zorunda',
    'LBL_POTENTIALS_FIELD_MAPPING_INCOMPLETE' => 'Fırsatlar Alan Haritalama eksiktir (Ayarlar> Modül Yöneticisi> Fırsatlar> Fırsatlar Field Mapping)',
	'LBL_CREATE_PROJECT' => 'Proje Oluşturma',
    
    //Potentials Custom Field Mapping
	'LBL_CUSTOM_FIELD_MAPPING'=> 'Haritalama Proje Fırsat',

  'Contact Name' => 'Kişi Adı',

);

$jsLanguageStrings = array(
	'JS_SELECT_PROJECT_TO_CONVERT_LEAD' => 'Dönüşüm Projesi seçimi gerektirir',
);