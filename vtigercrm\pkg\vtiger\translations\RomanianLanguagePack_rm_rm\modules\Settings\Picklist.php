<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_SELECT_PICKLIST_IN'       => 'Select Picklist in'          , // TODO: Review
	'LBL_ADD_VALUE'                => 'Add Value'                   , // TODO: Review
	'LBL_RENAME_VALUE'             => 'Rename Value'                , // TODO: Review
	'LBL_DELETE_VALUE'             => 'Delete Value'                , // TODO: Review
	'LBL_ITEMS'                    => 'Values'                      , // TODO: Review
	'LBL_DRAG_ITEMS_TO_RESPOSITION' => 'Drag items to reposition them', // TODO: Review
	'LBL_SELECT_AN_ITEM_TO_RENAME_OR_DELETE' => 'Select an item to rename or delete', // TODO: Review
	'LBL_TO_DELETE_MULTIPLE_HOLD_CONTROL_KEY' => 'To Delete multiple items hold Ctrl key down while selecting items', // TODO: Review
	'LBL_ADD_ITEM_TO'              => 'Add Item to'                 , // TODO: Review
	'LBL_ITEM_VALUE'               => 'Item value'                  , // TODO: Review
	'LBL_ITEM_TO_RENAME'           => 'Item to rename'              , // TODO: Review
	'LBL_ENTER_NEW_NAME'           => 'Enter new Name'              , // TODO: Review
	'LBL_RENAME_PICKLIST_ITEM'     => 'Rename PickList Item'        , // TODO: Review
	'LBL_DELETE_PICKLIST_ITEMS'    => 'Delete PickList Items'       , // TODO: Review
	'LBL_ITEMS_TO_DELETE'          => 'Items to Delete'             , // TODO: Review
	'LBL_REPLACE_IT_WITH'          => 'Replace it with'             , // TODO: Review
	'LBL_ASSIGN_TO_ROLE'           => 'Assign to Role'              , // TODO: Review
	'LBL_ALL_ROLES'                => 'All Roles'                   , // TODO: Review
	'LBL_CHOOSE_ROLES'             => 'Choose Roles'                , // TODO: Review
	'LBL_ALL_VALUES'               => 'All values'                  , // TODO: Review
	'LBL_VALUES_ASSIGNED_TO_A_ROLE' => 'Values assigned to a role'   , // TODO: Review
	'LBL_ASSIGN_VALUE'             => 'Assign Value'                , // TODO: Review
	'LBL_SAVE_ORDER'               => 'Save Order'                  , // TODO: Review
	'LBL_ROLE_NAME'                => 'Role name'                   , // TODO: Review
	'LBL_SELECTED_VALUES_MESSGAE'  => 'will appear for the user with this role', // TODO: Review
	'LBL_ENABLE/DISABLE_MESSGAE'   => 'Click on value to Enable/Disable it.After done click on save', // TODO: Review
	'LBL_ASSIGN_VALUES_TO_ROLES'   => 'Assign Values to Roles'      , // TODO: Review
	'LBL_SELECTED_VALUES'          => 'Selected Values'             , // TODO: Review
	'NO_PICKLIST_FIELDS'           => 'do not have any picklist fields', // TODO: Review
    'LBL_EDIT_PICKLIST_ITEM'       => 'Editare Picklist Articol',
	
	//Translation for module
	'Calendar'					   =>'Sarcină'						,

  'LBL_NON_EDITABLE_PICKLIST_VALUES' => 'Nu Editabile Valori',

);
$jsLanguageStrings = array(
	'JS_ITEM_RENAMED_SUCCESSFULLY' => 'Item Renamed Successfully'   , // TODO: Review
	'JS_ITEM_ADDED_SUCCESSFULLY'   => 'Item added Successfully'     , // TODO: Review
	'JS_NO_ITEM_SELECTED'          => 'No item Selected'            , // TODO: Review
	'JS_MORE_THAN_ONE_ITEM_SELECTED' => 'More than one Item selected' , // TODO: Review
	'JS_ITEMS_DELETED_SUCCESSFULLY' => 'Items Deleted Successfully'  , // TODO: Review
	'JS_YOU_CANNOT_DELETE_ALL_THE_VALUES' => 'You cannot delete all the values', // TODO: Review
	'JS_ALL_ROLES_SELECTED'        => 'All Roles Selected'          , // TODO: Review
	'JS_LIST_UPDATED_SUCCESSFULLY' => 'List updated Successfully'   , // TODO: Review
	'JS_SEQUENCE_UPDATED_SUCCESSFULLY' => 'Sequence updated successfully', // TODO: Review
	'JS_VALUE_ASSIGNED_SUCCESSFULLY' => 'Value assigned successfully' , // TODO: Review
	'JS_PLEASE_SELECT_MODULE'      => 'Please seelct module'        , // TODO: Review

  'JS_SPECIAL_CHARACTERS' => 'Caractere speciale, cum ar fi',
  'JS_NOT_ALLOWED' => 'nu este permisă',

);