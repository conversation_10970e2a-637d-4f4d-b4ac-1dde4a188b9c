<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'LBL_SELECT_EMAIL_IDS' => 'Zaznacz adresy mailowe',
	'LBL_SUBJECT' => 'Temat',
	'LBL_ATTACHMENT' => 'Załącznik',
	'LBL_BROWSE_CRM' => 'Przeglądaj dokumenty w CRM',
	'LBL_SEND' => 'Wyślij',
	'LBL_SAVE_AS_DRAFT' => 'Zapisz jako kopia robocza',
	'LBL_SELECT_EMAIL_TEMPLATE' => 'Wybierz ',
	'LBL_COMPOSE_EMAIL' => 'Utwórz wiadomość',
 	'LBL_CC' => 'DW',
   	'LBL_BCC' => 'UDW',
   	'LBL_ADD_CC' => 'Dodaj do DW',
   	'LBL_ADD_BCC' => 'Dodaj do UDW',
	'LBL_MAX_UPLOAD_SIZE' => 'Maksymalny rozmiar załącznika ',
	'LBL_EXCEEDED' => 'przekroczony',
    'LBL_EMAIL_INFORMATION' => 'Informacje o e-mail',

    'LBL_EMAILTEMPLATE_WARNING'    => 'Czy twoje seryjnej-tags poprawić',
    'LBL_EMAILTEMPLATE_WARNING_CONTENT' => 'Upewnij się, że szablon ma scalić wybrane znaczniki odpowiednie do rekordu odbiorcy. 
                                            Jeśli wysyłasz wiadomość e-mail do bramki, jednak scalanie-tags należą do Kontakt moduł (np.: $contacts-lastname$), 
                                            wówczas wartości nie zostaną połączone.',
	'Draft'=>'Szkic',
    'Parent ID' => 'Rekord rodzic',
	

  'SINGLE_Emails' => 'E-mail',
  'Emails' => 'E-maile',
  'LBL_GO_TO_PREVIEW' => 'Wejść w podgląd',
  'LBL_TO' => 'Do',
  'LBL_FORWARD' => 'Do przodu',
  'LBL_PRINT' => 'Drukowanie',
  'LBL_DESCRIPTION' => 'Opis',
  'LBL_FROM' => 'Od',
  'LBL_INFO' => 'Info',
  'LBL_DRAFTED_ON' => 'Sporządzony na',
  'LBL_SENT_ON' => 'Wysłał na',
  'LBL_OWNER' => 'Właściciel',
  'Date & Time Sent' => 'Data Wysłania',
  'Time Start' => 'Raz Wysłał',

);

$jsLanguageStrings = array(
    'JS_WARNING' => 'ostrzeżenie',
);   