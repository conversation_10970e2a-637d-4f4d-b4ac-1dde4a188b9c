<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'SINGLE_Settings:Webforms'     => 'Webform'                     , // TODO: Review
	'WebForm Name'                 => 'Webform Name'                , // TODO: Review
	'Public Id'                    => 'Public Id'                   , // TODO: Review
	'Enabled'                      => 'Status'                      , // TODO: Review
	'Module'                       => 'Module'                      , // TODO: Review
	'Return Url'                   => 'Return Url'                  , // TODO: Review
	'Post Url'                     => 'Post Url'                    , // TODO: Review
    'Captcha Enabled'              => 'Captcha etkin'               ,
	'SINGLE_Webforms'              => 'Webform'                     , // TODO: Review
	'LBL_SHOW_FORM'                => 'Show Form'                   , // TODO: Review
	'LBL_DUPLICATES_EXIST'         => 'Webform Name already exists' , // TODO: Review
	'LBL_WEBFORM_INFORMATION'      => 'Webform Information'         , // TODO: Review
	'LBL_FIELD_INFORMATION'        => 'Field Information'           , // TODO: Review
	'LBL_FIELD_NAME'               => 'Field Name'                  , // TODO: Review
	'LBL_OVERRIDE_VALUE'           => 'Override Value'              , // TODO: Review
	'LBL_MANDATORY'                => 'Mandatory'                   , // TODO: Review
	'LBL_WEBFORM_REFERENCE_FIELD'  => 'Webforms reference Field'    , // TODO: Review
	'LBL_SELECT_FIELDS_OF_TARGET_MODULE' => 'Select Fields for Target Module...', // TODO: Review
	'LBL_ALLOWS_YOU_TO_MANAGE_WEBFORMS' => 'Allows you to manage webforms', // TODO: Review
	'LBL_ADD_FIELDS'               => 'Add Fields'                  , // TODO: Review
	'LBL_EMBED_THE_FOLLOWING_FORM_IN_YOUR_WEBSITE' => 'Embed the following form in your website', // TODO: Review
	'LBL_SELECT_VALUE'             => 'Select Value'                , // TODO: Review
	'LBL_LABEL'                    => 'label'                       , // TODO: Review
	'LBL_SAVE_FIELDS_ORDER' => 'Kaydet alanlar sipariş', 
	'LBL_HIDDEN' => 'Gizli',
	'LBL_ENABLE_TARGET_MODULES_FOR_WEBFORM' => 'Webform için hedef modülleri etkinleştirme',
	'LBL_ASSIGN_USERS' => 'Kullanıcı Atama',
    'LBL_ASSIGN_ROUND_ROBIN' => 'Round Robin yılında Kullanıcılar atama',
    'LBL_ROUNDROBIN_USERS_LIST' => 'Round Robin Kullanıcılar Listesi',

  'LBL_ADD_RECORD' => 'Web Formunu Ekleyin',

	'LBL_UPLOAD_DOCUMENTS' => 'Yükleme Belgeler',
	'LBL_ADD_FILE_FIELD' => 'Yükleme Field Dosya',
	'LBL_FIELD_LABEL' => 'Belge başlığı',
	'LBL_FILE_FIELD_INFO' => "Web'den yüklenen her dosya için yeni bir belge ekli dosya ile oluşturulur oluştururlar. Belge de bu yeni oluşturulan%s ile bağlantılıdır.",
	'LBL_NO_FILE_FIELD' => 'Hiçbir dosya alanları eklendi.',
	'LBL_COPY_TO_CLIPBOARD' => 'Panoya kopyala',
);
$jsLanguageStrings = array(
	'JS_WEBFORM_DELETED_SUCCESSFULLY' => 'Webform deleted successfully', // TODO: Review
	'JS_LOADING_TARGET_MODULE_FIELDS' => 'Loadding Target Module Fields', // TODO: Review
	'JS_SELECT_VALUE'              => 'Select Vlaue'                , // TODO: Review
	'JS_MANDATORY_FIELDS_WITHOUT_OVERRIDE_VALUE_CANT_BE_HIDDEN' => 'Yüzdesel değerler olmadan zorunlu alanlar gizli cant',
	'JS_REFERENCE_FIELDS_CANT_BE_MANDATORY_WITHOUT_OVERRIDE_VALUE' => 'Referans alanlar geçersiz kılma değeri olmayan zorunlu olamaz',
	'JS_TYPE_TO_SEARCH' => 'Aramak için yazın',
	"JS_WEBFORM_WITH_THIS_NAME_ALREADY_EXISTS" => 'Bu adla Webform zaten var',

  'JS_SELECT_AN_OPTION' => 'Bir Seçenek belirleyin',
  'JS_LABEL' => 'etiket',

	'JS_MAX_FILE_FIELDS_LIMIT' => 'Maksimum Eğer%s dosyası alanları ekleyebilirsiniz.',
	'JS_COPIED_SUCCESSFULLY' => 'Başarıyla kopyalandı.',
	'JS_COPY_FAILED' => 'Kopya başarısız oldu. El ile kopyalayın.',
);