<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	// Basic Strings
	'Potentials' => 'Opportunities',
	'SINGLE_Potentials' => 'Opportunity',
	'LBL_ADD_RECORD' => 'Add Opportunity',
	'LBL_RECORDS_LIST' => 'Opportunities List',

	// Blocks
	'LBL_OPPORTUNITY_INFORMATION' => 'Opportunity Details',

	//Field Labels
	'Potential No' => 'Opportunity Number',
	'Amount' => 'Amount',
	'Next Step' => 'Next Step',
	'Sales Stage' => 'Sales Stage',
	'Probability' => 'Probability',
	'Campaign Source' => 'Campaign Source',
	'Forecast Amount' => 'Weighted Revenue',
	'Related To' => 'Organization Name',
	'Contact Name' => 'Contact Name',
        'Type' => 'Type',
	
	//Dashboard widgets
	'Funnel' => 'Sales Funnel',
	'Potentials by Stage' => 'Opportunities by Stage',
	'Total Revenue' => 'Revenue by Salesperson',
	'Top Potentials' => 'Top Opportunities',
	'Forecast' => 'Sales Forecast',

	//Added for Existing Picklist Strings

	'Prospecting'=>'Prospecting',
	'Qualification'=>'Qualification',
	'Needs Analysis'=>'Needs Analysis',
	'Value Proposition'=>'Value Proposition',
	'Id. Decision Makers'=>'Identify Decision Makers',
	'Perception Analysis'=>'Perception Analysis',
	'Proposal/Price Quote'=>'Proposal/Quotation',
	'Negotiation/Review'=>'Negotiation/Review',
	'Closed Won'=>'Closed Won',
	'Closed Lost'=>'Closed Lost',

	'--None--'=>'--None--',
	'Existing Business'=>'Existing Business',
	'New Business'=>'New Business',
	'LBL_EXPECTED_CLOSE_DATE_ON' => 'Expected to close on',

	//widgets headers
	'LBL_RELATED_CONTACTS' => 'Related Contacts',
	'LBL_RELATED_PRODUCTS' => 'Related Products',
    
    //Convert Potentials
    'LBL_CONVERT_POTENTIAL' => 'Convert Opportunity',
	'LBL_CREATE_PROJECT' => 'Create Project',
    'LBL_POTENTIALS_FIELD_MAPPING' => 'Opportunities Field Mapping',
    'LBL_CONVERT_POTENTIALS_ERROR' => 'You have to enable Project to convert the Opportunity',
    'LBL_POTENTIALS_FIELD_MAPPING_INCOMPLETE' => 'Opportunities Field Mapping is incomplete(Settings > Module Manager > Opportunities > Opportunities Field Mapping)',
    
    //Potentials Custom Field Mapping
	'LBL_CUSTOM_FIELD_MAPPING'=> 'Opportunity to Project mapping',
);

$jsLanguageStrings = array(
	'JS_SELECT_PROJECT_TO_CONVERT_LEAD' => 'Conversion requires selection of Project',
);