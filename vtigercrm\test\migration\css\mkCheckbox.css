/* hide the checkboxes and radios */
input[type="checkbox"],input[type="radio"]
{
    opacity: 0;
    position: absolute;
    width:14px;
    height: 14px;
    margin: 4px 2px;
    margin-top: 2px\9;
}
label{
    display: inline-block;
}
/* Inline div for placeholder to the checkbox*/
.chkbox, .radiobtn {
    display: inline;
    margin-right: 20px\9;
}
/* Fix for IE */
:root .chkbox, :root .radiobtn {
    margin-right: 0px\9;
}
/* we use generated content to create a placeholder for the checkbox*/
input[type="checkbox"] + div::before,
input[type="radio"] + div::before
{
    content: "";
    display: inline-block;
    width: 17px;
    height: 17px;
    margin: -2px 8px 0 0;
    background: url("check_radio_sprite.png") no-repeat 0 -17px;
    vertical-align: middle;
}
/* hover checkbox (unselected state only) */
input[type="checkbox"]:not(:checked):hover + div::before
{
    background-position: 0 -68px;
}
/* selected checkbox */
input[type="checkbox"]:checked + div::before
{
    background-position: 0 0;
}
/* disabled checkbox*/
input[type="checkbox"]:disabled + div::before
{
    background-position: 0 -51px;
    opacity: 0.6;
}
/* disabled checked checkbox*/ 
input[type="checkbox"]:disabled:checked + div::before
{
    background-position: 0 -34px;
    opacity: 0.6;
}
/*Unselected Radio */
input[type="radio"] + div::before
{
    background-position: -17px -17px;
}
/* radio hover (unselected state only) */
input[type="radio"]:hover + div::before
{
    background-position: -17px -68px;
}
/* selected radio */
input[type="radio"]:checked + div::before
{
    background-position: -17px 0px;
}
/* disabled radio*/
input[type="radio"]:disabled + div::before
{
    background-position: -17px -51px;
    opacity: 0.6;
}
/* disabled checked radio*/ 
input[type="radio"]:disabled:checked + div::before
{
    background-position: -17px -34px;
    opacity: 0.6;
}
/* Single line text box*/
input[type="text"], input[type="password"] {
    border: 1px solid #ccc;
    border-radius: 3px;
    box-shadow: 1px 1px 2px #ddd;
    height: 30px !important;
    width: 180px;
    margin-bottom:0px;
}
