<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'Services'                     => 'Servicii'                    , 
	'SINGLE_Services'              => 'Serviciu'                    , 
	'LBL_ADD_RECORD'               => 'Adauga serviciu'             , 
	'LBL_RECORDS_LIST'             => 'Services List'               , 
	'LBL_SERVICE_INFORMATION'      => 'Informatie Service'          , 
	'LBL_MORE_CURRENCIES'          => 'mai multe monede'            , 
	'LBL_PRICES'                   => 'Service Prices'              , 
	'LBL_PRICE'                    => 'Pret'                        , 
	'LBL_RESET_PRICE'              => 'Reseteaza pret'              , 
	'LBL_RESET'                    => 'Reseteaza'                   , 
	'LBL_ADD_TO_PRICEBOOKS'        => 'Add to PriceBooks'           , 
	'Service Name'                 => 'Nume Serviciu'               , 
	'Service Active'               => 'Activ'                       , 
	'Service Category'             => 'Categorie'                   , 
	'Service No'                   => 'ID Serviciu'                 , 
	'Owner'                        => 'Proprietar'                  , 
	'No of Units'                  => 'Nr Unitati'                  , 
	'Commission Rate'              => 'Rata Comision (%)'           , 
	'Price'                        => 'Pret'                        , 
	'Usage Unit'                   => 'Unitate de Utilizare'        , 
	'Tax Class'                    => 'Clasa Impozit'               , 
	'Website'                      => 'Site Web'                    , 
);