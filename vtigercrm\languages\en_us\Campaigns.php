<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	// Basic Strings
	'Campaigns' => 'Campaigns',
	'SINGLE_Campaigns' => 'Campaign',
	'LBL_ADD_RECORD' => 'Add Campaign',
	'LBL_RECORDS_LIST' => 'Campaigns List',

	// Blocks
	'LBL_CAMPAIGN_INFORMATION' => 'Campaign Details',
	'LBL_EXPECTATIONS_AND_ACTUALS' => 'Expectations & Actuals',

	//Field Labels
	'Campaign Name' => 'Campaign Name',
	'Campaign No' => 'Campaign No', 
	'Campaign Type' => 'Campaign Type', 
	'Product' => 'Product',
	'Campaign Status' => 'Campaign Status',
	'Num Sent' => 'Num Sent',
	'Sponsor' => 'Sponsor',
	'Target Audience' => 'Target Audience',
	'TargetSize' => 'TargetSize',
	'Expected Response' => 'Expected Response',
	'Expected Revenue' => 'Expected Revenue',
	'Budget Cost' => 'Budget Cost',
	'Actual Cost' => 'Actual Cost',
	'Expected Response Count' => 'Expected Response Count',
	'Expected Sales Count' => 'Expected Sales Count',
	'Expected ROI' => 'Expected ROI',
	'Actual Response Count' => 'Actual Response Count',
	'Actual Sales Count' => 'Actual Sales Count',
	'Actual ROI' => 'Actual ROI',

	//Added for existing Picklist Entries

	'Webinar'=>'Webinar',
	'Referral Program'=>'Referral Program',
	'Advertisement'=>'Advertisement',
	'Banner Ads'=>'Banner Ads',
	'Direct Mail'=>'Direct Mail',
	'Telemarketing'=>'Telemarketing',
	'Others'=>'Others',

	'Planning'=>'Planning',						      	    
	'Inactive'=>'Inactive',
	'Complete'=>'Complete',
	'Cancelled'=>'Cancelled',							      

	'Excellent'=>'Excellent',
	'Good'=>'Good',
	'Average'=>'Average',
	'Poor'=>'Poor',

	// status fields 
	'--None--'=>'--None--',
	'Contacted - Successful' => 'Contacted - Successful',
	'Contacted - Unsuccessful' => 'Contacted - Unsuccessful',
	'Contacted - Never Contact Again' => 'Contacted - Never Contact Again',
);

$jsLanguageStrings = array(
	'JS_APPENDED_TO_EXISTING_LIST' => '%s from the selected list will be appended with the existing list.',
);