<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_IMPORT_MODULE'            => 'Import Module'               , // TODO: Review
	'LBL_VTIGER_EXTENSION_STORE'   => 'Vtiger Extension Store'      , // TODO: Review
	'LBL_PUBLISHER'                => 'Publisher'                   , // TODO: Review
	'LBL_LICENSE'                  => 'License'                     , // TODO: Review
	'LBL_PUBLISHED_ON'             => 'Published on'                , // TODO: Review
	'LBL_INSTALL'                  => 'Install'                     , // TODO: Review
	'LBL_UPGRADE'                  => 'Upgrade'                     , // TODO: Review
	'LBL_VERSION'                  => 'Version'                     , // TODO: Review
	'LBL_DECLINE'                  => 'Decline'                     , // TODO: Review
	'LBL_ACCEPT_AND_INSTALL'       => 'Accept and Install'          , // TODO: Review
	'LBL_ALREADY_EXISTS'           => 'Already Exists'              , // TODO: Review
	'LBL_OK'                       => 'OK'                          , // TODO: Review
	'LBL_EXTENSION_NOT_COMPATABLE' => 'Extension is not Vtiger Compatable', // TODO: Review
	'LBL_INVALID_FILE'             => 'Invalid File'                , // TODO: Review
	'LBL_NO_LICENSE_PROVIDED'      => 'No License Provided'         , // TODO: Review
	'LBL_INSTALLATION'             => 'Installation'                , // TODO: Review
	'LBL_FAILED'                   => 'Failed'                      , // TODO: Review
	'LBL_SUCCESSFULL'              => 'Successfull'                 , // TODO: Review
	'LBL_INSTALLATION_LOG'         => 'Installation Log'            , // TODO: Review

        //Extension Store translations
        'LBL_VTIGER_EXTENSION_STORE' => 'Vtiger Uzatma Mağazası',
        'LBL_SEARCH_FOR_EXTENSION'  => 'Uzatma ara',
        'LBL_DOWNLOADS' => 'Yüklemeler',
        'LBL_NO_EXTENSIONS_FOUND' => 'Bulunamadı Uzantıları',
        'LBL_REGISTER_USER' => 'Üye Kaydı',	
        'LBL_SETUP_CARD_DETAILS' => 'Kurulum Kartı Detayları',
        'LBL_SETUP_CARD' => 'Kurulum Kartı',
        'LBL_CARD_NUMBER' => 'Kart Numarası',
        'LBL_EXP_MONTH' => 'Uzm Ay',
        'LBL_EXP_YEAR' => 'Uzm Yıl',
        'LBL_CVC_CODE' => 'CVC 3 haneli bir kod',
        'LBL_RESET' => 'Ayarlamak',
        'LBL_EXTENSION_STORE' => 'Uzatma Mağazası',
        'LBL_INSTALLED' => 'Yüklü',
        'LBL_UPDATE_CARD_DETAILS' => 'Güncelleme Kart Bilgileri',
        'LBL_BY' => 'Tarafından',
        'LBL_RATINGS' => 'Puanlar',
        'LBL_DESCRIPTION' => 'Tanım',
        'LBL_AUTHOR_INFORMATION' => 'Yazar Bilgileri',
        'LBL_AUTHOR_NAME' => 'Yazar Adı',
        'LBL_PHONE' => 'Telefon',
        'LBL_EMAIL' => 'E-posta',
        'LBL_SCREEN_SHOTS' => 'Ekran',
        'LBL_CUSTOMER_RATINGS' => 'Müşteri Puanları',
        'LBL_CUSTOMER_REVIEWS' => 'Müşteri Yorumları',
        'LBL_WRITE_A_REVIEW' => 'Bir Yorum Yazın',
        'LBL_CUSTOMER_REVIEW' => 'Müşteri Gözden',
        'LBL_REVIEW' => 'İnceleme',
        'LBL_CUSTOMERS_REVIEWED' => 'Yorum Müşteriler',
        'LBL_SINGLE_CUSTOMER_REVIEWED' => 'Tek Müşteri yorum',
        'LBL_INSTALLATION_FAILED' => 'Yükleme Başarısız',
        'LBL_SUCCESSFULL_INSTALLATION' => 'Başarılı Kurulum',
        'Install' => 'Kurmak',
        'Upgrade' => 'Yükseltmek',
        'LBL_RATE_IT' => 'Ver',
);
$jsLanguageStrings = array(
  'JS_PLEASE_SETUP_CARD_DETAILS_TO_INSTALL_THIS_EXTENSION'  => 'Kurulum kartı bilgileriniz bu eklenti yüklemek için lütfen',
  'JS_UPDATE_CARD_DETAILS' => 'Güncelleme Kart Bilgileri',
  'JS_ON' => 'üzerinde',
  'JS_RATINGS' => 'Puanlar',
  'JS_PLEASE_INSTALL_EXTENSION_LOADER_TO_INSTALL_THIS_EXTENSION_FROM_BELOW_LINK' => 'Aşağıdaki linkten bu eklentiyi kurmak için uzatma yükleyici kurun',
);