<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'SalesOrder'    =>  'Sales Orders',
    //DetailView Actions
	'SINGLE_SalesOrder' => 'Sales Order',
	'LBL_EXPORT_TO_PDF' => 'Export to PDF',
    'LBL_SEND_MAIL_PDF' => 'Send Email with PDF',

	//Basic strings
	'LBL_ADD_RECORD' => 'Add Sales Order',
	'LBL_RECORDS_LIST' => 'Sales Order List',

	// Blocks
	'LBL_SO_INFORMATION' => 'Sales Order Details',

	//Field labels
	'SalesOrder No'=>'Sales Order Number',
	'Quote Name'=>'Quote Name',
	'Customer No' => 'Customer No',
	'Requisition No'=>'Requisition No',
	'Tracking Number'=>'Tracking Number',
	'Sales Commission' => 'Sales Commission',
	'Purchase Order'=>'Purchase Order',
	'Vendor Terms'=>'Vendor Terms',
	'Pending'=>'Pending',
	'Enable Recurring' => 'Enable Recurring',
	'Frequency' => 'Frequency',
	'Start Period' => 'Start Period',
	'End Period' => 'End Period',
	'Payment Duration' => 'Payment Duration',
	'Invoice Status' => 'Invoice Status',

	//Added for existing Picklist Entries

	'Sub Total'=>'Sub Total',
	'AutoCreated'=>'Auto Created',
	'Sent'=>'Sent',
	'Credit Invoice'=>'Credit Invoice',
	'Paid'=>'Paid',
	
	//Translation for product not found
	'LBL_THIS' => 'This',
	'LBL_IS_DELETED_FROM_THE_SYSTEM_PLEASE_REMOVE_OR_REPLACE_THIS_ITEM' => 'is deleted from the system.please remove or replace this item',
	'LBL_THIS_LINE_ITEM_IS_DELETED_FROM_THE_SYSTEM_PLEASE_REMOVE_THIS_LINE_ITEM' => 'This line item is deleted from the system,please remove this line items',
);

$jsLanguageStrings = array(
	'JS_PLEASE_REMOVE_LINE_ITEM_THAT_IS_DELETED' => 'Please remove line item that is deleted',
);
