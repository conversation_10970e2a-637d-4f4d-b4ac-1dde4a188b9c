<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD'               => 'Nuova configurazione'           , // TODO: Review
	'SMSNotifier'                  => 'Configurazione Provider SMS'  , // TODO: Review
	'LBL_ADD_CONFIGURATION'        => 'Nuova Configurazione'           , // TODO: Review
	'LBL_EDIT_CONFIGURATION'       => 'Modifica Configurazione'          , // TODO: Review
	'LBL_SELECT_ONE'               => 'Seleziona uno'                  , // TODO: Review
	'providertype'                 => 'Provider'                    , // TODO: Review
	'isactive'                     => 'Attivo'                      , // TODO: Review
	'username'                     => 'nome utente'                   , // TODO: Review
	'password'                     => 'Password'                    , // TODO: Review
);
$jsLanguageStrings = array(
	'LBL_DELETE_CONFIRMATION'      => 'Sei sicuoro che vuoi cancellare questa configurazione di SMS Notifier?', // TODO: Review
	'JS_RECORD_DELETED_SUCCESSFULLY' => 'Provider SMS cancellato con successo', // TODO: Review
	'JS_CONFIGURATION_SAVED'       => 'Configurazione Provider SMS salvata', // TODO: Review
);