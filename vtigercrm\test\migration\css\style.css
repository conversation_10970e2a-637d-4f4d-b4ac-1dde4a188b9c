/* 
    Document   : style
    Created on : Jun 5, 2013, 3:23:02 PM
    Author     : Administrator
    Description:
        Purpose of the stylesheet follows.
*/
html{
    height:100%;
}
body{
    font-family: <PERSON><PERSON><PERSON>, "Trebuchet MS","Lucida Grande",Verdana !important;
    background: #F5FAEE !important;/*#f1f6e8;*/
    color : #555 !important;
    font-size: 85% !important;
    height: 98% !important;
}
.page-container{
    width:70%;
    margin: 10px auto;
    min-height: 600px;
    min-width: 1100px;
}
.error{
    border: 1px solid red !important;
}
.main-container{
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 3px;
    box-shadow: 0 0 5px #ccc;
    min-height: 375px;
    *padding: 0 15px;
}
.logo{
    padding: 15px 0 ;
}
.head
{
    padding-top: 15px;
}
.inner-container{
    padding: 15px 2%;
    *padding: 15px 0 0;
}
.welcome-image{
    text-align: right;
}
.alignCenter{
    text-align: center;
}
.welcome-div{
    padding: 16% 0;
}
.welcome-div{
    padding: 16% 0;
}
.mig-div{
    padding: 10% 0;
}
.vLinks{
    margin: 6% 0;
    padding: 4% 10%;
    width: 85px;
    height: 200px;
    background: #eee;
    border-radius: 4px;
    float: right;
    border: 1px solid #ddd;
}
.marginTopZero{
    margin-top: 0;
}
hr{
    border: 1px solid #ddd;
    margin: 13px 0;
}
.row-fluid .group-container{
    background: #eee;
    border: 1px solid #ddd;
    border-radius: 3px;
    margin-bottom: 25px; 
    padding: 15px;
    position: relative;
    min-height: 230px !important;
    *margin: 0px !important;
    *padding: 5px 0;
}
.group-heading{
    color: #2c69a7;
    text-shadow: 0px 0px 1px #BBB;
}
.group-desc{
    padding-bottom:9px;
    min-height: 130px;
}
.module_list{
    background: #146bae;
    border-radius: 3px 0;
    right: 0;
    bottom: 0;
    position: absolute;
    color: #fff;
    font-weight: bold;
    padding: 3px 10px;
    text-shadow: 0 0 1px #000;
    z-index: 605;
}
.module_list a{
    cursor: pointer;
    text-decoration: none;
    color: #fff;
}
.reveal_modules{
    position: absolute;
    height:0;
    width: 0;
    z-index: 600;
    background: #000;
    opacity: 0;
    top:100%;
    left:100%;
    color: #fff;
    visibility:hidden;
    border-radius: 3px;
}
.reveal_modules > div{
    padding-left: 30px;
}
.reveal_modules > div > p{
    margin: 0  30px 10px 0;
    vertical-align: top;
    display: inline-block;
}
.config-table{
    width: 100%;
    border: 1px solid #ddd;    
    margin: 5px 0 15px;
    border-radius: 3px;
    border-collapse: separate;
    padding: 0;
    background: #fff;
}
.config-table tr td, .config-table tr th{
    padding: 8px;
}

.config-table tr th:last-child{
    border-radius: 0 3px 0 0;
}
.config-table tr th:first-child{
    border-radius: 3px 0 0 0;
    text-align: left;
}
.config-table tr td:first-child{
    text-align: left;
}
.config-table th{
    background: #ddd;
}
.config-table tr:hover{
    background: #efefef;
}

.config-table td, .config-table th{
    text-align: center;
    width: 33%;
} 

.config-table tr td.no, p.no,span.no{
    color: red;
    font-weight: bold;
} 
.config-table tr td.yes, p.yes, span.yes{
    color: green;
    font-weight: bold;

}
.config-table td span img{
    margin-left: 10px;
}
.button-container a{
    text-decoration: none;
}
.button-container{
    float: right;
}
.button-container .btn{
    margin-left: 15px;
    min-width: 100px;
    font-weight: bold;
}
.license{
    padding: 10px;   
    height: 330px;
    overflow-y: scroll;
    margin: 15px 0;
    border: 1px solid #ddd;
    font-size: 12px;
}
.input-table tr td{
    padding: 5px 10px;
    height: 30px;
    text-align: left;
}
