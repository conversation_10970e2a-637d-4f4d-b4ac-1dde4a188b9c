<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
    'Portal' => 'Nasze strony',
    'LBL_ADD_BOOKMARK' => 'Dodaj do zakła<PERSON>',
    'LBL_BOOKMARK_NAME' => 'Nazwa zakładka',
    'LBL_BOOKMARK_URL' => 'Dodaj do zakładek WWW',
    'LBL_CREATED_ON' => 'Utworzono dnia',
    'SINGLE_Portal' => 'Nasze strony',
    'LBL_EDIT_BOOKMARK' => 'Edytuj zakładkę',
    'LBL_ENTER_BOOKMARK_NAME' => 'Wpisz nazwę zakładki',
    'LBL_ENTER_URL' => 'Wpisz adres URL (www.example.com)',
    'LBL_ADD_NEW_BOOKMARK' => 'Dodaj nową zakładkę',
    'LBL_BOOKMARK_SAVED_SUCCESSFULLY' => 'Zakładka zapisana',
    'LBL_RECORD_DELETED_SUCCESSFULLY' => 'Rekord został pomyślnie usunięty',
    'LBL_OUR_SITES_LIST' => 'Nasza Lista Witryny',
    'LBL_BOOKMARKS_LIST' => 'Lista zakładek',
    'LBL_BOOKMARKS_DELETED_SUCCESSFULLY' => 'Zakładki usunięta pomyślnie',
    'LBL_BOOKMARK' => 'Dodaj do ulubionych',
    'LBL_BOOKMARKS' => 'Zakładki',
    'HTTP_ERROR' => 'Strona, którą próbujesz otworzyć nie jest bezpieczna i może nie otwarte. Jeśli nadal chcesz obejrzeć stronę internetową, a następnie kliknij na blokera treści w pasku adresu i włączyć go.',
);

$jsLanguageStrings = array(
    'JS_ENTER_MANDATORY_FIELDS' => 'Wprowadź wszystkie wymagane pola',
);