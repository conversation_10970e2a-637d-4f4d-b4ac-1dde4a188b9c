<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	// Basic Strings
	'Profiles' => 'Profiles',
	'SINGLE_Profiles' => 'Profile',
	'LBL_ADD_RECORD' => 'Add Profile',
	'LBL_CREATE_PROFILE' => 'Create Profile',
	'LBL_PROFILE_NAME' => 'Profile name',
	'LBL_DESCRIPTION' => 'Description',
	'LBL_EDIT_PRIVILEGES_OF_THIS_PROFILE' => 'Edit privileges of this profile',
	'LBL_MODULES' => 'Modules',
	'LBL_PROFILE_VIEW' => 'Profile view',
	'LBL_FIELDS' => 'Fields',
	'LBL_TOOLS' => 'Tools',
	'LBL_FIELD_AND_TOOL_PRIVILEGES' => 'Field and Tool Privileges',
	'LBL_EDIT_RECORD' => 'Edit',
	'LBL_DUPLICATE_RECORD' => 'Duplicate',
	'LBL_DELETE_RECORD' => 'Delete',

	'LBL_VIEW_PRVILIGE' => 'View',
	'LBL_EDIT_PRVILIGE' => 'Create/Edit',
	'LBL_DELETE_PRVILIGE' => 'Delete',
	'LBL_INIVISIBLE' => 'Invisible',
	'LBL_READ_ONLY' => 'Read only',
	'LBL_WRITE' => 'Write',

	'LBL_DELETE_PROFILE' => 'Delete Profile',
	'LBL_TRANSFER_ROLES_TO_PROFILE' => 'Transfer roles to profile',
	'LBL_PROFILES' => 'Profiles',
	'LBL_CHOOSE_PROFILES' => 'Choose Profiles',

	'LBL_VIEW_ALL' => 'View All',
	'LBL_EDIT_ALL' => 'Edit All',
	'LBL_VIEW_ALL_DESC' => 'Can view all the modules information',
	'LBL_EDIT_ALL_DESC' => 'Can edit all the modules information',
	'LBL_DUPLICATES_EXIST' => 'Duplicate Profile Exists',

	/*Vtiger7 Strings*/
	'LBL_EDIT_PROFILE' => 'Edit Profile',
	'LBL_GLOBAL_PERMISSION_WARNING' => 'Warning : Users associated with this profile can access all records in the CRM',
);

$jsLanguageStrings = array(
	'JS_RECORD_DELETED_SUCCESSFULLY' => 'Profile deleted successfully',
);