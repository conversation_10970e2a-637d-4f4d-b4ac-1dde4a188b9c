<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
    'Portal' => 'A webhelyek',
    'LBL_ADD_BOOKMARK' => 'Köny<PERSON>jel<PERSON>ő hozzáadása',
    'LBL_BOOKMARK_NAME' => 'Könyvjelző nevét',
    'LBL_BOOKMARK_URL' => 'Könyvjelző Url',
    'LBL_CREATED_ON' => 'Létrehozva',
    'SINGLE_Portal' => 'Honlapunk',
    'LBL_EDIT_BOOKMARK' => 'Könyvjelző szerkesztése',
    'LBL_ENTER_BOOKMARK_NAME' => 'Adja meg a könyvjelző nevét',
    'LBL_ENTER_URL' => 'Írja be az URL (www.example.com)',
    'LBL_ADD_NEW_BOOKMARK' => 'Új könyvjelző',
    'LBL_BOOKMARK_SAVED_SUCCESSFULLY' => 'Könyvjelző sikeresen mentve',
    'LBL_RECORD_DELETED_SUCCESSFULLY' => 'Record sikeresen törölve',
    'LBL_OUR_SITES_LIST' => 'A webhelyek listája',
    'LBL_BOOKMARKS_LIST' => 'Könyvjelzők listája',
    'LBL_BOOKMARKS_DELETED_SUCCESSFULLY' => 'Könyvjelzők sikeresen törölve',
    'LBL_BOOKMARK' => 'Könyvjelző',
    'LBL_BOOKMARKS' => 'Könyvjelzők',
    'HTTP_ERROR' => 'A honlap, amit próbál megnyitni nem biztonságos, és lehet, hogy nem nyitott. Ha továbbra is szeretné megtekinteni a weboldalon, akkor kattintson a tartalmat blokkoló a címsorba, és lehetővé teszi.',
);

$jsLanguageStrings = array(
    'JS_ENTER_MANDATORY_FIELDS' => 'Kérjük, adja meg az összes kötelező mezőt',
);