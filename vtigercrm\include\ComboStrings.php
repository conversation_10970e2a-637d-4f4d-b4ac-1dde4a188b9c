<?php
/*********************************************************************************
** The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
*
 ********************************************************************************/

global $combo_strings;

$combo_strings = Array(
'accounttype_dom' => Array(''=>''
                , 'Analyst'=>'Analyst'
                , 'Competitor'=>'Competitor'
                , 'Customer'=>'Customer'
                , 'Integrator'=>'Integrator'
                , 'Investor'=>'Investor'
                , 'Partner'=>'Partner'
                , 'Press'=>'Press'
                , 'Prospect'=>'Prospect'
                , 'Reseller'=>'Reseller'
                , 'Other'=>'Other'
                ),
'industry_dom' => Array(''=>''
                , 'Apparel'=>'Apparel'
                , 'Banking'=>'Banking'
                , 'Biotechnology'=>'Biotechnology'
                , 'Chemicals'=>'Chemicals'
                , 'Communications'=>'Communications'
                , 'Construction'=>'Construction'
                , 'Consulting'=>'Consulting'
                , 'Education'=>'Education'
                , 'Electronics'=>'Electronics'
                , 'Energy'=>'Energy'
                , 'Engineering'=>'Engineering'
                , 'Entertainment'=>'Entertainment'
                , 'Environmental'=>'Environmental'
                , 'Finance'=>'Finance'
                , 'Food & Beverage'=>'Food & Beverage'
                , 'Government'=>'Government'
                , 'Healthcare'=>'Healthcare'
                , 'Hospitality'=>'Hospitality'
                , 'Insurance'=>'Insurance'
                , 'Machinery'=>'Machinery'
                , 'Manufacturing'=>'Manufacturing'
                , 'Media'=>'Media'
                , 'Not For Profit'=>'Not For Profit'
                , 'Recreation'=>'Recreation'
                , 'Retail'=>'Retail'
                , 'Shipping'=>'Shipping'
                , 'Technology'=>'Technology'
                , 'Telecommunications'=>'Telecommunications'
                , 'Transportation'=>'Transportation'
                , 'Utilities'=>'Utilities'
                , 'Other'=>'Other'
                ),
'leadsource_dom' => Array(''=>''
                , 'Cold Call'=>'Cold Call'
                , 'Existing Customer'=>'Existing Customer'
                , 'Self Generated'=>'Self Generated'
                , 'Employee'=>'Employee'
                , 'Partner'=>'Partner'
                , 'Public Relations'=>'Public Relations'
                , 'Direct Mail'=>'Direct Mail'
                , 'Conference'=>'Conference'
                , 'Trade Show'=>'Trade Show'
                , 'Web Site'=>'Web Site'
                , 'Word of mouth'=>'Word of mouth'
                , 'Other'=>'Other'
                ),
'leadstatus_dom' => Array(''=>''
                , 'Attempted to Contact'=>'Attempted to Contact'
                , 'Cold'=>'Cold'
                , 'Contact in Future'=>'Contact in Future'
                , 'Contacted'=>'Contacted'
                , 'Hot'=>'Hot'
                , 'Junk Lead'=>'Junk Lead'
                , 'Lost Lead'=>'Lost Lead'
                , 'Not Contacted'=>'Not Contacted'
                , 'Pre Qualified'=>'Pre Qualified'
                , 'Qualified'=>'Qualified'
                , 'Warm'=>'Warm'
                ),
'rating_dom' => Array(''=>''
                , 'Acquired'=>'Acquired'
                , 'Active'=>'Active'
                , 'Market Failed'=>'Market Failed'
                , 'Project Cancelled'=>'Project Cancelled'
                , 'Shutdown'=>'Shutdown'
                ),
'opportunity_type_dom' => Array(''=>''
                , 'Existing Business'=>'Existing Business'
                , 'New Business'=>'New Business'
                ),
'sales_stage_dom' => Array('Prospecting'=>'Prospecting'
                , 'Qualification'=>'Qualification'
                , 'Needs Analysis'=>'Needs Analysis'
                , 'Value Proposition'=>'Value Proposition'
                , 'Id. Decision Makers'=>'Id. Decision Makers'
                , 'Perception Analysis'=>'Perception Analysis'
                , 'Proposal/Price Quote'=>'Proposal/Price Quote'
                , 'Negotiation/Review'=>'Negotiation/Review'
                , 'Closed Won'=>'Closed Won'
                , 'Closed Lost'=>'Closed Lost'
                ),
'salutationtype_dom' => Array(''=>''
                , 'Mr.'=>'Mr.'
                , 'Ms.'=>'Ms.'
                , 'Mrs.'=>'Mrs.'
                , 'Dr.'=>'Dr.'
                , 'Prof.'=>'Prof.'
                ),
'eventstatus_dom' => Array('Planned'=>'Planned'
                , 'Held'=>'Held'
                , 'Not Held'=>'Not Held'
                ),
'taskstatus_dom' => Array('Not Started'=>'Not Started'
		, 'In Progress'=>'In Progress'
		, 'Completed'=>'Completed'
		, 'Pending Input'=>'Pending Input'
                , 'Deferred'=>'Deferred'
		, 'Planned'=>'Planned'
                ),
'taskpriority_dom' => Array('High'=>'High'
                ,'Medium'=>'Medium'
                ,'Low'=>'Low'
                ),
'duration_minutes_dom' => Array('00'=>'00'
                , '15'=>'15'
                , '30'=>'30'
                , '45'=>'45'
                ),
'productcategory_dom' => Array(''=>''
                , 'Hardware'=>'Hardware'
                , 'Software'=>'Software'
                , 'CRM Applications'=>'CRM Applications'
                ),
'manufacturer_dom' => Array(''=>''
                , 'AltvetPet Inc.'=>'AltvetPet Inc.'
                , 'LexPon Inc.'=>'LexPon Inc.'
                , 'MetBeat Corp'=>'MetBeat Corp'
                ),
'ticketcategories_dom' => Array('Big Problem'=>'Big Problem'
                                        ,'Small Problem'=>'Small Problem'
                                        ,'Other Problem'=>'Other Problem'
                                        ),
'ticketpriorities_dom' => Array('Low'=>'Low'
                                        ,'Normal'=>'Normal'
                                        ,'High'=>'High'
                                        ,'Urgent'=>'Urgent'
                                        ),
'ticketseverities_dom' => Array('Minor'=>'Minor'
                                        ,'Major'=>'Major'
                                        ,'Feature'=>'Feature'
                                        ,'Critical'=>'Critical'
                                        ),

'ticketstatus_dom' => Array('Open'=>'Open'
                                ,'In Progress'=>'In Progress'
                                ,'Wait For Response'=>'Wait For Response'
                                ,'Closed'=>'Closed'
				),

'activitytype_dom' => Array('Call'=>'Call'
		            , 'Meeting'=>'Meeting'
	        	   ),

'faqcategories_dom' => Array('General'=>'General'
				),

'faqstatus_dom' => Array('Draft'=>'Draft'
				,'Reviewed'=>'Reviewed'
				,'Published'=>'Published'
				,'Obsolete'=>'Obsolete'
				),


'currency_dom' => Array('Rupees'=>'Rupees',
                        'Dollar'=>'Dollar',
                        'Euro'=>'Euro'
                        ),

'visibility_dom' => Array('Private'=>'Private',
                          'Public'=>'Public'
			 ),
				     
'usageunit_dom' => Array('Box'=>'Box',
                        'Carton'=>'Carton',
                        'Dozen'=>'Dozen',
                        'Each'=>'Each',
                        'Hours'=>'Hours',
                        'Impressions'=>'Impressions',
                        'Lb'=>'Lb',
                        'M'=>'M',
                        'Pack'=>'Pack',
                        'Pages'=>'Pages',
                        'Pieces'=>'Pieces',
                        'Quantity'=>'Quantity',
                        'Reams'=>'Reams',
                        'Sheet'=>'Sheet',
                        'Spiral Binder'=>'Spiral Binder',
                        'Sq Ft'=>'Sq Ft'
                        ),

'glacct_dom' => Array('300-Sales-Software'=>'300-Sales-Software',
                        '301-Sales-Hardware'=>'301-Sales-Hardware',
                        '302-Rental-Income'=>'302-Rental-Income',
                        '303-Interest-Income'=>'303-Interest-Income',
                        '304-Sales-Software-Support'=>'304-Sales-Software-Support',
                        '305-Sales Other'=>'305-Sales Other',
                        '306-Internet Sales'=>'306-Internet Sales',
                        '307-Service-Hardware Labor'=>'307-Service-Hardware Labor',
                        '308-Sales-Books'=>'308-Sales-Books'
                        ),

'quotestage_dom' => Array('Created'=>'Created',
                        'Delivered'=>'Delivered',
                        'Reviewed'=>'Reviewed',
                        'Accepted'=>'Accepted',
                        'Rejected'=>'Rejected'
                        ),

'carrier_dom' => Array('FedEx'=>'FedEx',
                        'UPS'=>'UPS',
                        'USPS'=>'USPS',
                        'DHL'=>'DHL',
                        'BlueDart'=>'BlueDart'
                        ),

'taxclass_dom' => Array('SalesTax'=>'SalesTax',
                        'Vat'=>'Vat'
                        ),

'recurringtype_dom' => Array(''=>'',
			'Daily'=>'Daily',
			'Weekly'=>'Weekly',
			'Monthly'=>'Monthly',
			'Yearly'=>'Yearly'
			),

'invoicestatus_dom' => Array('AutoCreated'=>'AutoCreated',
			'Created'=>'Created',
			'Approved'=>'Approved',
			'Sent'=>'Sent',
			'Credit Invoice'=>'Credit Invoice',
			'Paid'=>'Paid'
			),

'postatus_dom' => Array('Created'=>'Created',
			'Approved'=>'Approved',
			'Delivered'=>'Delivered',
			'Cancelled'=>'Cancelled',
			'Received Shipment'=>'Received Shipment'
			),

'sostatus_dom' => Array('Created'=>'Created',
			'Approved'=>'Approved',
			'Delivered'=>'Delivered',
			'Cancelled'=>'Cancelled'
			),

'campaignstatus_dom' => Array(''=>'',
				'Planning'=>'Planning',
				'Active'=>'Active',
				'Inactive'=>'Inactive',
				'Completed'=>'Completed',
				'Cancelled'=>'Cancelled',							      ),
			

'campaigntype_dom' => Array(''=>'',
			    'Conference'=>'Conference',
			    'Webinar'=>'Webinar',
			    'Trade Show'=>'Trade Show',				    			    'Public Relations'=>'Public Relations',					    'Partners'=>'Partners',
			    'Referral Program'=>'Referral Program',
			    'Advertisement'=>'Advertisement',
			    'Banner Ads'=>'Banner Ads',
			    'Direct Mail'=>'Direct Mail',
			    'Email'=>'Email',
			    'Telemarketing'=>'Telemarketing',
			    'Others'=>'Others'
			    ),

'expectedresponse_dom' => Array(''=>'',
			      'Excellent'=>'Excellent',
			      'Good'=>'Good',
			      'Average'=>'Average',
                              'Poor'=>'Poor'
			      ),			      
'status_dom' => Array('Active'=>'Active',
			      'Inactive'=>'Inactive'
			      ),
'activity_view_dom' => Array('Today'=>'Today',
			      'This Week'=>'This Week',
			      'This Month'=>'This Month',
			      'This Year'=>'This Year'
		      ),
'lead_view_dom' => Array('Today'=>'Today',
			      'Last 2 Days'=>'Last 2 Days',
                              'Last Week'=>'Last Week'
		      ),
'date_format_dom' => Array('dd-mm-yyyy'=>'dd-mm-yyyy',
			      'mm-dd-yyyy'=>'mm-dd-yyyy',
                              'yyyy-mm-dd'=>'yyyy-mm-dd'
			      ),
'reminder_interval_dom' => Array('None'=>'None',
								'1 Minute'=>'1 Minute',
								'5 Minutes'=>'5 Minutes',
								'15 Minutes'=>'15 Minutes',
								'30 Minutes'=>'30 Minutes',
								'45 Minutes'=>'45 Minutes',
								'1 Hour'=>'1 Hour',
								'1 Day'=>'1 Day'
							),

'recurring_frequency_dom' => Array('--None--'=>'--None--',
								'Daily' => 'Daily',
								'Weekly' => 'Weekly',
								'Monthly' => 'Monthly',
								'Quarterly' => 'Quarterly',
								'Yearly' => 'Yearly'
							),		
'payment_duration_dom' => Array('Net 30 days'=>'Net 30 days',
								'Net 45 days'=>'Net 45 days',
								'Net 60 days'=>'Net 60 days'
							),      
'campaignrelstatus_dom' => Array('--None--'=>'--None--',
								'Contacted - Successful' => 'Contacted - Successful',
								'Contacted - Unsuccessful' => 'Contacted - Unsuccessful',
								'Contacted - Never Contact Again' => 'Contacted - Never Contact Again'
							),
'currency_grouping_pattern_dom' => Array('123,456,789' => '123,456,789',
										'123456789' => '123456789',
										'123456,789' => '123456,789',
										'12,34,56,789' => '12,34,56,789'
							),
'currency_decimal_separator_dom' => Array("." => ".",
										"," => ",",
										"'" => "'",
										" " => " ",
										"$" => "$"
							),
'currency_grouping_separator_dom' => Array("," => ",",
										"." => ".",
										"'" => "'",
										" " => " ",
										"$" => "$"
							),
'currency_symbol_placement_dom' => Array("$1.0" => "$1.0",
										"1.0$" => "1.0$"
							),
		
);

require_once('modules/Users/<USER>');
$arrayOfSupportedTimeZones = UserTimeZones::getAll();
$combo_strings['time_zone_dom'] = array_combine($arrayOfSupportedTimeZones,$arrayOfSupportedTimeZones);

?>


