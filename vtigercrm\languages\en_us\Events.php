<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/

/* NOTE: Should be inline with Calendar language translation but few variations */

$languageStrings = array(
	// Basic Strings
	'Events' => 'Events',
	'SINGLE_Events' => 'Event',
	'LBL_ADD_RECORD' => 'Add Event',
	'LBL_RECORDS_LIST' => 'Events List',
	'LBL_EVENTS' => 'Events',
	'LBL_TODOS' => 'Task',
	'LBL_HOLD_FOLLOWUP_ON' => 'Hold Followup on',
    'LBL_CREATE_FOLLOWUP_EVENT' => 'Create Follow Up Event',
	
	// Blocks
	'LBL_EVENT_INFORMATION' => 'Event Details',
	'LBL_RECURRENCE_INFORMATION' => 'Recurrence Details',
    'LBL_RELATED_TO' => 'Related To',
	
	//Fields
	'Start Date & Time'=>'Start Date & Time',
	'Recurrence' => 'Recurrence',
	'Send Notification' => 'Send Notification',
	'Location'=>'Location',
	'Send Reminder' => 'Send Email Reminder Before',
	'End Date & Time' => 'End Date & Time',
	'End Date' => 'End Date & Time',
    'Activity Type'=>'Activity Type',
	'Visibility' => 'Visibility',
	'Recurrence' => 'Repeat',
    'Related To' => 'Related To',
    
	//Visibility picklist values
	'Private' => 'Private',
	'Public' => 'Public',
	
	//Activity Type picklist values
	'Call' => 'Call',
	'Meeting' => 'Meeting',
	
	//Status picklist values
	'Planned' => 'Planned',
	'Held' => 'Held',
	'Not Held' => 'Not Held',
	
	//Reminder Labels
	'LBL_DAYS' => 'Days',
	'LBL_HOURS' => 'Hours',
	
	//Repeat Labels
	'LBL_DAYS_TYPE' => 'Day(s)',
	'LBL_WEEKS_TYPE' => 'Week(s)',
	'LBL_MONTHS_TYPE' => 'Month(s)',
	'LBL_YEAR_TYPE' => 'Year',
	
	'LBL_FIRST' => 'First',
	'LBL_LAST' => 'Last',
	
	'LBL_SM_SUN' => 'Sun',
	'LBL_SM_MON' => 'Mon',
	'LBL_SM_TUE' => 'Tue',
	'LBL_SM_WED' => 'Wed',
	'LBL_SM_THU' => 'Thr',
	'LBL_SM_FRI' => 'Fri',
	'LBL_SM_SAT' => 'Sat',
	
	'LBL_DAY0' => 'Sunday',
	'LBL_DAY1' => 'Monday',
	'LBL_DAY2' => 'Tuesday',
	'LBL_DAY3' => 'Wednesday',
	'LBL_DAY4' => 'Thursday',
	'LBL_DAY5' => 'Friday',
	'LBL_DAY6' => 'Saturday',
	
	'Daily'=>'Day(s)',
	'Weekly'=>'Week(s)',
	'Monthly'=>'Month(s)',
	'Yearly'=>'Year',
	
	'LBL_REPEATEVENT' => 'Once in every',
	'LBL_UNTIL' => 'Until',
	'LBL_DAY_OF_THE_MONTH' => 'day of the month',
	'LBL_ON' => 'on',
	
	'LBL_RECORDS_LIST' => 'List View',
	'LBL_CALENDAR_VIEW' => 'Calendar View',

    'LBL_INVITE_USER_BLOCK' => 'Invite',
    'LBL_INVITE_USERS' => 'Invite Users',
    'LBL_INVITE_PEOPLE' => 'Invite People',
	'INVITATION'=>' Invitation ',
	'Busy' => 'Busy',

);