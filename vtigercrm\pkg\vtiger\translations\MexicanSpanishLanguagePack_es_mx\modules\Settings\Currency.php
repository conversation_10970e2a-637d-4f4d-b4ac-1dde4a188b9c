<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD'               => 'Agregar moneda'                , 
	'LBL_EDIT_CURRENCY'            => 'Editar moneda'               , 
	'LBL_ADD_NEW_CURRENCY'         => 'Agregar nueva moneda'            , 
	'LBL_CURRENCY_NAME'            => 'Nombre de moneda'               , 
	'LBL_CURRENCY_CODE'            => 'Código'               , 
	'LBL_CURRENCY_SYMBOL'          => 'Símbolo'                      , 
	'LBL_CONVERSION_RATE'          => 'Tasa de conversión'             , 
	'LBL_ENTER_CONVERSION_RATE'    => 'Ingresar tasa de conversión'       , 
	'LBL_CURRENCY_STATUS_DESC'     => 'Activar la casilla para que que la moneda quede activada', 
	'LBL_TRANSFER_CURRENCY'        => 'Transferir moneda'           , 
	'LBL_CURRENT_CURRENCY'         => 'Moneda actual'            , 
	'Albania, Leke'                => 'Albania, Lek albanés'               , 
	'Argentina, Pesos'             => 'Argentina, Pesos'            , 
	'Aruba, Guilders'              => 'Aruba, Florín Arubiano'             , 
	'Australia, Dollars'           => 'Australia, Dólares'          , 
	'Azerbaijan, New Manats'       => 'Azerbaiyán, Manat'      , 
	'Bahamas, Dollars'             => 'Bahamas, Dólares'            , 
	'Bahrain, Dinar'               => 'Bahrain, Dinar bareiní'              , 
	'Barbados, Dollars'            => 'Barbados, Dólares'           , 
	'Belarus, Rubles'              => 'Bielorrusia, Rubles'             , 
	'Belize, Dollars'              => 'Belice, Dólares'             , 
	'Bermuda, Dollars'             => 'Bermuda, Dólares'            , 
	'Bolivia, Bolivianos'          => 'Bolivia, Bolivianos'         , 
	'Convertible Marka'            => 'Bosnia-herzegovina, Marco convertible'           , 
	'Botswana, Pulas'              => 'Botswana, Pulas'             , 
	'Bulgaria, Leva'               => 'Bulgaria, Lev'              , 
	'Brazil, Reais'                => 'Brasil, Real brasileño'               , 
	'Great Britain Pounds'         => 'Gran Bretaña, Libras'        , 
	'Brunei Darussalam, Dollars'   => 'Brunéi Darussalam, Dólares'  , 
	'Canada, Dollars'              => 'Canadá, Dólares'             , 
	'Cayman Islands, Dollars'      => 'Islas Caimán, Dólares'     , 
	'Chile, Pesos'                 => 'Chile, Pesos'                , 
	'Colombia, Pesos'              => 'Colombia, Pesos'             , 
	'Costa Rica, Colón'           => 'Costa Rica, Colón'          , 
	'Croatia, Kuna'                => 'Croacia, Kuna'               , 
	'Cuba, Pesos'                  => 'Cuba, Pesos'                 , 
	'Cyprus, Pounds'               => 'Chipre, Euros'              , 
	'Czech Republic, Koruny'       => 'República Checa, Corona'      , 
	'Denmark, Kroner'              => 'Dinamarca, Corona danesa'             , 
	'Dominican Republic, Pesos'    => 'República dominicana, Pesos'   , 
	'East Caribbean, Dollars'      => 'Caribe del Este, Dólares'     , 
	'Egypt, Pounds'                => 'Egipto, Libra egipcia'               , 
	'El Salvador, Colón'          => 'El Salvador, Colón'         , 
	'England, Pounds'              => 'Inglaterra, Libras'             , 
	'Estonia, Krooni'              => 'Estonia, Euros'             , 
	'Euro'                         => 'Euros'                        , 
	'Falkland Islands, Pounds'     => 'Islas Malvinas, Libras'    , 
	'Fiji, Dollars'                => 'Fiji, Dólares'               , 
	'Ghana, Cedis'                 => 'Ghana, Cedis'                , 
	'Gibraltar, Pounds'            => 'Gibraltar, Libras'           , 
	'Guatemala, Quetzales'         => 'Guatemala, Quetzales'        , 
	'Guernsey, Pounds'             => 'Guernsey, Libras'            , 
	'Guyana, Dollars'              => 'Guyana, Dólares'             , 
	'Honduras, Lempiras'           => 'Honduras, Lempiras'          , 
	'LvHong Kong, Dollars '        => 'Hong Kong, Dólares '       , 
	'Hungary, Forint'              => 'Hungría, Florín'             , 
	'Iceland, Krona'               => 'Islandia, Corona'              , 
	'India, Rupees'                => 'India, Rupias'               , 
	'Indonesia, Rupiahs'           => 'Indonesia, Rupias'          , 
	'Iran, Rials'                  => 'Irán, Riales'                 , 
	'Isle of Man, Pounds'          => 'Isla de Man, Libras'         , 
	'Israel, New Shekels'          => 'Israel, Nuevo sheqel'         , 
	'Jamaica, Dollars'             => 'Jamáica, Dólares'            , 
	'Japan, Yen'                   => 'Jaón, Yen'                  , 
	'Jersey, Pounds'               => 'Jersey, Libras'              , 
	'Kazakhstan, Tenge'            => 'Kazajistán, Tenge'           , 
	'Korea (North), Won'           => 'Corea (Norte), Won'          , 
	'Korea (South), Won'           => 'Corea (Sur), Won'          , 
	'Kyrgyzstan, Soms'             => 'Kuirguistán, Soms'            , 
	'Laos, Kips'                   => 'Laos, Kips'                  , 
	'Latvia, Lati'                 => 'Latvia, Euros'                , 
	'Lebanon, Pounds'              => 'Líbano, Libras'             , 
	'Liberia, Dollars'             => 'Liberia, Dólares'            , 
	'Switzerland Francs'           => 'Suiza, Francos'          , 
	'Lithuania, Litai'             => 'Lituania, Euros'            , 
	'Macedonia, Denars'            => 'Macedonia, Denars'           , 
	'Malaysia, Ringgits'           => 'Malasia, Ringgits'          , 
	'Malta, Liri'                  => 'Malta, Euros'                 , 
	'Mauritius, Rupees'            => 'República de Mauricio, Rupia'           , 
	'Mexico, Pesos'                => 'México, Pesos'               , 
	'Mongolia, Tugriks'            => 'Mongolia, Tugriks'           , 
	'Mozambique, Meticais'         => 'Mozambique, Meticais'        , 
	'Namibia, Dollars'             => 'Namibia, Dólares'            , 
	'Nepal, Rupees'                => 'Nepal, Rupias'               , 
	'Netherlands Antilles, Guilders' => 'Antillas holandesas, Florín antillano neerlandés', 
	'New Zealand, Dollars'         => 'Nueva Zelanda, Dólares'        , 
	'Nicaragua, Cordobas'          => 'Nicaragua, Córdobas'         , 
	'Nigeria, Nairas'              => 'Nigeria, Nairas'             , 
	'North Korea, Won'             => 'Corea del norte, Won'            , 
	'Norway, Krone'                => 'Noruega, Corona'               , 
	'Oman, Rials'                  => 'Oman, Riales'                 , 
	'Pakistan, Rupees'             => 'Pakistán, Rupias'            , 
	'Panama, Balboa'               => 'Panamá, Balboa'              , 
	'Paraguay, Guarani'            => 'Paraguay, Guaraní'           , 
	'Peru, Nuevos Soles'           => 'Perú, Nuevos Soles'          , 
	'Philippines, Pesos'           => 'Filipinas, Pesos'          , 
	'Poland, Zlotych'              => 'Plonia, Zloty'             , 
	'Qatar, Rials'                 => 'Qatar, Riyal'                , 
	'Romania, New Lei'             => 'Rumania, Leu'            , 
	'Russia, Rubles'               => 'Rusia, Rublos'              , 
	'Saint Helena, Pounds'         => 'Santa Elena, Libras'        , 
	'Saudi Arabia, Riyals'         => 'Arabia Saudita, Riyales'        , 
	'Serbia, Dinars'               => 'Serbia, Dinares'              , 
	'Seychelles, Rupees'           => 'Seychelles, Rupias'          , 
	'Singapore, Dollars'           => 'Singapur, Dólares'          , 
	'Solomon Islands, Dollars'     => 'Islas Salomón, Dólares'    , 
	'Somalia, Shillings'           => 'Somalia, Chelines'          , 
	'South Africa, Rand'           => 'Sudáfrica, Rand'          , 
	'South Korea, Won'             => 'Corea del sur, Won'            , 
	'Sri Lanka, Rupees'            => 'Sri Lanka, Rupias'           , 
	'Sweden, Kronor'               => 'Suecia, Corona'              , 
	'Switzerland, Francs'          => 'Suiza, Francos'         , 
	'Suriname, Dollars'            => 'Suriname, Dólares'           , 
	'Syria, Pounds'                => 'Siria, Libras'               , 
	'Taiwan, New Dollars'          => 'Taiwan, Nuevos dólares'         , 
	'Thailand, Baht'               => 'Tailandia, Baht'              , 
	'Trinidad and Tobago, Dollars' => 'Trinidad y Tobago, Dólares', 
	'Turkey, New Lira'             => 'Turquía, Nueva Lira'            , 
	'Turkey, Liras'                => 'Turquía, Liras'               , 
	'Tuvalu, Dollars'              => 'Tuvalu, Dólares'             , 
	'Ukraine, Hryvnia'             => 'Ucrania, Grivnas'            , 
	'United Kingdom, Pounds'       => 'Reino Unido, Libras'      , 
	'USA, Dollars'                 => 'EUA, Dólares'                , 
	'Uruguay, Pesos'               => 'Uruguay, Pesos'              , 
	'Uzbekistan, Sums'             => 'Uzbekistán, Soms'            , 
	'Venezuela, Bolivares Fuertes' => 'Venezuela, Bolivares Fuertes', 
	'Vietnam, Dong'                => 'Vietnam, Dong'               , 
	'Zimbabwe Dollars'             => 'Zimbabue, Dólares'            , 
	'China, Yuan Renminbi'         => 'China, Yuan Renminbi'        , 
	'Afghanistan, Afghanis'        => 'Afganistán, Afganí'       , 
	'Cambodia, Riels'              => 'Camboya, Riel'             , 
	'Jordan, Dinar'                => 'Jordania, Dinar'               ,
	'Kenya, Shilling'              => 'Kenia, Chelines'             ,
	'MADAGASCAR, Malagasy Ariary'  => 'Madagascar, Ariary Malgache' , 
	'United Arab Emirates, Dirham' => 'Emiratos Árabes Unidos, Dírham', 
	'United Republic of Tanzania, Shilling' => 'Tanzania, Chelines', 
	'Yemen, Rials'                 => 'Yemen, Riales'                , 
	'Zambia, Kwacha'               => 'Zambia, Kwacha'              , 
	'Malawi, kwacha'               => 'Malawi, Kwacha'              , 
	'Tunisian, Dinar'              => 'Tunez, Dinar'             , 
	'Moroccan, Dirham'             => 'Marruecos, Dirham'            , 
    
    'Currency Name' => 'Moneda',
    'Currency Code' => 'Código',
    'Symbol' => 'Símbolo',
);
$jsLanguageStrings = array(
	'JS_CURRENCY_DETAILS_SAVED'    => 'Detalles de la moneda guardados'      , 
	'JS_CURRENCY_DELETED_SUEESSFULLY' => 'Moneda borrada correctamente', 
);