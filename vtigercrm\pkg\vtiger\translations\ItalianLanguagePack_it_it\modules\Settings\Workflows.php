<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_NEW'                      => 'New'                         , // TODO: Review
	'LBL_WORKFLOW'                 => 'Workflow'                    , // TODO: Review
	'LBL_CREATING_WORKFLOW'        => 'Creating WorkFlow'           , // TODO: Review
	'LBL_NEXT'                     => 'Next'                        , // TODO: Review
	'LBL_STEP_1'                   => 'Step 1'                      , // TODO: Review
	'LBL_ENTER_BASIC_DETAILS_OF_THE_WORKFLOW' => 'Enter basic details of the Workflow', // TODO: Review
	'LBL_SPECIFY_WHEN_TO_EXECUTE'  => 'Specify when to execute this Workflow', // TODO: Review
	'ON_FIRST_SAVE'                => 'Only on the first save'      , // TODO: Review
	'ONCE'                         => 'Until the first time the condition is true', // TODO: Review
	'ON_EVERY_SAVE'                => 'Every time the record is saved', // TODO: Review
	'ON_MODIFY'                    => 'Every time a record is modified', // TODO: Review
	'MANUAL'                       => 'System'                      , // TODO: Review
	'SCHEDULE_WORKFLOW'            => 'Schedule Workflow'           , // TODO: Review
	'ADD_CONDITIONS'               => 'Add Conditions'              , // TODO: Review
	'ADD_TASKS'                    => 'Aggiungi Azioni'             ,
	'LBL_EXPRESSION'               => 'Expression'                  , // TODO: Review
	'LBL_FIELD_NAME'               => 'Field'                       , // TODO: Review
	'LBL_SET_VALUE'                => 'Set Value'                   , // TODO: Review
	'LBL_USE_FIELD'                => 'Use Field'                   , // TODO: Review
	'LBL_USE_FUNCTION'             => 'Use Function'                , // TODO: Review
	'LBL_RAW_TEXT'                 => 'Raw text'                    , // TODO: Review
	'LBL_ENABLE_TO_CREATE_FILTERS' => 'Enable to create Filters'    , // TODO: Review
	'LBL_CREATED_IN_OLD_LOOK_CANNOT_BE_EDITED' => 'This workflow was created in older look. Conditions created in older look cannot be edited. You can choose to recreate the conditions, or use the existing conditions without changing them.', // TODO: Review
	'LBL_USE_EXISTING_CONDITIONS'  => 'Use existing conditions'     , // TODO: Review
	'LBL_RECREATE_CONDITIONS'      => 'Recreate Conditions'         , // TODO: Review
	'LBL_SAVE_AND_CONTINUE'        => 'Save & Continue'             , // TODO: Review
	'LBL_ACTIVE'                   => 'Active'                      , // TODO: Review
	'LBL_TASK_TYPE'                => 'Tipo Azione'                 ,
	'LBL_TASK_TITLE'               => 'Azione Titolo'               ,
	'LBL_ADD_TASKS_FOR_WORKFLOW'   => 'Aggiungi azione per flusso di lavoro',
	'LBL_EXECUTE_TASK'             => 'Esegui Azione'               ,
	'LBL_SELECT_OPTIONS'           => 'Select Options'              , // TODO: Review
	'LBL_ADD_FIELD'                => 'Add Field'                   , // TODO: Review
	'LBL_ADD_TIME'                 => 'Add time'                    , // TODO: Review
	'LBL_TITLE'                    => 'Title'                       , // TODO: Review
	'LBL_PRIORITY'                 => 'Priority'                    , // TODO: Review
	'LBL_ASSIGNED_TO'              => 'Assigned to'                 , // TODO: Review
	'LBL_TIME'                     => 'Time'                        , // TODO: Review
	'LBL_DUE_DATE'                 => 'Due Date'                    , // TODO: Review
	'LBL_THE_SAME_VALUE_IS_USED_FOR_START_DATE' => 'The same value is used for the start date', // TODO: Review
	'LBL_EVENT_NAME'               => 'Event Name'                  , // TODO: Review
	'LBL_TYPE'                     => 'Type'                        , // TODO: Review
	'LBL_METHOD_NAME'              => 'Method Name'                 , // TODO: Review
	'LBL_RECEPIENTS'               => 'Recepients'                  , // TODO: Review
	'LBL_ADD_FIELDS'               => 'Add Fields'                  , // TODO: Review
	'LBL_SMS_TEXT'                 => 'Sms Text'                    , // TODO: Review
	'LBL_SET_FIELD_VALUES'         => 'Set Field Values'            , // TODO: Review
	'LBL_IN_ACTIVE'                => 'In Active'                   , // TODO: Review
	'LBL_SEND_NOTIFICATION'        => 'Send Notification'           , // TODO: Review
	'LBL_START_TIME'               => 'Start Time'                  , // TODO: Review
	'LBL_START_DATE'               => 'Start Date'                  , // TODO: Review
	'LBL_END_TIME'                 => 'End Time'                    , // TODO: Review
	'LBL_END_DATE'                 => 'End Date'                    , // TODO: Review
	'LBL_ENABLE_REPEAT'            => 'Enable Repeat'               , // TODO: Review
	'LBL_NO_METHOD_IS_AVAILABLE_FOR_THIS_MODULE' => 'No method is available for this module', // TODO: Review
	'LBL_FINISH'                   => 'Finish'                      , // TODO: Review
	'LBL_NO_TASKS_ADDED'           => 'Nessun Azioni'               ,
	'LBL_CANNOT_DELETE_DEFAULT_WORKFLOW' => 'You Cannot delete default Workflow', // TODO: Review
	'LBL_MODULES_TO_CREATE_RECORD' => 'Creare un record in'         ,
	'LBL_EXAMPLE_EXPRESSION'       => 'Expression'                  , // TODO: Review
	'LBL_EXAMPLE_RAWTEXT'          => 'Rawtext'                     , // TODO: Review
	'LBL_VTIGER'                   => 'Vtiger'                      , // TODO: Review
	'LBL_EXAMPLE_FIELD_NAME'       => 'Field'                       , // TODO: Review
	'LBL_NOTIFY_OWNER'             => 'notify_owner'                , // TODO: Review
	'LBL_ANNUAL_REVENUE'           => 'annual_revenue'              , // TODO: Review
	'LBL_EXPRESSION_EXAMPLE2'      => 'if mailingcountry == \'India\' then concat(firstname,\' \',lastname) else concat(lastname,\' \',firstname) end', // TODO: Review
	'LBL_FROM' => 'Da',
	'Optional' => 'Opzionale',
	'LBL_ADD_TASK'                 => 'Aggiungi azione'             ,
    'Portal Pdf Url' =>'Customer Portal collegamento Pdf',
    'LBL_ADD_TEMPLATE' => 'Aggiungi modello',
    'LBL_LINEITEM_BLOCK_GROUP' => 'Blocco LineItems per IVA di gruppo',
    'LBL_LINEITEM_BLOCK_INDIVIDUAL' => 'Blocco LineItems per fiscali',
    'LBL_ADD_PDF' => 'Aggiungi pdf',
    
	//Translation for module
	'Calendar' => 'Da fare',
	'Send Mail'					   => 'Send Mail',
	'Invoke Custom Function'	   => 'Richiamare funzione personalizzata',
	'Create Todo'				   => 'Creare Todo',
	'Create Event'				   => 'Crea evento',
	'Update Fields'				   => 'Aggiorna campi',
	'Create Entity'                => 'Creare Record'               ,
	'SMS Task'					   => 'SMS Task',
	'Mobile Push Notification'	   => 'Cellulare Push Notification',
	'LBL_ACTION_TYPE' => 'Tipo Azione (conteggio attivo)',
	'LBL_VTEmailTask' => 'Email',
    'LBL_VTEntityMethodTask' => 'Funzione personalizzata',
    'LBL_VTCreateTodoTask' => 'Compito',
    'LBL_VTCreateEventTask' => 'Evento',
    'LBL_VTUpdateFieldsTask' => 'Campo Aggiornamento',
    'LBL_VTSMSTask' => 'SMS', 
    'LBL_VTPushNotificationTask' => 'Notifica mobile',
    'LBL_VTCreateEntityTask' => 'Creare Record',
	'LBL_MAX_SCHEDULED_WORKFLOWS_EXCEEDED' => 'Numero massimo (%s) dei flussi di lavoro in programma è stato superato',

  'LBL_EDITING_WORKFLOW' => 'Flusso Di Lavoro Di Editing',
  'LBL_ADD_RECORD' => 'Nuovo Flusso Di Lavoro',
  'ON_SCHEDULE' => 'Pianificazione',
  'LBL_RUN_WORKFLOW' => 'Eseguire Il Flusso Di Lavoro',
  'LBL_AT_TIME' => 'Al Momento',
  'LBL_HOURLY' => 'Orari',
  'ENTER_FROM_EMAIL_ADDRESS' => 'Immettere un indirizzo di e-mail',
  'LBL_DAILY' => 'Quotidiano',
  'LBL_WEEKLY' => 'Settimanale',
  'LBL_ON_THESE_DAYS' => 'In questi giorni',
  'LBL_MONTHLY_BY_DATE' => 'Mensile dalla Data di',
  'LBL_MONTHLY_BY_WEEKDAY' => 'Mensile entro il giorno Feriale',
  'LBL_YEARLY' => 'Annuale',
  'LBL_SPECIFIC_DATE' => 'In Data Specifica',
  'LBL_CHOOSE_DATE' => 'Scegliere La Data',
  'LBL_SELECT_MONTH_AND_DAY' => 'Selezionare il Mese e la Data',
  'LBL_SELECTED_DATES' => 'Le Date Selezionate',
  'LBL_EXCEEDING_MAXIMUM_LIMIT' => 'Massimo ha superato il limite di',
  'LBL_NEXT_TRIGGER_TIME' => 'Il prossimo tempo di attivazione sul',
  'LBL_MESSAGE' => 'Messaggio',
  'LBL_WORKFLOW_NAME' => 'Nome Del Flusso Di Lavoro',
  'LBL_TARGET_MODULE' => 'Modulo Di Destinazione',
  'LBL_WORKFLOW_TRIGGER' => 'Flusso Di Lavoro Di Trigger',
  'LBL_TRIGGER_WORKFLOW_ON' => 'Trigger Di Flusso Di Lavoro',
  'LBL_RECORD_CREATION' => 'Creazione Di Record Di',
  'LBL_RECORD_UPDATE' => 'Aggiornamento Record',
  'LBL_TIME_INTERVAL' => 'Intervallo Di Tempo',
  'LBL_RECURRENCE' => 'La ricorrenza',
  'LBL_FIRST_TIME_CONDITION_MET' => 'Solo la prima volta che le condizioni sono soddisfatte',
  'LBL_EVERY_TIME_CONDITION_MET' => 'Ogni volta che le condizioni sono soddisfatte',
  'LBL_WORKFLOW_CONDITION' => 'Condizione Del Flusso Di Lavoro',
  'LBL_WORKFLOW_ACTIONS' => 'Azioni Del Flusso Di Lavoro',
  'LBL_DELAY_ACTION' => 'Ritardo Di Azione',
  'LBL_FREQUENCY' => 'Frequenza',
  'LBL_SELECT_FIELDS' => 'Selezionare I Campi',
  'LBL_INCLUDES_CREATION' => 'Comprende La Creazione',
  'LBL_ACTION_FOR_WORKFLOW' => 'Azione per il Flusso di lavoro',
  'LBL_WORKFLOW_SEARCH' => 'Ricerca per Nome',

);
$jsLanguageStrings = array(
	'JS_STATUS_CHANGED_SUCCESSFULLY' => 'Status changed Successfully' , // TODO: Review
	'JS_TASK_DELETED_SUCCESSFULLY' => 'Azione cancellato con successo',
	'JS_SAME_FIELDS_SELECTED_MORE_THAN_ONCE' => 'Same fields selected more than once', // TODO: Review
	'JS_WORKFLOW_SAVED_SUCCESSFULLY' => 'Workflow saved successfully' , // TODO: Review
    'JS_CHECK_START_AND_END_DATE'=>'Data e ora di fine deve essere maggiore o uguale a Data e ora inizio',

  'JS_TASK_STATUS_CHANGED' => 'Attività stato modificato correttamente.',
  'JS_WORKFLOWS_STATUS_CHANGED' => 'Flusso di lavoro di stato cambiato con successo.',
  'VTEmailTask' => 'Inviare Mail',
  'VTEntityMethodTask' => 'Richiamare La Funzione Personalizzata',
  'VTCreateTodoTask' => 'Creare Attività',
  'VTCreateEventTask' => 'Creare L\'Evento',
  'VTUpdateFieldsTask' => 'Aggiorna Campi',
  'VTSMSTask' => 'SMS Compito',
  'VTPushNotificationTask' => 'Mobile Di Notifica Push',
  'VTCreateEntityTask' => 'Creare Record',
  'LBL_EXPRESSION_INVALID' => 'Espressione Non Valida',

);