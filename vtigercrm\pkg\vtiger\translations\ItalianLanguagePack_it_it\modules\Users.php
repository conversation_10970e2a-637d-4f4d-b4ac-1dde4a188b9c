<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD'               => 'Aggiungi Utente'                    , 
	'LBL_MY_PREFERENCES'           => 'Le Mie Preferenze'           , 
	'LBL_MORE_INFORMATION'         => 'Più Informazioni'     , 
	'LBL_USERLOGIN_ROLE'           => 'Ruolo e Login Utente'        , 
	'LBL_USER_IMAGE_INFORMATION'   => 'Fotografia Utente'           , 
	'LBL_CURRENCY_CONFIGURATION'   => 'Configurazione Campi Valuta e Numeri', 
	'LBL_ADDRESS_INFORMATION'      => 'Indirizzo utente'            , 
	'LBL_USER_ADV_OPTIONS'         => 'Opzioni avanzate utente'     , 
	'Asterisk Configuration'       => 'Configurazione di Asterisk'  , 
	'LBL_HOME_PAGE_COMPONENTS'     => 'Componenti di Home Page'     , 
	'LBL_TAG_CLOUD_DISPLAY'        => 'Mostra Tag Cloud'           , // TODO: Review
	'Role'                         => 'Ruolo'                       , 
	'Admin'                        => 'Amministratore'              , 
	'User Name'                    => 'Nome Utente'                 , 
	'Default Activity View'		   => 'Predefinito MyCalendar View',
	'Default Calendar View'        => 'Vista Calendario di Default'            ,
	'Default Lead View'            => 'Vista Lead di Default'       , 
	'Title'                        => 'Titolo'                      , 
	'Office Phone'                 => 'Telefono Ufficio'            , 
	'Department'                   => 'Interno'                     , 
	'Reports To'                   => 'Riporta a'                   , 
	'Yahoo id'                     => 'Yahoo id'                    , 
	'Home Phone'                   => 'Telefono Casa'               , 
	'User Image'                   => 'Carica Fotografia'           , 
	'Date Format'                  => 'Formato Data'                , 
	'Tag Cloud'                    => 'Tag Cloud'                   , 
	'Signature'                    => 'Firma'                       , 
	'Street Address'               => 'Via'                         , 
	'Password'                     => 'Password'                    , 
	'Confirm Password'             => 'Conferma Password'           , 
	'LBL_SHOWN'                    => 'Mostrato'                    , 
	'LBL_HIDDEN'                   => 'Nascosto'                    , 
	'LBL_SHOW'                     => 'Mostra'                      , 
	'LBL_HIDE'                     => 'Nascondi'                    , 
	'LBL_HOME_PAGE_COMPO'          => 'Componenti di Home Page'     , 
	'LBL_LOGIN_HISTORY'            => 'Storico Login'               , 
	'LBL_USERDETAIL_INFO'          => 'Visualizzando i dettagli dell\'utente', 
	'LBL_DELETE_GROUP'             => 'Elimina Gruppo'              , 
	'LBL_DELETE_GROUPNAME'         => 'Gruppo da Eliminare'         , 
	'LBL_TRANSFER_GROUP'           => 'Trasferisci la Proprietà a:', 
	'LBL_DELETE_USER'              => 'Utente da Eliminare'         , 
	'LBL_TRANSFER_USER'            => 'Trasferisci la proprietà all\'Utente', 
	'LBL_DELETE_PROFILE'           => 'Elimina Profilo'             , 
	'LBL_TRANSFER_ROLES_TO_PROFILE' => 'Trasferisci Ruoli al Profilo', 
	'LBL_PROFILE_TO_BE_DELETED'    => 'Profilo da Eliminare'        , 
	'INTERNAL_MAIL_COMPOSER'       => 'Compositore Email interno'   , 
	'Asterisk Extension'           => 'Asterisk Extension'          , 
	' Receive Incoming Calls'      => 'Riceve Chiamate in Arrivo'      ,  
	'Reminder Interval'            => 'Intervallo Promemoria'       , 
	'Webservice Access Key'        => 'Password di accesso'         , 
	'Language'                     => 'Linguaggio:'                 , 
	'Theme'                        => 'Tema'                       , 
	'Time Zone'                    => 'Fuso orario'                   , 
	'Decimal Separator'            => 'Separatore Decimali'           , 
	'Digit Grouping Pattern'       => 'Modo raggruppamento Cifre'      , 
	'Digit Grouping Separator'     => 'Separatore cifre'    , 
	'Symbol Placement'             => 'Posizionamento Simbolo'            , 
	'Number Of Currency Decimals'  => 'Numero dei decimali per la valuta' , 
	'Truncate Trailing Zeros'      => 'Troncare zeri eccessivi'     , 
	'Default Call Duration'        => 'Durata standard telefonata (minuti)', // TODO: Review
	'Other Event Duration'         => 'Durata altri eventi (minuti)' ,  
	'Calendar Hour Format'         => 'Formato ore Calendario'        ,  
	'Kwajalein'                    => '(UTC-12:00) International Date Line West', 
	'Pacific/Midway'               => '(UTC-11:00) Coordinated Universal Time-11', 
	'Pacific/Samoa'                => '(UTC-11:00) Samoa'           , 
	'Pacific/Honolulu'             => '(UTC-10:00) Hawaii'          , 
	'America/Anchorage'            => '(UTC-09:00) Alaska'          , 
	'America/Los_Angeles'          => '(UTC-08:00) Pacific Time (US &amp; Canada)', 
	'America/Tijuana'              => '(UTC-08:00) Tijuana, Baja California', 
	'America/Denver'               => '(UTC-07:00) Mountain Time (US &amp; Canada)', 
	'America/Chihuahua'            => '(UTC-07:00) Chihuahua, La Paz, Mazatlan', 
	'America/Mazatlan'             => '(UTC-07:00) Mazatlan'        , 
	'America/Phoenix'              => '(UTC-07:00) Arizona'         , 
	'America/Regina'               => '(UTC-06:00) Saskatchewan'    , 
	'America/Tegucigalpa'          => '(UTC-06:00) Central America' , 
	'America/Chicago'              => '(UTC-06:00) Central Time (US &amp; Canada)', 
	'America/Mexico_City'          => '(UTC-06:00) Mexico City'     , 
	'America/Monterrey'            => '(UTC-06:00) Monterrey'       , 
	'America/New_York'             => '(UTC-05:00) Eastern Time (US &amp; Canada)', 
	'America/Bogota'               => '(UTC-05:00) Bogota, Lima, Quito', 
	'America/Lima'                 => '(UTC-05:00) Lima'            , 
	'America/Rio_Branco'           => '(UTC-05:00) Rio Branco'      , 
	'America/Indiana/Indianapolis' => '(UTC-05:00) Indiana (East)'  , 
	'America/Caracas'              => '(UTC-04:30) Caracas'         , 
	'America/Halifax'              => '(UTC-04:00) Atlantic Time (Canada)', 
	'America/Manaus'               => '(UTC-04:00) Manaus'          , 
	'America/Santiago'             => '(UTC-04:00) Santiago'        , 
	'America/La_Paz'               => '(UTC-04:00) La Paz'          , 
	'America/Cuiaba'               => '(UTC-04:00) Cuiaba'          , 
	'America/Asuncion'             => '(UTC-04:00) Asuncion'        , 
	'America/St_Johns'             => '(UTC-03:30) Newfoundland'    , 
	'America/Argentina/Buenos_Aires' => '(UTC-03:00) Buenos Aires'    , 
	'America/Sao_Paulo'            => '(UTC-03:00) Brasilia'        , 
	'America/Godthab'              => '(UTC-03:00) Greenland'       , 
	'America/Montevideo'           => '(UTC-03:00) Montevideo'      , 
	'Atlantic/South_Georgia'       => '(UTC-02:00) Mid-Atlantic'    , 
	'Atlantic/Azores'              => '(UTC-01:00) Azores'          , 
	'Atlantic/Cape_Verde'          => '(UTC-01:00) Cape Verde Is.'  , 
	'Europe/London'                => '(UTC) London, Edinburgh, Dublin, Lisbon', 
	'UTC'                          => '(UTC) Coordinated Universal Time, Greenwich Mean Time', 
	'Africa/Monrovia'              => '(UTC) Monrovia, Reykjavik'   , 
	'Africa/Casablanca'            => '(UTC) Casablanca'            , 
	'Europe/Belgrade'              => '(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague', 
	'Europe/Sarajevo'              => '(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb', 
	'Europe/Brussels'              => '(UTC+01:00) Brussels, Copenhagen, Madrid, Paris', 
	'Africa/Algiers'               => '(UTC+01:00) West Central Africa', 
	'Europe/Amsterdam'             => '(UTC+01:00) Amsterdam, Berlin, Bern, Roma, Rome, Stockholm, Vienna', 
	'Europe/Minsk'                 => '(UTC+02:00) Minsk'           , 
	'Africa/Cairo'                 => '(UTC+02:00) Cairo'           , 
	'Europe/Helsinki'              => '(UTC+02:00) Helsinki, Riga, Sofia, Tallinn, Vilnius', 
	'Europe/Athens'                => '(UTC+02:00) Athens, Bucharest', 
	'Europe/Istanbul'              => '(UTC+02:00) Istanbul'        , 
	'Asia/Jerusalem'               => '(UTC+02:00) Jerusalem'       , 
	'Asia/Amman'                   => '(UTC+02:00) Amman'           , 
	'Asia/Beirut'                  => '(UTC+02:00) Beirut'          , 
	'Africa/Windhoek'              => '(UTC+02:00) Windhoek'        , 
	'Africa/Harare'                => '(UTC+02:00) Harare'          , 
	'Asia/Kuwait'                  => '(UTC+03:00) Kuwait, Riyadh'  , 
	'Asia/Baghdad'                 => '(UTC+03:00) Baghdad'         , 
	'Africa/Nairobi'               => '(UTC+03:00) Nairobi'         , 
	'Asia/Tehran'                  => '(UTC+03:30) Tehran'          , 
	'Asia/Tbilisi'                 => '(UTC+04:00) Tbilisi'         , 
	'Europe/Moscow'                => '(UTC+03:00) Moscow, Volgograd', 
	'Asia/Muscat'                  => '(UTC+04:00) Abu Dhabi, Muscat', 
	'Asia/Baku'                    => '(UTC+04:00) Baku'            , 
	'Asia/Yerevan'                 => '(UTC+04:00) Yerevan'         , 
	'Asia/Karachi'                 => '(UTC+05:00) Islamabad, Karachi', 
	'Asia/Tashkent'                => '(UTC+05:00) Tashkent'        , 
	'Asia/Kolkata'                 => '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi', 
	'Asia/Colombo'                 => '(UTC+05:30) Sri Jayawardenepura', 
	'Asia/Katmandu'                => '(UTC+05:45) Kathmandu'       , 
	'Asia/Dhaka'                   => '(UTC+06:00) Dhaka'           , 
	'Asia/Almaty'                  => '(UTC+06:00) Almaty'          , 
	'Asia/Yekaterinburg'           => '(UTC+06:00) Ekaterinburg'    , 
	'Asia/Rangoon'                 => '(UTC+06:30) Yangon (Rangoon)', 
	'Asia/Novosibirsk'             => '(UTC+07:00) Novosibirsk'     , 
	'Asia/Bangkok'                 => '(UTC+07:00) Bangkok, Jakarta', 
	'Asia/Brunei'                  => '(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi', 
	'Asia/Krasnoyarsk'             => '(UTC+08:00) Krasnoyarsk'     , 
	'Asia/Ulaanbaatar'             => '(UTC+08:00) Ulaan Bataar'    , 
	'Asia/Kuala_Lumpur'            => '(UTC+08:00) Kuala Lumpur, Singapore', 
	'Asia/Taipei'                  => '(UTC+08:00) Taipei'          , 
	'Australia/Perth'              => '(UTC+08:00) Perth'           , 
	'Asia/Irkutsk'                 => '(UTC+09:00) Irkutsk'         , 
	'Asia/Seoul'                   => '(UTC+09:00) Seoul'           , 
	'Asia/Tokyo'                   => '(UTC+09:00) Tokyo'           , 
	'Australia/Darwin'             => '(UTC+09:30) Darwin'          , 
	'Australia/Adelaide'           => '(UTC+09:30) Adelaide'        , 
	'Australia/Canberra'           => '(UTC+10:00) Canberra, Melbourne, Sydney', 
	'Australia/Brisbane'           => '(UTC+10:00) Brisbane'        , 
	'Australia/Hobart'             => '(UTC+10:00) Hobart'          , 
	'Asia/Vladivostok'             => '(UTC+10:00) Vladivostok'     , 
	'Pacific/Guam'                 => '(UTC+10:00) Guam, Port Moresby', 
	'Asia/Yakutsk'                 => '(UTC+10:00) Yakutsk'         , 
	'Etc/GMT-11'				   => '(UTC+11:00) Solomon Is., New Caledonia',
	'Pacific/Fiji'                 => '(UTC+12:00) Fiji'            , 
	'Asia/Kamchatka'               => '(UTC+12:00) Kamchatka'       , 
	'Pacific/Auckland'             => '(UTC+12:00) Auckland'        , 
	'Asia/Magadan'                 => '(UTC+12:00) Magadan'         , 
	'Pacific/Tongatapu'            => '(UTC+13:00) Nukualofa'       , 
	'Summary'                      => 'Sommario'                     ,  
	'Detail'                       => 'Dettaglio'                      , 
	'LBL_USER_LIST_DETAILS'        => 'Dettagli'                     ,  
	'LBL_USER_DELETED_SUCCESSFULLY' => 'Utente cancellato con successo',
    'LBL_ACTIVE_USERS' => 'Utenti attivi',
    'LBL_INACTIVE_USERS' => 'Gli utenti inattivi',
    'LBL_DELETE_USER_PERMANENTLY' => 'Elimina utente in modo permanente',
    'LBL_RESTORE' => 'Ripristinare',
    'LBL_USER_RESTORED_SUCCESSFULLY' => 'Utente ripristinato con successo',
	'LBL_ALMOST_THERE'	=>	'Ci siamo quasi!',
	'LBL_ABOUT_ME'		=>	'Il mio profilo',
	'LBL_WE_PROMISE_TO_KEEP_THIS_PRIVATE'	=>	'(Ci impegniamo a mantenere questo privato)',
	'LBL_ALL_FIELDS_BELOW_ARE_REQUIRED'		=>	'(Tutti i campi sottostanti sono obbligatori)',
	'LBL_GET_STARTED'	=> 'Per Iniziare',
	'LBL_YOUR_CONTACT_NUMBER' => 'Il tuo numero di Contatto',
	'LBL_WHERE_ARE_YOU_FROM' =>	'Di dove sei?',
	'LBL_SELECT_COUNTRY'	=> 'Seleziona un Paese',
	'LBL_COMPANY_SIZE'		=> 'Dimensioni della società',
	'LBL_JOB_TITLE'			=> 'Professione',
	'LBL_DEPARTMENT'		=> 'Dipartimento',
	'LBL_BASE_CURRENCY'		=> 'Valuta di Base',
	'LBL_CHOOSE_BASE_CURRENCY'	=> 'Scegliere Valuta di Base',
	'LBL_OPERATING_CURRENCY'	=> 'Valuta di riferimento non può essere modificato successivamente. Seleziona la tua valuta operativa',
	'LBL_LANGUAGE' => 'Lingua',
	'LBL_CHOOSE_LANGUAGE'	=> 'Scegli la lingua',
	'LBL_CHOOSE_TIMEZONE'	=> 'Scegli Fuso orario',
	'LBL_DATE_FORMAT'		=> 'Formato Data',
	'LBL_CHOOSE_DATE_FORMAT'=> 'Scegli il formato Data',
	'LBL_PHONE'	=> 'Telefono',
    'Space' => 'Spazio',
	//picklist values for Default Calendar View field in MyPreference Page
	'ListView' => 'Lista Viste',
	'MyCalendar' => 'Il mio calendario',
	'SharedCalendar' => 'Calendario condiviso',
    
    'LBL_CHANGE_OWNER' => 'Cambia proprietario',
    'LBL_TRANSFER_OWNERSHIP' => 'Trasferimento della proprietà',
    'LBL_TRANSFER_OWNERSHIP_TO_USER' => 'Trasferire la proprietà a utente',
    'LBL_OWNERSHIP_TRANSFERRED_SUCCESSFULLY' => 'Proprietario CRM cambiato con successo',
    'LBL_OWNERSHIP_TRANSFERRED_FAILED' => 'Impossibile cambiare proprietario CRM',
    'Account Owner' => 'Account Proprietario',
    'Starting Day of the week' => 'Primo Giorno della settimana',
    'Day starts at' => 'Il Giorno inizia alle',
    'Default Event Status' => 'Stato Predefinito per Eventi',
    'Default Activity Type' => 'Tipo Predefinito per Attività',
    'Default Record View' => 'Record Predefinito per View',
    'Left Panel Hide' => 'Nascondi Pannello a sinistra',
    'Row Height' => 'Altezza riga',
	'LBL_RESTORE_USER_FAILED' => 'Impossibile ripristinare utente. Esiste già un utente CRM con questo nome utente.',   
    'LBL_DUPLICATE_USER_EXISTS' => 'Utente già esistente',
    'LBL_CHANGE_PASSWORD' => 'Cambiare La Password',


	'LBL_CHANGE_USERNAME'          => 'Cambia nome utente'          ,
	'LBL_USERNAME_CHANGED'         => 'Nome utente modificato correttamente',
	'ERROR_CHANGE_USERNAME'        => 'Errore nel cambiamento nome utente. Si prega di riprovare più tardi',

  'LBL_REMOVE_USER' => 'Eliminare',
  'LBL_MORE_OPTIONS' => 'Più Opzioni',
  'LBL_RESTORE_USER' => 'Ripristino Configurazione Di Utente',
  'LBL_OLD_PASSWORD' => 'Vecchia Password',
  'LBL_NEW_PASSWORD' => 'Nuova Password',
  'LBL_CONFIRM_PASSWORD' => 'Conferma Password',
	'LBL_CHANGE_ACCESS_KEY' => 'Cambio Accessibilità',
	'LBL_ACCESS_KEY_UPDATED_SUCCESSFULLY' => 'Chiave di accesso aggiornato con successo',
	'LBL_FAILED_TO_UPDATE_ACCESS_KEY' => 'Impossibile aggiornare chiave di accesso',
  'LBL_LOGIN_AS' => 'Il Login ',
  'LBL_CREATE_USER' => 'Creare Utente',
  'LBL_DELETE_USER_PERMANENTLY_INFO' => 'Eliminazione di un utente in modo permanente il trasferimento di tutti i record compresi i commenti e la storia sono un nuovo utente.',
  'LBL_TO_CRM' => 'Login per Vtiger CRM',
  'LBL_INVALID_USER_OR_PASSWORD' => 'Nome utente o la password non validi.',
  'LBL_INVALID_USER_OR_EMAIL' => 'Non valido nome utente o indirizzo Email.',
  'LBL_EMAIL_SEND' => 'Abbiamo inviato una email per reimpostare la password.',
  'ForgotPassword' => 'Hai Dimenticato La Password?',
  'LBL_CONNECT_WITH_US' => 'Connettiti con NOI',
  'LBL_GET_MORE' => 'Ottenere di più da Vtiger',
  'LBL_TRANSFER_RECORDS_TO_USER' => 'Trasferire i record utente',
  'LBL_USER_TO_BE_DELETED' => 'Utente Cancellato',
  'LBL_USERS_SETTINGS' => 'IMPOSTAZIONI DEGLI UTENTI',
  'LBL_TEMPLATES' => 'Modelli',

);
$jsLanguageStrings = array(
		
	//Curency seperator validation messages
	'JS_ENTER_OLD_PASSWORD'=>'Per favore inserire la tua vecchia password.',
	'JS_ENTER_NEW_PASSWORD'=>'Per favore inserire la tua nuova new password.',
	'JS_ENTER_CONFIRMATION_PASSWORD'=>'Per favore inserisci la tua password di conferma.',
	'JS_REENTER_PASSWORDS'=>'Per favore inserisce nuovamente le passowrd. La nuova password e quella di conferma non coincidono.',
	'JS_INVALID_PASSWORD'=>'Devi specificare un nome utente e una password validi.',
	'JS_PASSWORD_CHANGE_FAILED_1'=>'Il cambio password per l\'utente è fallito a causa di ',
	'JS_PASSWORD_CHANGE_FAILED_2'=>' fallito. La nuova password deve essere reimpostata.',
	'JS_PASSWORD_INCORRECT_OLD'=>'Vecchia password errata. Reinserire le informazioni delle password.',
	'JS_ENTERED_CURRENT_USERNAME_MSG' => 'Hai inserito il nome utente corrente. Inserisci il nuovo nome utente.',
	'JS_NEW_ACCESS_KEY_REQUESTED' => 'nuova chiave di accesso richiesto',
	'JS_CHANGE_ACCESS_KEY_CONFIRMATION' => 'Hai richiesto per una nuova chiave di accesso. <br><br>Con la nuova chiave di accesso fornita, è necessario sostituire la chiave di accesso vecchia con quella nuova in tutte le estensioni installate. <br><br>Si desidera continuare?',
);