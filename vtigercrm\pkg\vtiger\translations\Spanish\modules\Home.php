<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * Portions created by JPL TSolucio, S.L. are Copyright (C) jpl tsolucio.
 * All Rights Reserved.
 * ********************************************************************************
 *  Language     : Español es_es
 *  Version      : 6.0.0
 *  Created Date : 2012-10-25
 *  Author       : JPL <PERSON>Solucio, S. L. <PERSON>
 *  Last change  : 2012-10-25
 *  Author       : JPL TSolucio, S. L. <PERSON>
 ************************************************************************************/
$languageStrings = array(
	'ALVT'                         => 'Cuentas Destacadas',
	'PLVT'                         => 'Oportunidades Destacadas',
	'QLTQ'                         => 'Presupuestos Destacados',
	'CVLVT'                        => 'Medidas Clave',
	'HLT'                          => 'Incidencias Destacadas',
	'GRT'                          => 'Asignación de Grupo',
	'OLTSO'                        => 'Orden Venta Destacadas',
	'ILTI'                         => 'Facturas Destacadas',
	'HDB'                          => 'Cuadro Mando Inicio',
	'OLTPO'                        => 'Orden Compra Destacadas',
	'LTFAQ'                        => 'FAQs Recientes',
	'UA'                           => 'Próximas Actividades',
	'PA'                           => 'Actividades Pendientes',
        'Home'                         => 'Tablero'                      ,

  'LBL_SAVE_ORDER' => 'Guardar El Fin De',
  'LBL_ADD_NEW_DASHBOARD' => 'Añadir nuevo panel de control',
	'LBL_MAX_CHARACTERS_ALLOWED_DASHBOARD' => 'Máximo 30 caracteres están permitidos para el nombre salpicadero.',
);

$jsLanguageStrings = array(
	'JS_TAB_NAME_SHOULD_NOT_BE_EMPTY' => 'Nombre del salpicadero no no puede estar vacía',
	'JS_NO_DATA_AVAILABLE' => 'Datos no disponibles',
);