<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'LBL_TRANSFORM_OWNERSHIP' => 'Transfer ownership',
	'SINGLE_Groups' => 'Group',
	'LBL_TO_OTHER_GROUP' => 'To Other Group ',
	'LBL_ADD_RECORD' => 'Add Group',
	'LBL_GROUP_NAME' => 'Group Name',
	'LBL_GROUP_MEMBERS' => 'Group Members',
	'LBL_ROLEANDSUBORDINATE' => 'Role and Subordinates',
	'RoleAndSubordinates' => 'Role and Subordinates',
	'LBL_DUPLICATES_EXIST' => 'Group Name already Exists',
);
$jsLanguageStrings = array(
	'JS_PLEASE_SELECT_ATLEAST_ONE_MEMBER_FOR_A_GROUP' => 'Please select atleast one member for a group',
	'JS_RECORD_DELETED_SUCCESSFULLY' => 'Group deleted successfully',
	'JS_COMMA_NOT_ALLOWED_GROUP' => 'Special characters like ,"<> are not allowed in Group Name.',
);

