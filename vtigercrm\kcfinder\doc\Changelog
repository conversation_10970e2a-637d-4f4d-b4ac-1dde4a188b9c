VERSION 2.21 - 2010-11-19
-------------------------
* Bugfixes only


VERSION 2.2 - 2010-07-27
------------------------
* Many bugfixes
* Read-only config option


VERSION 2.1 - 2010-07-04
------------------------
* Endless JavaScript loop on KCFinder disable bugfix
* New config setting whether to generate .htaccess file in upload folder
* Upload to specified folder from CKEditor & FCKeditor direct upload dialog
* Select multiple files bugfixes


VERSION 2.0 - 2010-07-01
------------------------
* Brand new core
* Ability to resize files/folders panels with mouse drag
* Select multiple files with Ctrl key
* Return list of files to custom integrating application
* Animated folder tree
* Directory Type specific configuration settings
* Download multiple files or a folder as ZIP file


VERSION 1.7 - 2010-06-17
------------------------
* Maximize toolbar button
* Clipboard for copying and moving multiple files
* Show warning if the browser is not capable to display KCFinder
* Google Chrome Frame support for old versions of Internet Explorer


VERSION 1.6 - 2010-06-02
------------------------
* Support of Windows Apache server
* Support of Fileinfo PHP extension to detect mime types (*mime directory type)
* Ability to deny globaly some dangerous extensions like exe, php, pl, cgi etc
* Check for denied file extension on file rename
* Disallow to upload hidden files (with names begins with .)
* Missing last character of filenames without extension bugfix
* Some small bugfixes


VERSION 1.5 - 2010-05-30
------------------------
* Filenames with spaces download bugfix
* FCKEditor direct upload bugfix
* Thumbnail generation bugfixes


VERSION 1.4 - 2010-05-24
------------------------
* Client-side caching bugfix
* Custom integrations - window.KCFinder.callBack()
* Security fixes


VERSION 1.3 - 2010-05-06
------------------------
* Another session bugfix. Now session configuratin works!
* Show filename by default bugfix
* Loading box on top right corner


VERSION 1.2 - 2010-05-03
------------------------
* Thumbnail generation bugfix
* Session bugfix
* other small bugfixes
