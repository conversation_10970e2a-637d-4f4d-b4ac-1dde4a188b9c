<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_ADD_RECORD'               => 'Добавить пользователя'                    , 
	'LBL_MY_PREFERENCES'           => 'Мои Настройки'   , 
	'LBL_MORE_INFORMATION'         => 'Дополнительная Информации', 
	'LBL_USERLOGIN_ROLE'           => 'Учетная Запись и Роль Пользователя', 
	'LBL_USER_IMAGE_INFORMATION'   => 'Фотография Пользователя', 
	'LBL_CURRENCY_CONFIGURATION'   => 'Настройка валюты', 
	'LBL_ADDRESS_INFORMATION'      => 'Адрес'                  , 
	'LBL_USER_ADV_OPTIONS'         => 'Дополнительные Опции Пользователя', 
	'Asterisk Configuration'       => 'Конфигурация Asterisk', 
	'LBL_HOME_PAGE_COMPONENTS'     => 'Компоненты Главной Страницы', // KEY 5.x: LBL_HOME_PAGE_COMP
	'LBL_TAG_CLOUD_DISPLAY'        => 'Отображение Облака Тегов', // KEY 5.x: LBL_TAGCLOUD_DISPLAY
	'Role'                         => 'Роль'                    , 
	'Admin'                        => 'Администратор'  , 
	'User Name'                    => 'Пользователь'    , 
	'Default Activity View'		   => 'По умолчанию Мой календарь Вид',
	'Default Calendar View'        => 'Вид Календаря по умолчанию'            ,
	'Default Lead View'            => 'Вид Обращения по умолчанию', 
	'Title'                        => 'Должность'          , 
	'Office Phone'                 => 'Тел.'                     , 
	'Department'                   => 'Отдел'                  , 
	'Reports To'                   => 'Начальник'          , 
	'Yahoo id'                     => 'Yahoo id'                    , 
	'Home Phone'                   => 'Тел. дом.'             , 
	'User Image'                   => 'Фотография'        , 
	'Date Format'                  => 'Формат даты'       , 
	'Tag Cloud'                    => 'Облако Тегов'     , 
	'Signature'                    => 'Подпись'              , 
	'Street Address'               => 'Адрес'                  , 
	'Password'                     => 'Пароль'                , 
	'Confirm Password'             => 'Подтверждение Пароля', 
	'LBL_SHOWN'                    => 'Отображено'        , 
	'LBL_HIDDEN'                   => 'Скрыто'                , 
	'LBL_SHOW'                     => 'Показать'            , 
	'LBL_HIDE'                     => 'Скрыть'                , 
	'LBL_HOME_PAGE_COMPO'          => 'Компоненты Главной Страницы', // KEY 5.x: LBL_HOME_PAGE_COMP
	'LBL_LOGIN_HISTORY'            => 'Журнал Регистрации в Системе', 
	'LBL_USERDETAIL_INFO'          => 'Просмотр данных о Пользователе', 
	'LBL_DELETE_GROUP'             => 'Удалить Группу' , 
	'LBL_DELETE_GROUPNAME'         => 'Группа для Удаления', 
	'LBL_TRANSFER_GROUP'           => 'Перенести Право Собственности для: ', 
	'LBL_DELETE_USER'              => 'Пользователь для Удаления', 
	'LBL_TRANSFER_USER'            => 'Перенести Право Собственности Пользователю', 
	'LBL_DELETE_PROFILE'           => 'Удалить Профиль', 
	'LBL_TRANSFER_ROLES_TO_PROFILE' => 'Перенести Роли в Профиль', 
	'LBL_PROFILE_TO_BE_DELETED'    => 'Профиль для Удаления', 
	'INTERNAL_MAIL_COMPOSER'       => 'Встроенная эл.почта', 
	'Asterisk Extension'           => 'Внутренний телефонный номер', 
	' Receive Incoming Calls'      => 'Получать Входящие Звонки', 
	'Reminder Interval'            => 'Интервал Оповещения', 
	'Webservice Access Key'        => 'Ключ Доступа'     , 
	'Language'                     => 'Язык'                    , 
	'Theme'                        => 'Тема Оформления', 
	'Time Zone'                    => 'Часовой Пояс'     , 
	'Decimal Separator'            => 'Десятичный Разделитель', 
	'Digit Grouping Pattern'       => 'Шаблон Группировки Разрядов', 
	'Digit Grouping Separator'     => 'Разделитель Группировки Разрядов', 
	'Symbol Placement'             => 'Расположение Символа', 
	'Number Of Currency Decimals'  => 'Количество валютной Десятичные' , 
	'Truncate Trailing Zeros'      => 'Обрезать конечные нули'     , 
	'Default Call Duration'        => 'По умолчанию Продолжительность вызова (Мин)', 
	'Other Event Duration'         => 'Другое длительность события (Мин)' , 
	'Calendar Hour Format'         => 'Календарь часовой формат'        , 
	'Kwajalein'                    => '(UTC-12:00) International Date Line West', 
	'Pacific/Midway'               => '(UTC-11:00) Coordinated Universal Time-11', 
	'Pacific/Samoa'                => '(UTC-11:00) Samoa'           , 
	'Pacific/Honolulu'             => '(UTC-10:00) Hawaii'          , 
	'America/Anchorage'            => '(UTC-09:00) Alaska'          , 
	'America/Los_Angeles'          => '(UTC-08:00) Pacific Time (US &amp; Canada)', 
	'America/Tijuana'              => '(UTC-08:00) Tijuana, Baja California', 
	'America/Denver'               => '(UTC-07:00) Mountain Time (US &amp; Canada)', 
	'America/Chihuahua'            => '(UTC-07:00) Chihuahua, La Paz, Mazatlan', 
	'America/Mazatlan'             => '(UTC-07:00) Mazatlan'        , 
	'America/Phoenix'              => '(UTC-07:00) Arizona'         , 
	'America/Regina'               => '(UTC-06:00) Saskatchewan'    , 
	'America/Tegucigalpa'          => '(UTC-06:00) Central America' , 
	'America/Chicago'              => '(UTC-06:00) Central Time (US &amp; Canada)', 
	'America/Mexico_City'          => '(UTC-06:00) Mexico City'     , 
	'America/Monterrey'            => '(UTC-06:00) Monterrey'       , 
	'America/New_York'             => '(UTC-05:00) Eastern Time (US &amp; Canada)', 
	'America/Bogota'               => '(UTC-05:00) Bogota, Lima, Quito', 
	'America/Lima'                 => '(UTC-05:00) Lima'            , 
	'America/Rio_Branco'           => '(UTC-05:00) Rio Branco'      , 
	'America/Indiana/Indianapolis' => '(UTC-05:00) Indiana (East)'  , 
	'America/Caracas'              => '(UTC-04:30) Caracas'         , 
	'America/Halifax'              => '(UTC-04:00) Atlantic Time (Canada)', 
	'America/Manaus'               => '(UTC-04:00) Manaus'          , 
	'America/Santiago'             => '(UTC-04:00) Santiago'        , 
	'America/La_Paz'               => '(UTC-04:00) La Paz'          , 
	'America/Cuiaba'               => '(UTC-04:00) Cuiaba'          , 
	'America/Asuncion'             => '(UTC-04:00) Asuncion'        , 
	'America/St_Johns'             => '(UTC-03:30) Newfoundland'    , 
	'America/Argentina/Buenos_Aires' => '(UTC-03:00) Buenos Aires'    , 
	'America/Sao_Paulo'            => '(UTC-03:00) Brasilia'        , 
	'America/Godthab'              => '(UTC-03:00) Greenland'       , 
	'America/Montevideo'           => '(UTC-03:00) Montevideo'      , 
	'Atlantic/South_Georgia'       => '(UTC-02:00) Mid-Atlantic'    , 
	'Atlantic/Azores'              => '(UTC-01:00) Azores'          , 
	'Atlantic/Cape_Verde'          => '(UTC-01:00) Cape Verde Is.'  , 
	'Europe/London'                => '(UTC) London, Edinburgh, Dublin, Lisbon', 
	'UTC'                          => '(UTC) Coordinated Universal Time, Greenwich Mean Time', 
	'Africa/Monrovia'              => '(UTC) Monrovia, Reykjavik'   , 
	'Africa/Casablanca'            => '(UTC) Casablanca'            , 
	'Europe/Belgrade'              => '(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague', 
	'Europe/Sarajevo'              => '(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb', 
	'Europe/Brussels'              => '(UTC+01:00) Brussels, Copenhagen, Madrid, Paris', 
	'Africa/Algiers'               => '(UTC+01:00) West Central Africa', 
	'Europe/Amsterdam'             => '(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna', 
	'Europe/Minsk'                 => '(UTC+02:00) Минск'      , 
	'Africa/Cairo'                 => '(UTC+02:00) Cairo'           , 
	'Europe/Helsinki'              => '(UTC+02:00) Helsinki, Riga, Sofia, Tallinn, Vilnius', 
	'Europe/Athens'                => '(UTC+02:00) Athens, Bucharest', 
	'Europe/Istanbul'              => '(UTC+02:00) Istanbul'        , 
	'Asia/Jerusalem'               => '(UTC+02:00) Jerusalem'       , 
	'Asia/Amman'                   => '(UTC+02:00) Amman'           , 
	'Asia/Beirut'                  => '(UTC+02:00) Beirut'          , 
	'Africa/Windhoek'              => '(UTC+02:00) Windhoek'        , 
	'Africa/Harare'                => '(UTC+02:00) Harare'          , 
	'Asia/Kuwait'                  => '(UTC+03:00) Kuwait, Riyadh'  , 
	'Asia/Baghdad'                 => '(UTC+03:00) Baghdad'         , 
	'Africa/Nairobi'               => '(UTC+03:00) Nairobi'         , 
	'Asia/Tehran'                  => '(UTC+03:30) Tehran'          , 
	'Asia/Tbilisi'                 => '(UTC+04:00) Tbilisi'         , 
	'Europe/Moscow'                => '(UTC+03:00) Москва, Волгоград', 
	'Asia/Muscat'                  => '(UTC+04:00) Abu Dhabi, Muscat', 
	'Asia/Baku'                    => '(UTC+04:00) Baku'            , 
	'Asia/Yerevan'                 => '(UTC+04:00) Yerevan'         , 
	'Asia/Karachi'                 => '(UTC+05:00) Islamabad, Karachi', 
	'Asia/Tashkent'                => '(UTC+05:00) Tashkent'        , 
	'Asia/Kolkata'                 => '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi', 
	'Asia/Colombo'                 => '(UTC+05:30) Sri Jayawardenepura', 
	'Asia/Katmandu'                => '(UTC+05:45) Kathmandu'       , 
	'Asia/Dhaka'                   => '(UTC+06:00) Dhaka'           , 
	'Asia/Almaty'                  => '(UTC+06:00) Almaty'          , 
	'Asia/Yekaterinburg'           => '(UTC+06:00) Екатеринбург', 
	'Asia/Rangoon'                 => '(UTC+06:30) Yangon (Rangoon)', 
	'Asia/Novosibirsk'             => '(UTC+07:00) Новосибирск', 
	'Asia/Bangkok'                 => '(UTC+07:00) Bangkok, Jakarta', 
	'Asia/Brunei'                  => '(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi', 
	'Asia/Krasnoyarsk'             => '(UTC+08:00) Красноярск', 
	'Asia/Ulaanbaatar'             => '(UTC+08:00) Ulaan Bataar'    , 
	'Asia/Kuala_Lumpur'            => '(UTC+08:00) Kuala Lumpur, Singapore', 
	'Asia/Taipei'                  => '(UTC+08:00) Taipei'          , 
	'Australia/Perth'              => '(UTC+08:00) Perth'           , 
	'Asia/Irkutsk'                 => '(UTC+09:00) Иркутск'  , 
	'Asia/Seoul'                   => '(UTC+09:00) Seoul'           , 
	'Asia/Tokyo'                   => '(UTC+09:00) Tokyo'           , 
	'Australia/Darwin'             => '(UTC+09:30) Darwin'          , 
	'Australia/Adelaide'           => '(UTC+09:30) Adelaide'        , 
	'Australia/Canberra'           => '(UTC+10:00) Canberra, Melbourne, Sydney', 
	'Australia/Brisbane'           => '(UTC+10:00) Brisbane'        , 
	'Australia/Hobart'             => '(UTC+10:00) Hobart'          , 
	'Asia/Vladivostok'             => '(UTC+10:00) Владивосток', 
	'Pacific/Guam'                 => '(UTC+10:00) Guam, Port Moresby', 
	'Asia/Yakutsk'                 => '(UTC+10:00) Якутск'    , 
	'Etc/GMT-11'				   => '(UTC+11:00) Solomon Is., New Caledonia',
	'Pacific/Fiji'                 => '(UTC+12:00) Fiji'            , 
	'Asia/Kamchatka'               => '(UTC+12:00) Камчатка', 
	'Pacific/Auckland'             => '(UTC+12:00) Auckland'        , 
	'Asia/Magadan'                 => '(UTC+12:00) Магадан'  , 
	'Pacific/Tongatapu'            => '(UTC+13:00) Nukualofa'       , 
	'Summary'                      => 'Резюме'                     , 
	'Detail'                       => 'Деталь'                      , 
	'LBL_USER_LIST_DETAILS'        => 'Детали'                     , 
	'LBL_USER_DELETED_SUCCESSFULLY' => 'Пользователь удален успешно',
    'LBL_ACTIVE_USERS' => 'Активные пользователи',
    'LBL_INACTIVE_USERS' => 'Неактивные пользователи',
    'LBL_DELETE_USER_PERMANENTLY' => 'Удалить пользователя Постоянно',
    'LBL_RESTORE' => 'Восстановление',
    'LBL_USER_RESTORED_SUCCESSFULLY' => 'Пользователь успешно восстановлены',
	'LBL_ALMOST_THERE'	=>	'Почти там!',
	'LBL_ABOUT_ME'		=>	'Обо мне',
	'LBL_WE_PROMISE_TO_KEEP_THIS_PRIVATE'	=>	'(Мы обещаем сохранить этот частный)',
	'LBL_ALL_FIELDS_BELOW_ARE_REQUIRED'		=>	'(Все приведенные ниже поля обязательны для заполнения)',
	'LBL_GET_STARTED'	=> 'Начать',
	'LBL_YOUR_CONTACT_NUMBER' => 'Контактный телефон',
	'LBL_WHERE_ARE_YOU_FROM' =>	'Откуда вы?',
	'LBL_SELECT_COUNTRY'	=> 'Выберите страну',
	'LBL_COMPANY_SIZE'		=> 'Размер компании',
	'LBL_JOB_TITLE'			=> 'Профессия',
	'LBL_DEPARTMENT'		=> 'Отдел',
	'LBL_BASE_CURRENCY'		=> 'Базисная валюта',
	'LBL_CHOOSE_BASE_CURRENCY'	=> 'Выберите базовую валюту',
	'LBL_OPERATING_CURRENCY'	=> 'Базовая валюта не могут быть изменены позже. Выберите операционную валюту',
	'LBL_LANGUAGE' => 'Язык',
	'LBL_CHOOSE_LANGUAGE'	=> 'Выберите язык',
	'LBL_CHOOSE_TIMEZONE'	=> 'Выберите часовой пояс',
	'LBL_DATE_FORMAT'		=> 'Формат даты',
	'LBL_CHOOSE_DATE_FORMAT'=> 'Выберите формат даты',
	'LBL_PHONE'	=> 'Телефон',
    'Space' => 'Пространство',
	//picklist values for Default Calendar View field in MyPreference Page
	'ListView' => 'Список',
	'MyCalendar' => 'Мой календарь',
	'SharedCalendar' => 'Общий календарь',
    
    'LBL_CHANGE_OWNER' => 'Сменить владельца',
    'LBL_TRANSFER_OWNERSHIP' => 'Передача права собственности',
    'LBL_TRANSFER_OWNERSHIP_TO_USER' => 'С передачей прав собственности на пользователя',
    'LBL_OWNERSHIP_TRANSFERRED_SUCCESSFULLY' => 'CRM Владелец успешно изменен',
    'LBL_OWNERSHIP_TRANSFERRED_FAILED' => 'Не удалось меняется владелец CRM',
    'Account Owner' => 'Владелец счета',
    'Starting Day of the week' => 'Начиная День недели',
    'Day starts at' => 'День начинается с',
    'Default Event Status' => 'Состояние по умолчанию события',
    'Default Activity Type' => 'По умолчанию Тип деятельности',
    'Default Record View' => 'По умолчанию запись Просмотр',
    'Left Panel Hide' => 'Левая панель Скрыть',
    'Row Height' => 'Высота строки',
	'LBL_RESTORE_USER_FAILED' => 'Не удалось восстановить пользователя. Существует уже пользователь CRM от имени этого пользователя.',
    
    'LBL_DUPLICATE_USER_EXISTS' => 'Пользователь уже существует',


	'LBL_CHANGE_USERNAME'          => 'Изменить Имя пользователя',
	'LBL_USERNAME_CHANGED'         => 'Имя пользователя успешно изменен',
	'ERROR_CHANGE_USERNAME'        => 'Ошибка в изменение имени пользователя. Пожалуйста, попробуйте позже',

  'LBL_REMOVE_USER' => 'Удалить',
  'LBL_MORE_OPTIONS' => 'Другие Варианты',
  'LBL_RESTORE_USER' => 'Восстановить Пользователя',
  'LBL_OLD_PASSWORD' => 'Старый Пароль',
  'LBL_CHANGE_PASSWORD' => 'Изменить Пароль',
  'LBL_NEW_PASSWORD' => 'Новый Пароль',
  'LBL_CONFIRM_PASSWORD' => 'Подтвердите Пароль',
	'LBL_CHANGE_ACCESS_KEY' => 'Изменение ключа доступа',
	'LBL_ACCESS_KEY_UPDATED_SUCCESSFULLY' => 'Ключ доступа успешно обновлен',
	'LBL_FAILED_TO_UPDATE_ACCESS_KEY' => 'Не удалось обновить ключ доступа',
  'LBL_LOGIN_AS' => 'Логин как ',
  'LBL_CREATE_USER' => 'Создать Пользователя',
  'LBL_DELETE_USER_PERMANENTLY_INFO' => 'Удаление пользователя постоянно будет перенести все записи, включая комментарии и истории к новому пользователю.',
  'LBL_TO_CRM' => 'Логин вольная русская CRM',
  'LBL_INVALID_USER_OR_PASSWORD' => 'Неверное имя пользователя или пароль.',
  'LBL_INVALID_USER_OR_EMAIL' => 'Неверное имя пользователя или адрес электронной почты.',
  'LBL_EMAIL_SEND' => 'Мы отправили вам письмо для сброса пароля.',
  'ForgotPassword' => 'Забыл Пароль',
  'LBL_CONNECT_WITH_US' => 'Связаться с нами',
  'LBL_GET_MORE' => 'Получите больше от вольная русская',
  'LBL_TRANSFER_RECORDS_TO_USER' => 'Перенос записей пользователя',
  'LBL_USER_TO_BE_DELETED' => 'Пользователь удален',
  'LBL_USERS_SETTINGS' => 'НАСТРОЙКИ ПОЛЬЗОВАТЕЛЕЙ',
  'LBL_TEMPLATES' => 'Шаблоны',

);
$jsLanguageStrings = array(
		
	//Curency seperator validation message
	'JS_ENTER_OLD_PASSWORD'=>'Please enter your old password.',
	'JS_ENTER_NEW_PASSWORD'=>'Please enter your new password.',
	'JS_ENTER_CONFIRMATION_PASSWORD'=>'Please enter your password confirmation.',
	'JS_REENTER_PASSWORDS'=>'Please re-enter passwords.  The \"new password\" and \"confirm password\" values do not match.',
	'JS_INVALID_PASSWORD'=>'You must specify a valid username and password.',
	'JS_PASSWORD_CHANGE_FAILED_1'=>'User password change failed for ',
	'JS_PASSWORD_CHANGE_FAILED_2'=>' failed.  The new password must be set.',
	'JS_PASSWORD_INCORRECT_OLD'=>'Incorrect old password specified. Re-enter password information.',
	'JS_ENTERED_CURRENT_USERNAME_MSG' => 'Вы вошли в текущее имя пользователя. Пожалуйста, введите новое имя пользователя.',
	'JS_NEW_ACCESS_KEY_REQUESTED' => 'Ключ доступа Новый просил',
	'JS_CHANGE_ACCESS_KEY_CONFIRMATION' => 'Вы запросили для нового ключа доступа & л;. Бр & GT; & л; BR & GT; с новым ключом предоставления доступа, вы должны заменить старый ключ доступа с новым во всех установленных расширений и ЛТ;. Бр & GT; & л; BR & GT; Do вы хотите продолжить?',
);