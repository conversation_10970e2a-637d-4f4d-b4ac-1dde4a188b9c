<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
    'Recycle Bin'                  => 'Recycle Bin'                 , // TODO: Review
	'RecycleBin'                   => 'Recycle Bin'                 , // TODO: Review
	'LBL_SELECT_MODULE'            => 'Select Module'               , // TODO: Review
	'LBL_EMPTY_RECYCLEBIN'         => 'Empty Recycle Bin'           , // TODO: Review
	'LBL_RESTORE'                  => 'Restore'                     , // TODO: Review
	'LBL_NO_PERMITTED_MODULES'     => 'No permitted modules available', // TODO: Review
	'LBL_RECORDS_LIST'             => 'Recycle Bin List'            , // TODO: Review
	'LBL_NO_RECORDS_FOUND'         => 'No records found to Restore in module', // TODO: Review
);
$jsLanguageStrings = array(
	'JS_MSG_EMPTY_RB_CONFIRMATION' => 'Are you sure you want to permanently remove all the deleted records from your database?', // TODO: Review
	'JS_LBL_RESTORE_RECORDS_CONFIRMATION' => 'Are you sure you want to restore the records?', // TODO: Review
    'JS_LBL_RESTORE_RECORD_CONFIRMATION' => 'Sunteţi sigur că doriţi să restauraţi Record?',
    'JS_RESTORING_RECORD' => 'Restabilirea înregistrare',
    'JS_RESTORE_AND_UNTRASH_FILE_IN_DRIVE' => 'Restaurare în Vtiger și unitate',

  'JS_RESTORING_RECORDS' => 'Reconstituirea',

);
