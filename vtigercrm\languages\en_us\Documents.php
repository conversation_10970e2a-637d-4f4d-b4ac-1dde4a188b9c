<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	// Basic Strings
	'SINGLE_Documents' => 'Document',
	'Documents' => 'Documents',
	'LBL_ADD_RECORD' => 'Add Document',
	'LBL_RECORDS_LIST' => 'Documents List',

	// Blocks
	'LBL_NOTE_INFORMATION' => 'Basic Information',
	'LBL_FILE_INFORMATION' => 'File Details',
	'LBL_DESCRIPTION' => 'Description',

	//Field Labels
	'Title' => 'Title',
	'File Name' => 'File Name',
	'Note' => 'Note',
	'File Type' => 'File Type',
	'File Size' => 'File Size',
	'Download Type' => 'Download Type',
	'Version' => 'Version',
	'Active' => 'Active',
	'Download Count' => 'Download Count',
	'Folder Name' => 'Folder Name',
	'Document No' => 'Document No',
	'Last Modified By' => 'Last Modified By',

	//Folder
	'LBL_FOLDER_HAS_DOCUMENTS' => 'Please move documents from folder before deleting',

	//DetailView Actions
	'LBL_DOWNLOAD_FILE' => 'Download File',
	'LBL_CHECK_FILE_INTEGRITY' => 'Check file integrity',
	'LBL_EMAIL_FILE_AS_ATTACHMENT' => 'Email file as attachment',

	//EditView
	'LBL_INTERNAL' => 'Internal',
	'LBL_EXTERNAL' => 'External',
	'LBL_MAX_UPLOAD_SIZE' => 'Maximum upload size',

	//ListView Actions
	'LBL_MOVE' => 'Move',
	'LBL_ADD_FOLDER' => 'Add Folder',
	'LBL_FOLDERS_LIST' => 'Folders List',
	'LBL_FOLDERS' => 'Folders',
	'LBL_DOCUMENTS_MOVED_SUCCESSFULLY' => 'Documents Moved Successfully',
	'LBL_DENIED_DOCUMENTS' => 'Denied Documents',
	'MB' => 'MB',

	'LBL_ADD_NEW_FOLDER' => 'Add New Folder',
	'LBL_FOLDER_NAME' => 'Folder Name',
	'LBL_FOLDER_DESCRIPTION' => 'Folder Description',

	//Check file integrity messages
	'LBL_FILE_AVAILABLE' => 'File is available for download',
	'LBL_FILE_NOT_AVAILABLE' => 'This Document is not available for Download',
    'LBL_VIEW_FILE' => 'View File',
    'LBL_PREVIEW_NOT_AVAILABLE' => 'Preview Not Available',
    'LBL_VIEW_FILE' => 'View File',
    'LBL_PREVIEW_NOT_AVAILABLE' => 'Preview Not Available',
    'LBL_INTERNAL_DOCUMENT_TYPE' =>'Internal Document',
    'LBL_EXTERNAL_DOCUMENT_TYPE' =>'External URL',
    'LBL_WEBDOCUMENT_TYPE' =>'Web Document',
    'LBL_DRAG_&_DROP_FILE_HERE' => 'Drag & Drop File Here',
    'LBL_WEB' => 'Web',
    'LBL_UPLOAD_DOCUMENT_TO_VTIGER' => 'Upload Documents To Vtiger',
    'LBL_UPLOAD_TO_DRIVE' => 'Upload To Drive',
    'LBL_SELECT_FROM_DRIVE' => 'Select From Drive',
    'LBL_GOOGLE_DRIVE_FOLDERS' => 'Google Drive Folder',
    'LBL_ROOT' => 'Root',
    'LBL_AUTHORIZE' => 'Authorize',
    'LBL_DOCUMENT_SOURCE' => 'Document Source',
    'LBL_REVOKE_ACCESS_TO_DRIVE' => 'Revoke Access To Drive',
    'LBL_FILE_URL' => 'File Url',
    'LBL_UPLOAD_TO' => 'Upload To',
    'LBL_NEW_DOCUMENT' => 'New Document',
    'LBL_VTIGER' => 'Vtiger',
    'LBL_CREATE_YOUR_OWN' => 'Create your own',
    'LBL_SHARE_DOCUMENT' => 'Share a Document',
    'LBL_UPLOAD_TO_VTIGER' => 'Upload Document to Vtiger',
    'LBL_UPLOAD' => 'Upload',
    'LBL_SELECT_FILE_FROM_COMPUTER' => 'Select File from my Computer',
    'LBL_FILE_UPLOAD' => 'File Upload',
    'LBL_LINK_EXTERNAL_DOCUMENT' => 'Link External Document',
    'LBL_TO_SERVICE' => 'To %s',
    'LBL_FROM_SERVICE' => 'From %s',
    'LBL_CREATE_NEW' => 'Create New %s',
	'LBL_SELECT_A_FOLDER_TO_MOVE' => 'Please select a folder to move',
);

$jsLanguageStrings = array(
	'JS_NEW_FOLDER' => 'New Folder',
	'JS_MOVE_DOCUMENTS' => 'Move Documents',
	//Move documents confirmation message
	'JS_ARE_YOU_SURE_YOU_WANT_TO_MOVE_DOCUMENTS_TO' => 'Are you sure you want to move the file(s) to',
	'JS_FOLDER' => 'folder',
	'JS_OPERATION_DENIED' => 'Operation Denied',
	'JS_FOLDER_IS_NOT_EMPTY' => 'Please Delete/Move all the documents in the current folder before deleting it',
	'JS_SPECIAL_CHARACTERS' => 'Special Characters like',
	'JS_NOT_ALLOWED' => 'are not allowed',
    'JS_DELETE_AND_TRASH_FILE_IN_DRIVE' => 'Delete and trash file in drive',
    'JS_FAILED_TO_UPLOAD_FILE' => 'Failed to upload file',
    'JS_ARE_YOU_SURE_TO_REVOKE_ACCESS' => 'Are you sure you want to revoke access ?',
    'JS_ACCESS_REVOKED' => 'Access Revoked',
    'JS_DELETE_AND_TRASH_FILE_IN_DROPBOX' => 'Delete and trash file in dropbox',
    'JS_UPLOAD_SUCCESSFUL' => 'File Uploaded Successfuly',
    'JS_UPLOAD_FAILED' => 'File Upload Failed',
    'JS_DOCUMENT_CREATED' => 'Document created',
    'JS_DOCUMENT_CREATION_FAILED' => 'Document creation failed',
 );