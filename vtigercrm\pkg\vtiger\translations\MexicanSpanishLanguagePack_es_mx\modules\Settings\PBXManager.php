<?php

/* +***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 * *********************************************************************************** */
$languageStrings = array(
    'LBL_SELECT_ONE' => 'Seleccionar',
    'LBL_PBXMANAGER' =>'PBXManager',
    'LBL_PBXMANAGER_CONFIG' => 'Detalles del servidor Asterisk',
    'LBL_NOTE' => 'Nota:',
    'LBL_INFO_WEBAPP_URL' => 'Configure la URL de su App de Asterisk en el formato', 
    'LBL_FORMAT_WEBAPP_URL' => '(Protocolo) :/ / (asterisk_ip): (puerto)',
    'LBL_FORMAT_INFO_WEBAPP_URL' => 'ej: http://0.0.0.0:5000',
    'LBL_INFO_CONTEXT' => 'El contexto específico de Vtiger está configurado en el servidor de Asterisk (extensions.conf)',
    'LBL_PBXMANAGER_INFO' => 'Configure los detalles del servidor de Asterisk después de instalar el conector de  Asterisk con Vtiger en su servidor de Asterisk',
    
    'webappurl'=>'URL de la App de Vtiger Asterisk',
    'vtigersecretkey'=>'Clave secreta de Vtiger',
    'outboundcontext' => 'Contexto saliente',
    'outboundtrunk' => 'Línea saliente',
    
);

$jsLanguageStrings = array(
    
);
?>  
