<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_NEW'                      => 'New'                         , // TODO: Review
	'LBL_WORKFLOW'                 => 'Workflow'                    , // TODO: Review
	'LBL_CREATING_WORKFLOW'        => 'Creating WorkFlow'           , // TODO: Review
	'LBL_NEXT'                     => 'Next'                        , // TODO: Review
	'LBL_STEP_1'                   => 'Step 1'                      , // TODO: Review
	'LBL_ENTER_BASIC_DETAILS_OF_THE_WORKFLOW' => 'Enter basic details of the Workflow', // TODO: Review
	'LBL_SPECIFY_WHEN_TO_EXECUTE'  => 'Specify when to execute this Workflow', // TODO: Review
	'ON_FIRST_SAVE'                => 'Only on the first save'      , // TODO: Review
	'ONCE'                         => 'Until the first time the condition is true', // TODO: Review
	'ON_EVERY_SAVE'                => 'Every time the record is saved', // TODO: Review
	'ON_MODIFY'                    => 'Every time a record is modified', // TODO: Review
	'MANUAL'                       => 'System'                      , // TODO: Review
	'SCHEDULE_WORKFLOW'            => 'Schedule Workflow'           , // TODO: Review
	'ADD_CONDITIONS'               => 'Add Conditions'              , // TODO: Review
	'ADD_TASKS'                    => 'Acties toevoegen'            ,
	'LBL_EXPRESSION'               => 'Expression'                  , // TODO: Review
	'LBL_FIELD_NAME'               => 'Field'                       , // TODO: Review
	'LBL_SET_VALUE'                => 'Set Value'                   , // TODO: Review
	'LBL_USE_FIELD'                => 'Use Field'                   , // TODO: Review
	'LBL_USE_FUNCTION'             => 'Use Function'                , // TODO: Review
	'LBL_RAW_TEXT'                 => 'Raw text'                    , // TODO: Review
	'LBL_ENABLE_TO_CREATE_FILTERS' => 'Enable to create Filters'    , // TODO: Review
	'LBL_CREATED_IN_OLD_LOOK_CANNOT_BE_EDITED' => 'This workflow was created in older look. Conditions created in older look cannot be edited. You can choose to recreate the conditions, or use the existing conditions without changing them.', // TODO: Review
	'LBL_USE_EXISTING_CONDITIONS'  => 'Use existing conditions'     , // TODO: Review
	'LBL_RECREATE_CONDITIONS'      => 'Recreate Conditions'         , // TODO: Review
	'LBL_SAVE_AND_CONTINUE'        => 'Save & Continue'             , // TODO: Review
	'LBL_ACTIVE'                   => 'Active'                      , // TODO: Review
	'LBL_TASK_TYPE'                => 'Actie Type'                  ,
	'LBL_TASK_TITLE'               => 'Actie Titel'                 ,
	'LBL_ADD_TASKS_FOR_WORKFLOW'   => 'Voeg Actie voor Workflow'    ,
	'LBL_EXECUTE_TASK'             => 'Voer actie'                  ,
	'LBL_SELECT_OPTIONS'           => 'Select Options'              , // TODO: Review
	'LBL_ADD_FIELD'                => 'Add Field'                   , // TODO: Review
	'LBL_ADD_TIME'                 => 'Add time'                    , // TODO: Review
	'LBL_TITLE'                    => 'Title'                       , // TODO: Review
	'LBL_PRIORITY'                 => 'Priority'                    , // TODO: Review
	'LBL_ASSIGNED_TO'              => 'Assigned to'                 , // TODO: Review
	'LBL_TIME'                     => 'Time'                        , // TODO: Review
	'LBL_DUE_DATE'                 => 'Due Date'                    , // TODO: Review
	'LBL_THE_SAME_VALUE_IS_USED_FOR_START_DATE' => 'The same value is used for the start date', // TODO: Review
	'LBL_EVENT_NAME'               => 'Event Name'                  , // TODO: Review
	'LBL_TYPE'                     => 'Type'                        , // TODO: Review
	'LBL_METHOD_NAME'              => 'Method Name'                 , // TODO: Review
	'LBL_RECEPIENTS'               => 'Recepients'                  , // TODO: Review
	'LBL_ADD_FIELDS'               => 'Add Fields'                  , // TODO: Review
	'LBL_SMS_TEXT'                 => 'Sms Text'                    , // TODO: Review
	'LBL_SET_FIELD_VALUES'         => 'Set Field Values'            , // TODO: Review
	'LBL_IN_ACTIVE'                => 'In Active'                   , // TODO: Review
	'LBL_SEND_NOTIFICATION'        => 'Send Notification'           , // TODO: Review
	'LBL_START_TIME'               => 'Start Time'                  , // TODO: Review
	'LBL_START_DATE'               => 'Start Date'                  , // TODO: Review
	'LBL_END_TIME'                 => 'End Time'                    , // TODO: Review
	'LBL_END_DATE'                 => 'End Date'                    , // TODO: Review
	'LBL_ENABLE_REPEAT'            => 'Enable Repeat'               , // TODO: Review
	'LBL_NO_METHOD_IS_AVAILABLE_FOR_THIS_MODULE' => 'No method is available for this module', // TODO: Review
	'LBL_FINISH'                   => 'Finish'                      , // TODO: Review
	'LBL_NO_TASKS_ADDED'           => 'Geen acties'                 ,
	'LBL_CANNOT_DELETE_DEFAULT_WORKFLOW' => 'You Cannot delete default Workflow', // TODO: Review
	'LBL_MODULES_TO_CREATE_RECORD' => 'Maak een record in'          ,
	'LBL_EXAMPLE_EXPRESSION'       => 'Expression'                  , // TODO: Review
	'LBL_EXAMPLE_RAWTEXT'          => 'Rawtext'                     , // TODO: Review
	'LBL_VTIGER'                   => 'Vtiger'                      , // TODO: Review
	'LBL_EXAMPLE_FIELD_NAME'       => 'Field'                       , // TODO: Review
	'LBL_NOTIFY_OWNER'             => 'notify_owner'                , // TODO: Review
	'LBL_ANNUAL_REVENUE'           => 'annual_revenue'              , // TODO: Review
	'LBL_EXPRESSION_EXAMPLE2'      => 'if mailingcountry == \'India\' then concat(firstname,\' \',lastname) else concat(lastname,\' \',firstname) end', // TODO: Review
	'LBL_FROM' => 'Van',
	'Optional' => 'Facultatief',
	'LBL_ADD_TASK'                 => 'Actie toevoegen'             ,
    'Portal Pdf Url' =>'Portal Pdf koppeling',
    'LBL_ADD_TEMPLATE' => 'Sjabloon Voeg de',
    'LBL_LINEITEM_BLOCK_GROUP' => 'Artikelen blok voor groep belasting',
    'LBL_LINEITEM_BLOCK_INDIVIDUAL' => 'Artikelen blok voor individuele fiscale',
    'LBL_ADD_PDF' => 'pdf toevoegen',
	
	//Translation for module
	'Calendar'					   => 'Te doen'						,
	'Send Mail'					   => 'Email',
	'Invoke Custom Function'	   => 'Beroep doen op persoonlijke voorkeuze',
	'Create Todo'				   => 'Maak Todo',
	'Create Event'				   => 'Maak Event',
	'Update Fields'				   => 'Velden bijwerken',
	'Create Entity'                => 'Record maken'                ,
	'SMS Task'					   => 'SMS Task',
	'Mobile Push Notification'	   => 'Mobile Push Notification',
	'LBL_ACTION_TYPE' => 'Actie Type (Active Count)',
	'LBL_VTEmailTask' => 'Email',
    'LBL_VTEntityMethodTask' => 'Custom Function',
    'LBL_VTCreateTodoTask' => 'Taak',
    'LBL_VTCreateEventTask' => 'Evenement',
    'LBL_VTUpdateFieldsTask' => 'Veld bijwerken',
    'LBL_VTSMSTask' => 'SMS', 
    'LBL_VTPushNotificationTask' => 'Mobile Kennisgeving',
    'LBL_VTCreateEntityTask' => 'Maak Record',
	'LBL_MAX_SCHEDULED_WORKFLOWS_EXCEEDED' => 'Maximumaantal (%s) van geregelde werkstromen overschreden',

  'LBL_EDITING_WORKFLOW' => 'Het Bewerken Van Workflow',
  'LBL_ADD_RECORD' => 'Nieuwe Workflow',
  'ON_SCHEDULE' => 'Plannen',
  'LBL_RUN_WORKFLOW' => 'Uitvoeren Van De Workflow',
  'LBL_AT_TIME' => 'Op Tijd',
  'LBL_HOURLY' => 'Per uur',
  'ENTER_FROM_EMAIL_ADDRESS' => 'Voer een e-mailadres',
  'LBL_DAILY' => 'Dagelijks',
  'LBL_WEEKLY' => 'Wekelijks',
  'LBL_ON_THESE_DAYS' => 'Op deze dagen',
  'LBL_MONTHLY_BY_DATE' => 'Maandelijks (Datum',
  'LBL_MONTHLY_BY_WEEKDAY' => 'Maandelijks per Weekdag',
  'LBL_YEARLY' => 'Jaarlijks',
  'LBL_SPECIFIC_DATE' => 'Op Een Specifieke Datum',
  'LBL_CHOOSE_DATE' => 'Kies Een Datum',
  'LBL_SELECT_MONTH_AND_DAY' => 'Selecteer de Maand en de Datum',
  'LBL_SELECTED_DATES' => 'Gekozen Data',
  'LBL_EXCEEDING_MAXIMUM_LIMIT' => 'Maximum overschreden',
  'LBL_NEXT_TRIGGER_TIME' => 'Naast de trigger-tijd op',
  'LBL_MESSAGE' => 'Bericht',
  'LBL_WORKFLOW_NAME' => 'Workflow Naam',
  'LBL_TARGET_MODULE' => 'Doel Module',
  'LBL_WORKFLOW_TRIGGER' => 'Workflow Trigger',
  'LBL_TRIGGER_WORKFLOW_ON' => 'Trigger Workflow Op',
  'LBL_RECORD_CREATION' => 'Record Creation',
  'LBL_RECORD_UPDATE' => 'Record Update',
  'LBL_TIME_INTERVAL' => 'Tijd Interval',
  'LBL_RECURRENCE' => 'Herhaling',
  'LBL_FIRST_TIME_CONDITION_MET' => 'Alleen de eerste keer conditons zijn voldaan',
  'LBL_EVERY_TIME_CONDITION_MET' => 'Elke keer conditons zijn voldaan',
  'LBL_WORKFLOW_CONDITION' => 'Workflow Toestand',
  'LBL_WORKFLOW_ACTIONS' => 'Workflow-Acties',
  'LBL_DELAY_ACTION' => 'Vertraging Actie',
  'LBL_FREQUENCY' => 'Frequentie',
  'LBL_SELECT_FIELDS' => 'Selecteer Velden',
  'LBL_INCLUDES_CREATION' => 'De Oprichting',
  'LBL_ACTION_FOR_WORKFLOW' => 'Actie voor Workflow',
  'LBL_WORKFLOW_SEARCH' => 'Zoek op Naam',

);
$jsLanguageStrings = array(
	'JS_STATUS_CHANGED_SUCCESSFULLY' => 'Status changed Successfully' , // TODO: Review
	'JS_TASK_DELETED_SUCCESSFULLY' => 'Actie succesvol verwijderd'  ,
	'JS_SAME_FIELDS_SELECTED_MORE_THAN_ONCE' => 'Same fields selected more than once', // TODO: Review
	'JS_WORKFLOW_SAVED_SUCCESSFULLY' => 'Workflow saved successfully' , // TODO: Review
    'JS_CHECK_START_AND_END_DATE'=>'Einddatum en tijd moet groter zijn dan of gelijk zijn aan Datum en tijd starten',

  'JS_TASK_STATUS_CHANGED' => 'Status van een taak met succes veranderd.',
  'JS_WORKFLOWS_STATUS_CHANGED' => 'De Workflow-status gewijzigd.',
  'VTEmailTask' => 'Stuur E-Mail',
  'VTEntityMethodTask' => 'Aanroepen Van De Aangepaste Functie',
  'VTCreateTodoTask' => 'Taak Maken',
  'VTCreateEventTask' => 'Afspraak Maken',
  'VTUpdateFieldsTask' => 'Velden Bijwerken',
  'VTSMSTask' => 'SMS Taak',
  'VTPushNotificationTask' => 'Mobiele Push-Meldingen',
  'VTCreateEntityTask' => 'Record Maken',
  'LBL_EXPRESSION_INVALID' => 'Ongeldige Expressie',

);