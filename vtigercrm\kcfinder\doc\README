[============================< KCFinder 2.21 >===============================]
[                                                                            ]
[                    Copyright 2010 KCFinder Project                         ]
[                     http://kcfinder.sunhater.com                           ]
[              <PERSON> <<EMAIL>>                  ]
[                                                                            ]
[============================================================================]


I. DESCRIPTION

    KCFinder is an alternative to the CKFinder Web file manager. It can be
    integrated into FCKeditor, CKEditor, and TinyMCE WYSIWYG web editors or
    your custom web applications to upload and manage images, flash movies,
    and other files that can be embedded in an editor's generated HTML
    content. Only PHP server-side scripting is supported.


II. FEATURES

    1. Fully integrated Ajax engine.

    2. Easy to integrate and configure in web applications.

    3. Clipboard for copy and move multiple files

    4. Select multiple files with Ctrl key

    5. Download multiple files or a folder as ZIP file

    6. Resize bigger uploaded images. Configurable maximum image resolution.

    7. Configurable thumbnail resolution.

    8. Visual themes.

    9. Multilanguage system.


III. REQUIREMENTS

    1. Web server (Apache 2 is tested only)

    2. PHP 5.x.x. with GD extension. Safe mode should be disabled. To work
       width client-side HTTP cache, the PHP must be configured as Apache
       module.

    3. PHP ZIP extension for multiple files download. If it's not available,
       KCFinder will work but without this feature.

    4. PHP Fileinfo extension if you want to check file's MIME type before
       moving to upload directory. PHP versions lesser than 5.3 needs to
       install Fileinfo PECL extension: http://pecl.php.net/package/Fileinfo

    5. Modern browser (not IE6!).


IV. INSTALLATION

    See http://kcfinder.sunhater.com/install


V. USED 3RD PARTY SOFTWARE

    1. jQuery JavaScript library v1.4.2 - http://www.jquery.com

    2. jQuery Right-Click Plugin v1.01 - http://abeautifulsite.net/notebook/68

    3. jquery.event.drag Plugin v2.0.0 - http://threedubmedia.com/code/event/drag

    4. In realization of "oxygen" theme were used icons and color schemes of
       default KDE4 theme - http://www.kde.org
