<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 * ********************************************************************************
 *  Language     : Español es_es
 *  Version      : 6.0.0
 *  Created Date : 2013-05-10
 *  Author       : JPL TSolucio, S. L. Joe <PERSON>rdes
 *  Last change  : 2013-05-10
 *  Author       : JPL TSolucio, S. L. <PERSON>
 ************************************************************************************/

$languageStrings = Array(
    'Recycle Bin' => 'Papelera de Reciclaje',
	'RecycleBin' => 'Papelera de Reciclaje',
	'LBL_SELECT_MODULE' => 'Selecciona Módulo',
	'LBL_EMPTY_RECYCLEBIN' => 'Vaciar Papelera de Reciclaje',
	'LBL_RESTORE' => 'Restaurar',
	'LBL_NO_PERMITTED_MODULES' => 'No hay modulos permitidos',
	'LBL_RECORDS_LIST' => 'Lista Papelera',
	'LBL_NO_RECORDS_FOUND' => 'No hay registros en la papelera de este módulo',
);

$jsLanguageStrings = array(
	'JS_MSG_EMPTY_RB_CONFIRMATION' => '¿Estás seguro que quieres eliminar permanentemente todos los registros borrados de la base de datos?',
	'JS_LBL_RESTORE_RECORDS_CONFIRMATION' => '¿Estás seguro que quieres restaurar los registros?',
    'JS_LBL_RESTORE_RECORD_CONFIRMATION' => '¿Está seguro que desea restaurar el registro?',
    'JS_RESTORING_RECORD' => 'Restauración de Registro',
    'JS_RESTORE_AND_UNTRASH_FILE_IN_DRIVE' => 'Restaurar en Vtiger y Drive',

  'JS_RESTORING_RECORDS' => 'La restauración de registros',

);

?>