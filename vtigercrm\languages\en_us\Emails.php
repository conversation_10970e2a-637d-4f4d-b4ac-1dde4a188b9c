<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'SINGLE_Emails' => 'Email',
	'Emails' => 'Emails',
	'LBL_SELECT_EMAIL_IDS' => 'Select Email Addresses',
	'LBL_SUBJECT' => 'Subject',
	'LBL_ATTACHMENT' => 'Attachment',
	'LBL_BROWSE_CRM' => 'Browse CRM',
	'LBL_SEND' => 'Send',
	'LBL_SAVE_AS_DRAFT' => 'Save as Draft',
	'LBL_GO_TO_PREVIEW' => 'Go to Preview',
	'LBL_SELECT_EMAIL_TEMPLATE' => 'Select Email Template',
	'LBL_COMPOSE_EMAIL' => 'Compose Email',
	'LBL_TO' => 'To',
	'LBL_CC' => 'Cc',
	'LBL_BCC' => 'Bcc',
	'LBL_ADD_CC' => 'Add Cc',
	'LBL_ADD_BCC' => 'Add Bcc',
	'LBL_MAX_UPLOAD_SIZE' => 'Maximum upload size is',
	'LBL_EXCEEDED' => 'Exceeded',
	'LBL_EMAILTEMPLATE_WARNING'    => 'Are your merge-tags correct',
	'LBL_EMAILTEMPLATE_WARNING_CONTENT' => 'Please make sure that the template you selected has merge-tags relevant to the recipient record. 
											If you are sending an email to Lead, but the merge-tags belong to Contact module (ex: $contacts-lastname$), 
											then the values will not be merged.',

	//Button Names translation
	'LBL_FORWARD' => 'Forward',
	'LBL_PRINT' => 'Print',
	'LBL_DESCRIPTION' => 'Description',
	'LBL_FROM' => 'From',
	'LBL_INFO' => 'Info',
	'LBL_DRAFTED_ON' => 'Drafted on',
	'LBL_SENT_ON' => 'Sent on',
	'LBL_OWNER' => 'Owner',

	'Date & Time Sent' => 'Date Sent',
	'Time Start' => 'Time Sent',
	'LBL_EMAIL_INFORMATION' => 'Email Information',
	'Draft' => 'Draft',
	'Parent ID' => 'Parent Record',
);

$jsLanguageStrings = array(
	'JS_WARNING' => 'Warning',
);    
