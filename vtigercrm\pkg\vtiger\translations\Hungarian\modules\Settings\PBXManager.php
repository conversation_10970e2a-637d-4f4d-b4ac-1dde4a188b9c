<?php

/* +***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 * *********************************************************************************** */
$languageStrings = array(
    'LBL_SELECT_ONE'        => 'Választ',
    'LBL_PBXMANAGER'        => 'PBX-Menedzser',
    'LBL_PBXMANAGER_CONFIG' => 'Asterisk Szerver adatai',
    'LBL_NOTE'              => 'Megjegyzés:',
    'LBL_INFO_WEBAPP_URL'   => 'Állítsa be az Asterisk alkalmazás URL-t ebben a  formátumban', 
    'LBL_FORMAT_WEBAPP_URL' => '(protocol)://(asterisk_ip):(VtigerConnector_port)',
    'LBL_FORMAT_INFO_WEBAPP_URL' => 'ex:http://0.0.0.0:5000',
    'LBL_INFO_CONTEXT'      => 'Vtiger Specfic context configured in your Asterisk server(extensions.conf)',
    'LBL_PBXMANAGER_INFO'   => 'Configure Asterisk Server Details after Installing Vtiger Asterisk Connector in your Asterisk Server',
    
    'webappurl'             => 'Vtiger Asterisk alkalmazás URL',
    'vtigersecretkey'       => 'Vtiger biztonsági kulcs'       ,
    'outboundcontext'       => 'Kimenő csomag'                 ,
    'outboundtrunk'         => 'Kimenő törzs'                  ,
    
);

$jsLanguageStrings = array(
    
);
?>  
