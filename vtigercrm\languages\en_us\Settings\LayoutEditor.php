<?php
/*+***********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 *************************************************************************************/
$languageStrings = array(
	'LayoutEditor' => 'Layout Editor',
	'LBL_FIELDS_AND_LAYOUT_EDITOR' => 'Fields and Layout Editor',
	'LBL_CREATE_CUSTOM_FIELD' => 'Create Custom Field',
	'LBL_DETAILVIEW_LAYOUT' => 'Detail View Layout',
	'LBL_ARRANGE_RELATED_TABS' => 'Arrange Related Tabs',
	'LBL_ADD_CUSTOM_FIELD' => 'Add Custom Field',
	'LBL_ADD_CUSTOM_BLOCK' => 'Add Block',
	'LBL_SAVE_FIELD_SEQUENCE' => 'Save Field Sequence',
	'LBL_BLOCK_NAME' => 'Block Name',
	'LBL_ADD_AFTER' => 'Add After',
	'LBL_ACTIONS' => 'Actions',
	'LBL_ALWAYS_SHOW' => 'Always Show',
    'LBL_SHOW_INACTIVE_FIELDS' => 'Show inactive Fields',
	'LBL_SHOW_HIDDEN_FIELDS' => 'View Hidden Fields',
	'LBL_DELETE_CUSTOM_BLOCK' => 'Delete Custom Block',
	'LBL_MANDATORY_FIELD' => 'Mandatory Field',
	'LBL_ACTIVE' => 'Active',
	'LBL_QUICK_CREATE' => 'Quick Create',
	'LBL_SUMMARY_FIELD' => 'Summary View',
    'LBL_KEY_FIELD_VIEW' => 'Key Field View',
	'LBL_MASS_EDIT' => 'Mass Edit',
	'LBL_DEFAULT_VALUE' => 'Default Value',
	'LBL_SELECT_FIELD_TYPE' => 'Select Field Type',
	'LBL_LABEL_NAME' => 'Label Name',
	'LBL_LENGTH' => 'Length',
	'LBL_DECIMALS' => 'Decimals',
	'LBL_ENTER_PICKLIST_VALUES' => 'Enter Picklist Values..',
	'LBL_PICKLIST_VALUES' => 'Picklist Values',
	'LBL_INACTIVE_FIELDS' => 'Inactive Fields',
	'LBL_REACTIVATE' => 'Reactivate',
	'LBL_ARRANGE_RELATED_LIST' => 'Arrange Related List',
	'LBL_SELECT_MODULE_TO_ADD' => 'Select Module To Add',
    'LBL_SELECT_HIDDEN_MODULE' => 'Select hidden module',
	'LBL_NO_RELATED_INFORMATION' => 'No Related Information',
	'LBL_RELATED_LIST_INFO' => 'Drag and drop the module to reorder the list',
	'LBL_REMOVE_INFO' => 'Click on the delete icon to remove the relationship permanently from the list',
	'LBL_ADD_MODULE_INFO' => 'Select the module from the removed modules to add back to list',
    'LBL_MANY_MANY_TEXT' => 'Many-Many relationships are currently available only for %s and %s',
	'LBL_SELECT_MODULE' => 'Select Module..',
	'LBL_DUPLICATES_EXIST' => 'Block Name already exists',
	'LBL_NON_ROLE_BASED_PICKLIST' => 'Non Role Based Picklist',
	'LBL_DUPLICATE_FIELD_EXISTS' => 'Duplicate Field Exists',
	'LBL_WRONG_FIELD_TYPE' => 'Wrong Field Type',
	'LBL_ROLE_BASED_PICKLIST' => 'Role Based Picklist',
	'LBL_CLICK_HERE_TO_EDIT' => 'Click here to edit',
    'LBL_EDIT_FIELD' => 'Edit Field properties: %s',

	//Field Types
	'Text'=>'Text',
	'Decimal'=>'Decimal',
	'Integer'=>'Integer',
	'Percent'=>'Percent',
	'Currency'=>'Currency',
	'Date'=>'Date',
	'Email'=>'Email',
	'Phone'=>'Phone',
	'PickList'=>'Pick List',
	'MultiSelectCombo'=>'Multi-Select Combo Box',
	'URL' => 'URL',
	'Checkbox' => 'Checkbox',
	'TextArea' => 'Text Area',
	'Skype'=>'Skype',
	'Time'=>'Time',
	
	//Translation for module
	'Calendar' => 'Task',
	'LBL_FIELD_COULD_NOT_BE_CREATED'=>'Field %s could not be created',
	'SELECT_MODULE' => 'Select Module',
    
    //Related Lists 
    'LBL_RELATION_SHIPS' => 'Relationships',
    'LBL_ADD_RELATIONSHIP' => 'Add Relationship',
    'LBL_RELATED_MODULE' => 'Related Module',
    'LBL_ADDING_RELATIONSHIP' => 'Adding Relationship for %s',
    'LBL_SELECTED_RELATED_MODULE' => 'Select related module',
    'LBL_SELECTED_RELATION_TYPE' => 'Select relationship type',
    'ONE_ONE_AND_MANY_ONE_RELATIONSHIP' => 'One-one & Many-one Relationships',
    'ONE_MANY_RELATIONSHIP' => 'One-many & Many-many Relationships',
    '1-1' => 'One to One',
    '1-N' => 'One to Many',
    'N-1' => 'Many to One',
    'N-N' => 'Many to Many',
    
	
	//New layout translations
	'LBL_FIELD_TYPES'                   => 'Field Types', 
	'LBL_BASIC_FIELDS'             => 'Basic Fields', 
	'LBL_MANDATORY'                => 'Mandatory Field', 
	'LBL_PROPERTIES'               => 'Properties', 
	'LBL_DRAG_UI_TYPE'             => 'Drag and Drop Field here', 
	'LBL_RELATION_FIELDS'          => 'Relation Fields', 
	'LBL_SELECT_BLOCK'             => 'Select Block', 
    'LBL_RELATION_ADDED_SUCCESS'   => 'Relationship added sucessfully',
    
    'FIELD_NAME_IN_PRIMARY_MODULE' => 'Relation field in %s',
    'FIELD_NAME_IN_RELATED_MODULE' => 'Relation Field in %s',
    'TAB_IN_PRIMARY_MODULE'        => 'Table/Tab in %s',
    'TAB_IN_RELATED_MODULE'        => 'Table/Tab in %s',
    
    'LBL_DETAIL_VIEW'              => 'Detail View',
    'LBL_EXPANDED'                 => 'Expanded',
    'LBL_COLLAPSED'                => 'Collapsed',
    
    'LBL_FILED_IN_PRIMARY_HELP_TEXT' => 'Reference field of %s module',
    'LBL_TAB_IN_PRIMARY_HELP_TEXT'   => 'Table/Tab of %s module',
    'LBL_FILED_IN_RELATED_HELP_TEXT' => 'Reference field of  %s module',
    'LBL_TAB_IN_RELATED_HELP_TEXT'   => 'Table/Tab of %s module',
    'LBL_NO_RELATION_TYPE'         => 'No relationship of this type exists',

	'LBL_CREATE_ITEM_CUSTOM_FIELD' => 'Create Line item Custom Field',
	'LBL_MAP_PRODUCT_FIELD' => 'Map to Products field',
	'LBL_MAP_SERVICE_FIELD' => 'Map to Services field',
	'LBL_ENABLE_TO_MAP_PRODUCT_FIELD' => 'Enable to map Products field',
	'LBL_ENABLE_TO_MAP_SERVICE_FIELD' => 'Enable to map Services field',
    
    //Vtiger7 Strings
    'LBL_NO_RELATED_INFO' => 'No Relationship exists',
    'LBL_ADD_NEW_FIELD_HERE' => 'Add new field here',
    'LBL_SAVE_LAYOUT' => 'Save Layout',
    'LBL_SHOW_FIELD' => 'Show Field',
    'LBL_ENABLE_OR_DISABLE_FIELD_PROP' => 'Enable / Disable field properties',
    'LBL_PROP_MANDATORY' => 'mandatory',
    'LBL_DEFAULT_VALUE_NOT_SET' => 'Default value not set',
    'LBL_INFO' => 'Info',
    'LBL_PRODUCTFIELDDEFAULTVALUE' => ' (Products)',
    'LBL_SERVICEFIELDDEFAULTVALUE' => ' (Services)',
    'LBL_SHOW_THIS_FIELD_IN' => 'Click here to show this field in %s view',
    'LBL_MAKE_THIS_FIELD' => 'Click here to make this field %s',
    'LBL_HIDE_THIS_FIELD_IN' => 'Click here to hide this field in %s view',
    'LBL_NOT_MAKE_THIS_FIELD' => 'Click here to make this field non-%s',
    'LBL_TAB_NAME_HELP_TEXT' => 'List of %s shown in %s record',
    'LBL_TAB_NAME_TEXT' => 'Tab name of %s in %s record',
    'LBL_FILED_NAME_HELP_TEXT' => 'A Reference field to %s in %s record will be added',
    'LBL_FIELD_NAME_TEXT' => 'Field name of %s in %s record',
	'LBL_COLLAPSE_BLOCK' => 'Collapse Block',
	'LBL_COLLAPSE_BLOCK_DETAIL_VIEW' => 'Collapse the block in detail view',
    'LBL_HEADER' => 'Header',
    'LBL_DETAIL_HEADER' => 'Record header',
    'LBL_HEADER_FIELD' => 'Header View',

	'LBL_DUPLICATE_HANDLING' => 'Duplicate Prevention',
	'LBL_DUPLICATE_CHECK' => 'Enable duplicate check',
	'LBL_DUPLICATION_INFO_MESSAGE' => 'Duplicate prevention feature only prevents new duplicate records from getting created by users and external applications. Records created from Import, and from Workflows will not be checked for duplicates.<br><br>Existing duplicate records can be removed using “Find Duplicates” feature from the module page.',
	'LBL_SELECT_FIELDS_FOR_DUPLICATION' => 'Select the unique fields on which duplicate records are to be checked',
	'LBL_SELECT_FIELDS' => 'Select Fields',
	'LBL_MAX_3_FIELDS' => 'Max 3 Fields',
	'LBL_SELECT_RULE' => 'Select rule for handling duplicates',
	'LBL_ALLOW_DUPLICATES' => 'Allow Duplicates',
	'LBL_DO_NOT_ALLOW_DUPLICATES' => 'Do not allow Duplicates',
	'LBL_DUPLICATES_IN_SYNC_MESSAGE' => 'Action to take if duplicate record is found while syncing with external application',
	'LBL_PREFER_LATEST_RECORD' => 'Prefer latest record',
	'LBL_PREFER_INTERNAL_RECORD' => 'Prefer internal record',
	'LBL_PREFER_EXTERNAL_RECORD' => 'Prefer external record',
	'LBL_SYNC_TOOLTIP_MESSAGE' => 'Prefer latest record - Most recently modified record data will be retained<br>Prefer internal record - Existing record will be retained as it is<br>Prefer external record - Data from the external application will be copied',
);

$jsLanguageStrings = array(
	'JS_BLOCK_VISIBILITY_SHOW' => 'Block show enabled',
	'JS_BLOCK_VISIBILITY_HIDE' => 'Block hide enabled',
	'JS_CUSTOM_BLOCK_ADDED' => 'New Custom Block added',
	'JS_BLOCK_SEQUENCE_UPDATED' => 'Blocks Sequence Updated',
	'JS_SELECTED_FIELDS_REACTIVATED' => 'Selected Fields Reactivated',
	'JS_FIELD_DETAILS_SAVED' => 'Field Details Saved',
	'JS_CUSTOM_BLOCK_DELETED' => 'Custom Block Deleted',
	'JS_CUSTOM_FIELD_ADDED' => '%s Field Added',
	'JS_CUSTOM_FIELD_DELETED' => 'Custom Field Deleted',
	'JS_LENGTH_SHOULD_BE_LESS_THAN_EQUAL_TO' => 'Length Should be less than or equal to',
	'JS_PLEASE_ENTER_NUMBER_IN_RANGE_2TO5' => 'Decimal should be in the range 2 to 5',
	'JS_SAVE_THE_CHANGES_TO_UPDATE_FIELD_SEQUENCE' => 'Save the changes to update Field sequence',
	'JS_RELATED_INFO_SAVED' => 'Related Info Saved',
	'JS_BLOCK_NAME_EXISTS' => 'Block Name already exists',
	'JS_NO_HIDDEN_FIELDS_EXISTS' => 'No Inactive Fields',
	'JS_SPECIAL_CHARACTERS' => 'Special Characters like',
	'JS_NOT_ALLOWED' => 'are not allowed',
	'JS_FIELD_SEQUENCE_UPDATED' => 'Field Sequence Updated',
	'JS_DUPLICATES_VALUES_FOUND' => 'Duplicate Values found',
    'JS_FIELD_IN_RELATED_MODULE'  => 'Relation field in %s',
    'JS_TAB_IN_RELATED_MODULE' => 'Table/Tab in %s',
    'JS_FILED_IN_RELATED_HELP_TEXT' => 'Reference field of %s module',
    'JS_TAB_IN_RELATED_HELP_TEXT'   => 'Table/Tab of %s module',
    'JS_TAB_FIELD_DELETION'   => 'Deleting the relationship will delete the %s field in %s module and delete the %s Table/Tab in %s. Do you wish to proceed?',
    'JS_ONE_ONE_RELATION_FIELD_DELETE' => 'This will delete %s field in %s module and will delete %s field in %s module. Do you wish to continue?',
	'JS_CUSTOM_FIELDS_MAX_LIMIT' => 'You can add only maximum of %s custom fields in %s block',
    'JS_DEFAULT_VALUE_NOT_SET' => 'Default value not set',
    'JS_DEFAULT_VALUE' => 'Default Value',
    'JS_SAVE_MODULE_SEQUENCE' => 'Save the changes to update Related Module sequence',
    'JS_PRODUCTFIELDDEFAULTVALUE' => ' (Products)',
    'JS_SERVICEFIELDDEFAULTVALUE' => ' (Services)',
    'JS_TAB_TAB_DELETION' => 'This will delete %s tab in %s module and its data. Do you wish to proceed?',
    'JS_SHOW_THIS_FIELD_IN' => 'Click here to show this field in %s view',
    'JS_MAKE_THIS_FIELD' => 'Click here to make this field %s',
    'JS_HIDE_THIS_FIELD_IN' => 'Click here to hide this field in %s view',
    'JS_NOT_MAKE_THIS_FIELD' => 'Click here to make this field non-%s',
    'JS_TAB_NAME_HELP_TEXT' => 'List of %s shown in %s record',
    'JS_TAB_NAME_TEXT' => 'Tab name of %s in %s record',
    'JS_FILED_NAME_HELP_TEXT' => 'A Reference field to %s in %s record will be added',
    'JS_FIELD_NAME_TEXT' => 'Field name of %s in %s record',
    'JS_PROP_MANDATORY' => 'mandatory',
    'JS_SUMMARY' => 'Summary',
    'JS_KEY_FIELD' => 'Key Field',
    
    'JS_QUICK_CREATE' => 'Quick Create',
    'JS_MASS_EDIT' => 'Mass Edit',
    'JS_LBL_ARE_YOU_SURE_YOU_WANT_TO_DELETE' => 'This operation will result in permanent removal of data.
                                                 When a field is deleted, values stored in this field will be deleted, and cannot be retrieved.
                                                 If you are not sure and want to be able to view this data in the future, you can mark the field as Inactive instead of deleting it. Inactive fields can later be activated at any time.
                                                 Are you sure to delete this field?',
    'JS_FIELD_DELETE_CONFIRMATION' => 'Delete - I dont need the data in this field.',
	'JS_STATUS_CHANGED_SUCCESSFULLY' => 'Status changed Successfully',
    'JS_FIELD_CAN_EITHER_BE_HEADER_OR_SUMMARY_ENABLED' => 'Field can either be an header field or key field',
    'JS_DETAIL_HEADER' => 'Record header',
    'JS_MAXIMUM_HEADER_FIELDS_ALLOWED' => 'Maximum %s header fields allowed',
    'JS_NAME_FIELDS_APPEAR_IN_HEADER_BY_DEFAULT' => 'Name fields appear in Header by default',
    'JS_FIELD_IS_HEADER_ENABLED_FOR_VTIGER7' => 'This Field is Header enabled for Vtiger7, It will appear in Summary View',

	'JS_DUPLICATE_HANDLING_SUCCESS_MESSAGE' => 'Successfully updated to consider selected fields in duplicates prevention',
	'JS_DUPLICATE_HANDLING_FAILURE_MESSAGE' => 'Failed to consider selected fields in duplicates prevention',
	'JS_DUPLICATE_CHECK_DISABLED' => 'Duplicate check is disabled',
);
