<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
    'Recycle Bin'                  => 'Corbeille'                 , 
    'RecycleBin'                   => 'Corbeille'                 , 
    'LBL_SELECT_MODULE'            => 'Selectionner un Module'               , 
    'LBL_EMPTY_RECYCLEBIN'         => 'La Corbeille est vide'           , 
    'LBL_RESTORE'                  => 'Restorer'                     , 
    'LBL_NO_PERMITTED_MODULES'     => 'Aucun module autorisé disponible', 
    'LBL_RECORDS_LIST'             => 'Liste des objets dans la Corbeille'            , 
    'LBL_NO_RECORDS_FOUND'         => 'Aucun enregistrement trouvé à restaurer pour le module', 
);
$jsLanguageStrings = array(
    'JS_MSG_EMPTY_RB_CONFIRMATION' => 'Vous êtes sur de vouloir définitivement supprimer tous les enregistrements de la Base de données ?', 
    'JS_LBL_RESTORE_RECORDS_CONFIRMATION' => 'Êtes-vous sûr de vouloir restaurer les enregistrements ?', 
    'JS_LBL_RESTORE_RECORD_CONFIRMATION' => 'Êtes-vous sûr de que vouloir restaurer l\'enregistrement ?',
    'JS_RESTORING_RECORD' => 'Restauration de l\'enregistrement',
    'JS_RESTORE_AND_UNTRASH_FILE_IN_DRIVE' => 'Restaurer dans Vtiger et dur',
    'JS_RESTORING_RECORDS' => 'Restauration des enregistrements',

);
