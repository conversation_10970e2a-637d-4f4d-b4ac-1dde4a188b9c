<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.0
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/
$languageStrings = array(
	'LBL_NEW'                      => 'New'                         , // TODO: Review
	'LBL_WORKFLOW'                 => 'Workflow'                    , // TODO: Review
	'LBL_CREATING_WORKFLOW'        => 'Creating WorkFlow'           , // TODO: Review
	'LBL_NEXT'                     => 'Next'                        , // TODO: Review
	'LBL_STEP_1'                   => 'Step 1'                      , // TODO: Review
	'LBL_ENTER_BASIC_DETAILS_OF_THE_WORKFLOW' => 'Enter basic details of the Workflow', // TODO: Review
	'LBL_SPECIFY_WHEN_TO_EXECUTE'  => 'Specify when to execute this Workflow', // TODO: Review
	'ON_FIRST_SAVE'                => 'Only on the first save'      , // TODO: Review
	'ONCE'                         => 'Until the first time the condition is true', // TODO: Review
	'ON_EVERY_SAVE'                => 'Every time the record is saved', // TODO: Review
	'ON_MODIFY'                    => 'Every time a record is modified', // TODO: Review
	'MANUAL'                       => 'System'                      , // TODO: Review
	'SCHEDULE_WORKFLOW'            => 'Schedule Workflow'           , // TODO: Review
	'ADD_CONDITIONS'               => 'Add Conditions'              , // TODO: Review
	'ADD_TASKS'                    => 'Add Actions'                 ,
	'LBL_EXPRESSION'               => 'Expression'                  , // TODO: Review
	'LBL_FIELD_NAME'               => 'Field'                       , // TODO: Review
	'LBL_SET_VALUE'                => 'Set Value'                   , // TODO: Review
	'LBL_USE_FIELD'                => 'Use Field'                   , // TODO: Review
	'LBL_USE_FUNCTION'             => 'Use Function'                , // TODO: Review
	'LBL_RAW_TEXT'                 => 'Raw text'                    , // TODO: Review
	'LBL_ENABLE_TO_CREATE_FILTERS' => 'Enable to create Filters'    , // TODO: Review
	'LBL_CREATED_IN_OLD_LOOK_CANNOT_BE_EDITED' => 'This workflow was created in older look. Conditions created in older look cannot be edited. You can choose to recreate the conditions, or use the existing conditions without changing them.', // TODO: Review
	'LBL_USE_EXISTING_CONDITIONS'  => 'Use existing conditions'     , // TODO: Review
	'LBL_RECREATE_CONDITIONS'      => 'Recreate Conditions'         , // TODO: Review
	'LBL_SAVE_AND_CONTINUE'        => 'Save & Continue'             , // TODO: Review
	'LBL_ACTIVE'                   => 'Active'                      , // TODO: Review
	'LBL_TASK_TYPE'                => 'Akció típusa'              ,
	'LBL_TASK_TITLE'               => 'Akció Cím'                 ,
	'LBL_ADD_TASKS_FOR_WORKFLOW'   => 'Add Action for Workflow'     ,
	'LBL_EXECUTE_TASK'             => 'Execute Action'              ,
	'LBL_SELECT_OPTIONS'           => 'Select Options'              , // TODO: Review
	'LBL_ADD_FIELD'                => 'Add Field'                   , // TODO: Review
	'LBL_ADD_TIME'                 => 'Add time'                    , // TODO: Review
	'LBL_TITLE'                    => 'Title'                       , // TODO: Review
	'LBL_PRIORITY'                 => 'Priority'                    , // TODO: Review
	'LBL_ASSIGNED_TO'              => 'Assigned to'                 , // TODO: Review
	'LBL_TIME'                     => 'Time'                        , // TODO: Review
	'LBL_DUE_DATE'                 => 'Due Date'                    , // TODO: Review
	'LBL_THE_SAME_VALUE_IS_USED_FOR_START_DATE' => 'The same value is used for the start date', // TODO: Review
	'LBL_EVENT_NAME'               => 'Event Name'                  , // TODO: Review
	'LBL_TYPE'                     => 'Type'                        , // TODO: Review
	'LBL_METHOD_NAME'              => 'Method Name'                 , // TODO: Review
	'LBL_RECEPIENTS'               => 'Recepients'                  , // TODO: Review
	'LBL_ADD_FIELDS'               => 'Add Fields'                  , // TODO: Review
	'LBL_SMS_TEXT'                 => 'Sms Text'                    , // TODO: Review
	'LBL_SET_FIELD_VALUES'         => 'Set Field Values'            , // TODO: Review
	'LBL_IN_ACTIVE'                => 'In Active'                   , // TODO: Review
	'LBL_SEND_NOTIFICATION'        => 'Send Notification'           , // TODO: Review
	'LBL_START_TIME'               => 'Start Time'                  , // TODO: Review
	'LBL_START_DATE'               => 'Start Date'                  , // TODO: Review
	'LBL_END_TIME'                 => 'End Time'                    , // TODO: Review
	'LBL_END_DATE'                 => 'End Date'                    , // TODO: Review
	'LBL_ENABLE_REPEAT'            => 'Enable Repeat'               , // TODO: Review
	'LBL_NO_METHOD_IS_AVAILABLE_FOR_THIS_MODULE' => 'No method is available for this module', // TODO: Review
	'LBL_FINISH'                   => 'Finish'                      , // TODO: Review
	'LBL_NO_TASKS_ADDED'           => 'Nem Actions'                 ,
	'LBL_CANNOT_DELETE_DEFAULT_WORKFLOW' => 'You Cannot delete default Workflow', // TODO: Review
	'LBL_MODULES_TO_CREATE_RECORD' => 'Hozzon létre egy rekordot'  ,
	'LBL_EXAMPLE_EXPRESSION'       => 'Expression'                  , // TODO: Review
	'LBL_EXAMPLE_RAWTEXT'          => 'Rawtext'                     , // TODO: Review
	'LBL_VTIGER'                   => 'Vtiger'                      , // TODO: Review
	'LBL_EXAMPLE_FIELD_NAME'       => 'Field'                       , // TODO: Review
	'LBL_NOTIFY_OWNER'             => 'notify_owner'                , // TODO: Review
	'LBL_ANNUAL_REVENUE'           => 'annual_revenue'              , // TODO: Review
	'LBL_EXPRESSION_EXAMPLE2'      => 'if mailingcountry == \'India\' then concat(firstname,\' \',lastname) else concat(lastname,\' \',firstname) end', // TODO: Review
	'LBL_FROM' => '-Tól',
	'Optional' => 'Választható',
	'LBL_ADD_TASK'                 => 'Add Action'                  ,
    'Portal Pdf Url' =>'Portal Pdf kapcsolat',
    'LBL_ADD_TEMPLATE' => 'Add Sablon',
    'LBL_LINEITEM_BLOCK_GROUP' => 'LineItems blokk Group adó',
    'LBL_LINEITEM_BLOCK_INDIVIDUAL' => 'LineItems blokk egyes adó-',
    'LBL_ADD_PDF' => 'Betenni pdf',
	
	//Translation for module
	'Calendar' => 'To Do',
	'Send Mail'					   => 'E-mail küldése',
	'Invoke Custom Function'	   => 'Hivatkozhat Egyedi funkció',
	'Create Todo'				   => 'Hozzon létre Todo',
	'Create Event'				   => 'Esemény létrehozása',
	'Update Fields'				   => 'Frissítés Fields',
	'Create Entity'                => 'Rekord létrehozása'        ,
	'SMS Task'					   => 'SMS Task',
	'Mobile Push Notification'	   => 'Mobile Push Notification',
	'LBL_ACTION_TYPE' => 'Akció típusa (Active Count)',
	'LBL_VTEmailTask' => 'E-mail',
    'LBL_VTEntityMethodTask' => 'Egyedi funkció',
    'LBL_VTCreateTodoTask' => 'Feladat',
    'LBL_VTCreateEventTask' => 'Esemény',
    'LBL_VTUpdateFieldsTask' => 'Mező frissítése',
    'LBL_VTSMSTask' => 'SMS', 
    'LBL_VTPushNotificationTask' => 'Mobile bejelentése',
    'LBL_VTCreateEntityTask' => 'Rekord létrehozása',
	'LBL_MAX_SCHEDULED_WORKFLOWS_EXCEEDED' => 'Maximális száma (%s) a jegyzékben szereplő munkafolyamatok túllépték',

  'LBL_EDITING_WORKFLOW' => 'Szerkesztési Munkafolyamat',
  'LBL_ADD_RECORD' => 'Új Munkafolyamat',
  'ON_SCHEDULE' => 'Ütemezés',
  'LBL_RUN_WORKFLOW' => 'Munkafolyamat Futtatása',
  'LBL_AT_TIME' => 'Idő',
  'LBL_HOURLY' => 'Óránkénti',
  'ENTER_FROM_EMAIL_ADDRESS' => 'Adja meg A e-mail címet',
  'LBL_DAILY' => 'Napi',
  'LBL_WEEKLY' => 'Heti',
  'LBL_ON_THESE_DAYS' => 'Ezeken a napokon',
  'LBL_MONTHLY_BY_DATE' => 'Havi Dátum szerint',
  'LBL_MONTHLY_BY_WEEKDAY' => 'Havi által Hétköznap',
  'LBL_YEARLY' => 'Éves',
  'LBL_SPECIFIC_DATE' => 'A Konkrét Dátum',
  'LBL_CHOOSE_DATE' => 'Válasszon Dátuma',
  'LBL_SELECT_MONTH_AND_DAY' => 'Válasszuk a Havi vagy a Dátum',
  'LBL_SELECTED_DATES' => 'A Kiválasztott Dátumokra',
  'LBL_EXCEEDING_MAXIMUM_LIMIT' => 'Maximális határérték túllépése',
  'LBL_NEXT_TRIGGER_TIME' => 'Következő trigger idő',
  'LBL_MESSAGE' => 'Üzenet',
  'LBL_WORKFLOW_NAME' => 'A Munkafolyamat Neve',
  'LBL_TARGET_MODULE' => 'Cél Modul',
  'LBL_WORKFLOW_TRIGGER' => 'A Munkafolyamat Trigger',
  'LBL_TRIGGER_WORKFLOW_ON' => 'A Trigger Folyamatábrája A',
  'LBL_RECORD_CREATION' => 'Bejegyzés Létrehozása',
  'LBL_RECORD_UPDATE' => 'Bejegyzés Frissítés',
  'LBL_TIME_INTERVAL' => 'Időtartam',
  'LBL_RECURRENCE' => 'Ismétlődés',
  'LBL_FIRST_TIME_CONDITION_MET' => 'Csak az első alkalommal conditons teljesülnek',
  'LBL_EVERY_TIME_CONDITION_MET' => 'Minden alkalommal, amikor conditons teljesülnek',
  'LBL_WORKFLOW_CONDITION' => 'A Munkafolyamat Állapota',
  'LBL_WORKFLOW_ACTIONS' => 'A Munkafolyamat-Műveletek',
  'LBL_DELAY_ACTION' => 'Késedelem Akció',
  'LBL_FREQUENCY' => 'Frekvencia',
  'LBL_SELECT_FIELDS' => 'A Mezők',
  'LBL_INCLUDES_CREATION' => 'Magában Foglalja A Teremtés',
  'LBL_ACTION_FOR_WORKFLOW' => 'A művelet a Munkafolyamat',
  'LBL_WORKFLOW_SEARCH' => 'Név szerinti keresés',

);
$jsLanguageStrings = array(
	'JS_STATUS_CHANGED_SUCCESSFULLY' => 'Status changed Successfully' , // TODO: Review
	'JS_TASK_DELETED_SUCCESSFULLY' => 'Akció sikeresen törölve'  ,
	'JS_SAME_FIELDS_SELECTED_MORE_THAN_ONCE' => 'Same fields selected more than once', // TODO: Review
	'JS_WORKFLOW_SAVED_SUCCESSFULLY' => 'Workflow saved successfully' , // TODO: Review
    'JS_CHECK_START_AND_END_DATE'=>'Vége Dátum és idő legyen nagyobb vagy egyenlő, mint kezdő dátuma és időpontja',

  'JS_TASK_STATUS_CHANGED' => 'Feladat állapotát módosult.',
  'JS_WORKFLOWS_STATUS_CHANGED' => 'A munkafolyamat állapota módosult.',
  'VTEmailTask' => 'E-Mail Küldése',
  'VTEntityMethodTask' => 'Adjon Meg Egyéni Funkció',
  'VTCreateTodoTask' => 'Feladat Létrehozása',
  'VTCreateEventTask' => 'Esemény Létrehozása',
  'VTUpdateFieldsTask' => 'Frissítés Mezők',
  'VTSMSTask' => 'SMS Feladat',
  'VTPushNotificationTask' => 'Mobil Push Értesítés',
  'VTCreateEntityTask' => 'Bejegyzés Létrehozása',
  'LBL_EXPRESSION_INVALID' => 'Kifejezés Érvénytelen',

);