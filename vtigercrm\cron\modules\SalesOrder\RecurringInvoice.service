<?php
/*+**********************************************************************************
 * The contents of this file are subject to the vtiger CRM Public License Version 1.1
 * ("License"); You may not use this file except in compliance with the License
 * The Original Code is:  vtiger CRM Open Source
 * The Initial Developer of the Original Code is vtiger.
 * Portions created by vtiger are Copyright (C) vtiger.
 * All Rights Reserved.
 ************************************************************************************/

require_once('include/utils/utils.php');
require_once('include/logging.php');

global $adb, $log;
$log = Logger::getLogger('RecurringInvoice');
$log->debug("invoked RecurringInvoice");

$currentDate = date('Y-m-d');
$currentDateStrTime = strtotime($currentDate);

$sql="SELECT vtiger_salesorder.salesorderid, recurring_frequency, start_period, end_period, last_recurring_date,
		 payment_duration, invoice_status FROM vtiger_salesorder
		 INNER JOIN vtiger_crmentity ON vtiger_salesorder.salesorderid = vtiger_crmentity.crmid AND vtiger_crmentity.deleted = 0
		 INNER JOIN vtiger_invoice_recurring_info ON vtiger_salesorder.salesorderid = vtiger_invoice_recurring_info.salesorderid
		 WHERE DATE_FORMAT(start_period,'%Y-%m-%d') <= ? AND DATE_FORMAT(end_period,'%Y-%m-%d') >= ?";
$result = $adb->pquery($sql, array($currentDate, $currentDate));
$no_of_salesorder = $adb->num_rows($result);

for($i=0; $i<$no_of_salesorder;$i++) {
	$salesorder_id		= $adb->query_result($result, $i, 'salesorderid');
	$start_period		= $adb->query_result($result, $i, 'start_period');
	$end_period			= $adb->query_result($result, $i, 'end_period');
	$recurring_date		= $adb->query_result($result, $i, 'last_recurring_date');
	$recurringFrequency = $adb->query_result($result, $i, 'recurring_frequency');

	if ($recurring_date == NULL  || $recurring_date == '' || $recurring_date == '0000-00-00') {
		$recurring_date = $start_period;
	}

	$endDateStrTime = strtotime($end_period);
	$recurringDateStrTime = strtotime($recurring_date);

	if($recurringDateStrTime < $currentDateStrTime) {
		$recurringDatesList = array();
		$nextRecurringDate = $validNextRecurringDate = $recurring_date;

		while(strtotime($validNextRecurringDate) <= $currentDateStrTime && $currentDateStrTime <= $endDateStrTime) {
			$recurringDatesList[]	= $validNextRecurringDate;
			$nextRecurringDatesInfo = getRecurringDate($nextRecurringDate, $recurringFrequency);

			//Updating the existing values
			$validNextRecurringDate = $nextRecurringDatesInfo['validDate'];
			$nextRecurringDate		= $nextRecurringDatesInfo['nextRecurringDate'];
		}

		if ($recurringDatesList) {
			foreach ($recurringDatesList as $recurringDate) {
				createInvoice($salesorder_id, $recurringDate);
			}
			$adb->pquery('UPDATE vtiger_invoice_recurring_info SET last_recurring_date = ? WHERE salesorderid = ?', array($validNextRecurringDate, $salesorder_id));
		}

	} elseif($recurringDateStrTime == $currentDateStrTime && $recurringDateStrTime <= $endDateStrTime) {
		createInvoice($salesorder_id, $recurringDate);

		$nextRecurringDatesInfo = getRecurringDate($recurring_date, $recurringFrequency);
		$nextRecurringDate = $nextRecurringDatesInfo['validDate'];
		$adb->pquery('UPDATE vtiger_invoice_recurring_info SET last_recurring_date = ? WHERE salesorderid = ?', array($nextRecurringDate, $salesorder_id));
	}
}

/* Function to create a new Invoice using the given Sales Order id */
function createInvoice($salesorder_id, $recurringDate = false) {
	require_once('include/utils/utils.php');
	require_once('modules/SalesOrder/SalesOrder.php');
	require_once('modules/Invoice/Invoice.php');
	require_once('modules/Users/<USER>');

	global $log, $adb;
	global $current_user;

	// Payment duration in days
	$payment_duration_values = Array(
        'net 01 day' => '1',
        'net 05 days' => '5',
        'net 07 days' => '7',
        'net 10 days' => '10',
        'net 15 days' => '15',
		'net 30 days' => '30',
		'net 45 days' => '45',
		'net 60 days' => '60'
	);

	if (!$recurringDate) {
		$recurringDate = date('Y-m-d');
	}

	if(!$current_user) {
		$current_user = Users::getActiveAdminUser();
	}
	$so_focus = CRMEntity::getInstance('SalesOrder');
	$so_focus->id = $salesorder_id;
	$so_focus->retrieve_entity_info($salesorder_id,"SalesOrder");
	foreach($so_focus->column_fields as $fieldname=>$value) {
		$so_focus->column_fields[$fieldname] = decode_html($value);
	}

	$focus = new Invoice();
	// This will only fill in the basic columns from SO to Invoice and also Update the SO id in new Invoice
	$focus = getConvertSoToInvoice($focus,$so_focus,$salesorder_id);

	// Pick up the Payment due date based on the Configuration in SO
	$payment_duration = $so_focus->column_fields['payment_duration'];
	$due_duration = $payment_duration_values[trim(strtolower($payment_duration))];

	// Cleanup focus object, to duplicate the Invoice.
	$focus->id = '';
	$focus->mode = '';
	$focus->column_fields['invoicestatus'] = $so_focus->column_fields['invoicestatus'];
	$focus->column_fields['invoicedate'] = $recurringDate;

	list($y, $m, $d) = explode('-', $recurringDate);
	$focus->column_fields['duedate'] = date('Y-m-d', mktime(0, 0, 0, $m, $d + $due_duration, $y));

	// Additional SO fields to copy -> Invoice field name mapped to equivalent SO field name
	$invoice_so_fields = Array (
		'txtAdjustment' => 'txtAdjustment',
		'hdnSubTotal' => 'hdnSubTotal',
		'hdnGrandTotal' => 'hdnGrandTotal',
		'hdnTaxType' => 'hdnTaxType',
		'hdnDiscountPercent' => 'hdnDiscountPercent',
		'hdnDiscountAmount' => 'hdnDiscountAmount',
		'hdnS_H_Amount' => 'hdnS_H_Amount',
		'assigned_user_id' => 'assigned_user_id',
		'currency_id' => 'currency_id',
		'conversion_rate' => 'conversion_rate',
		'balance' => 'hdnGrandTotal'
	);
	foreach($invoice_so_fields as $invoice_field => $so_field) {
		$focus->column_fields[$invoice_field] = $so_focus->column_fields[$so_field];
	}
	$focus->_salesorderid = $salesorder_id;
	$focus->_recurring_mode = 'recurringinvoice_from_so';
	try {
		$focus->save("Invoice");
	} catch (Exception $e) {
		//TODO - Review
	}
}

function getRecurringDate($recurringDate, $recurringFrequency) {
	$currentDate = date('Y-m-d');
	list($y, $m, $d) = explode('-', $recurringDate);

	$period = false;
	switch(strtolower($recurringFrequency)) {
		case 'daily'		:	$period = '+1 day';		break;
		case 'weekly'		:	$period = '+1 week';	break;

		case 'monthly'		:	$m = $m + 1;			break;
		case 'quarterly'	:	$m = $m + 3;			break;
		case 'every 4 months':	$m = $m + 4;			break;
		case 'half-yearly'	:	$m = $m + 6;			break;
		case 'yearly'		:	$y = $y + 1;			break;

		default				:	$period = '';
	}

	if ($period !== false) {
		$nextRecurringDate = $validNextRecurringDate = date('Y-m-d', strtotime($period, mktime(0, 0, 0, $m, $d, $y)));
	} else {

		if ($m > 12) {
			$m = $m - 12;
			$y = $y + 1;
		}
		if (strlen($m) === 1) {
			$m = "0$m";
		}

		$nextRecurringDate = $validNextRecurringDate = "$y-$m-$d";
		if (!checkdate($m, $d, $y)) {
			$validNextRecurringDate = date('Y-m-d', mktime(0, 0, 0, $m, cal_days_in_month(CAL_GREGORIAN, $m, $y), $y));
		}
	}

	return array('validDate' => $validNextRecurringDate, 'nextRecurringDate' => $nextRecurringDate);
}
